#!/usr/bin/env python3
"""
إصلاح قاعدة البيانات لمشكلة إشعارات الاشتراك
"""

import os
import sys
from datetime import datetime

def fix_notification_table():
    """إصلاح جدول إشعارات الاشتراك"""
    
    print("🔧 إصلاح جدول إشعارات الاشتراك...")
    
    # تعيين متغيرات البيئة
    os.environ['FLASK_ENV'] = 'production'
    os.environ['PRODUCTION_MODE'] = 'true'
    
    try:
        from app import app, db
        from models_new import SubscriptionNotification, User
        
        with app.app_context():
            print("✅ تم الاتصال بقاعدة البيانات")
            
            # فحص بنية الجدول
            print("🔍 فحص بنية جدول subscription_notification...")
            
            # محاولة إنشاء إشعار تجريبي
            try:
                test_user = User.query.first()
                if test_user:
                    test_notification = SubscriptionNotification(
                        user_id=test_user.id,
                        subscription_id=None,  # اختبار None
                        notification_type='test',
                        message='اختبار إشعار',
                        is_read=False,
                        sent_at=datetime.utcnow()
                    )
                    
                    db.session.add(test_notification)
                    db.session.commit()
                    
                    print("✅ تم إنشاء إشعار تجريبي بنجاح")
                    
                    # حذف الإشعار التجريبي
                    db.session.delete(test_notification)
                    db.session.commit()
                    
                    print("✅ تم حذف الإشعار التجريبي")
                    
                else:
                    print("❌ لا يوجد مستخدمين للاختبار")
                    
            except Exception as test_error:
                print(f"❌ خطأ في الاختبار: {str(test_error)}")
                db.session.rollback()
                
                # محاولة إصلاح الجدول
                print("🔄 محاولة إصلاح الجدول...")
                
                try:
                    # إعادة إنشاء الجدول
                    db.engine.execute("DROP TABLE IF EXISTS subscription_notification")
                    
                    # إنشاء الجدول من جديد
                    SubscriptionNotification.__table__.create(db.engine)
                    
                    print("✅ تم إعادة إنشاء الجدول")
                    
                except Exception as recreate_error:
                    print(f"❌ خطأ في إعادة إنشاء الجدول: {str(recreate_error)}")
                    return False
            
            # فحص الإشعارات الموجودة
            notifications_count = SubscriptionNotification.query.count()
            print(f"📊 عدد الإشعارات الموجودة: {notifications_count}")
            
            # فحص الإشعارات مع subscription_id = None
            null_subscription_notifications = SubscriptionNotification.query.filter_by(subscription_id=None).count()
            print(f"📊 إشعارات بدون subscription_id: {null_subscription_notifications}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {str(e)}")
        import traceback
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

def test_extend_subscription():
    """اختبار تمديد الاشتراك"""
    
    print("\n🧪 اختبار تمديد الاشتراك...")
    
    try:
        from app import app, db
        from models_new import User, Role
        from subscription_manager import subscription_manager
        
        with app.app_context():
            # البحث عن مستخدم للاختبار
            test_user = User.query.filter(
                User.role.in_([Role.TEACHER, Role.INSPECTOR])
            ).first()
            
            if not test_user:
                print("❌ لا يوجد مستخدم مناسب للاختبار")
                return False
            
            # البحث عن مستخدم أدمن
            admin_user = User.query.filter_by(role=Role.ADMIN).first()
            
            if not admin_user:
                print("❌ لا يوجد مستخدم أدمن للاختبار")
                return False
            
            print(f"🔄 اختبار تمديد اشتراك المستخدم: {test_user.username}")
            print(f"👤 بواسطة الأدمن: {admin_user.username}")
            
            # تمديد الاشتراك
            result = subscription_manager.extend_subscription_days(
                user_id=test_user.id,
                days_to_add=1,
                admin_user_id=admin_user.id,
                reason="اختبار النظام"
            )
            
            if result['success']:
                print("✅ تم تمديد الاشتراك بنجاح!")
                print(f"📋 النتيجة: {result}")
                return True
            else:
                print(f"❌ فشل في تمديد الاشتراك: {result.get('error', 'خطأ غير معروف')}")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في اختبار تمديد الاشتراك: {str(e)}")
        import traceback
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح مشكلة إشعارات الاشتراك - Ta9affi")
    print("=" * 60)
    
    # إصلاح قاعدة البيانات
    if fix_notification_table():
        print("\n✅ تم إصلاح قاعدة البيانات بنجاح")
        
        # اختبار تمديد الاشتراك
        if test_extend_subscription():
            print("\n🎉 تم حل المشكلة بنجاح!")
        else:
            print("\n❌ لا تزال هناك مشكلة في تمديد الاشتراك")
    else:
        print("\n❌ فشل في إصلاح قاعدة البيانات")
    
    print("\n📋 ملاحظات:")
    print("1. تأكد من إعادة النشر في dokploy")
    print("2. اختبر تمديد الاشتراك من لوحة التحكم")
    print("3. راجع logs الخادم للتأكد من عدم وجود أخطاء")

if __name__ == '__main__':
    main()
