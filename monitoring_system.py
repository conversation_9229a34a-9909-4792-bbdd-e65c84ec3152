#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الأداء والسجلات لتطبيق Ta9affi
يوفر مراقبة شاملة للأداء والأخطاء والإحصائيات
"""

import os
import time
import psutil
import logging
import json
from datetime import datetime, timedelta
from functools import wraps
from flask import request, g, current_app
from sqlalchemy import text
from models_new import db, User, UserSession
from redis_manager import redis_manager
import threading
import queue

class PerformanceMonitor:
    """مراقب الأداء الرئيسي"""
    
    def __init__(self, app=None):
        self.app = app
        self.metrics_queue = queue.Queue()
        self.is_monitoring = False
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة نظام المراقبة مع التطبيق"""
        self.app = app
        
        # إعداد السجلات
        self.setup_logging()
        
        # تسجيل معالجات الأحداث
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        app.teardown_appcontext(self.teardown_request)
        
        # بدء خيط المراقبة
        self.start_monitoring()
        
        logging.info("✅ تم تهيئة نظام مراقبة الأداء")
    
    def setup_logging(self):
        """إعداد نظام السجلات المتقدم"""
        
        # إنشاء مجلد السجلات
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # إعداد تنسيق السجلات
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # سجل الأداء
        performance_handler = logging.FileHandler(f'{log_dir}/performance.log')
        performance_handler.setFormatter(formatter)
        performance_handler.setLevel(logging.INFO)
        
        # سجل الأخطاء
        error_handler = logging.FileHandler(f'{log_dir}/errors.log')
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        
        # سجل قاعدة البيانات
        db_handler = logging.FileHandler(f'{log_dir}/database.log')
        db_handler.setFormatter(formatter)
        db_handler.setLevel(logging.WARNING)
        
        # إعداد loggers مخصصة
        performance_logger = logging.getLogger('performance')
        performance_logger.addHandler(performance_handler)
        performance_logger.setLevel(logging.INFO)
        
        error_logger = logging.getLogger('errors')
        error_logger.addHandler(error_handler)
        error_logger.setLevel(logging.ERROR)
        
        db_logger = logging.getLogger('database')
        db_logger.addHandler(db_handler)
        db_logger.setLevel(logging.WARNING)
    
    def before_request(self):
        """معالج ما قبل الطلب"""
        g.start_time = time.time()
        g.request_id = f"{int(time.time())}-{id(request)}"
        
        # تسجيل بداية الطلب
        logging.getLogger('performance').info(
            f"REQUEST_START - {g.request_id} - {request.method} {request.path} - IP: {request.remote_addr}"
        )
    
    def after_request(self, response):
        """معالج ما بعد الطلب"""
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            
            # تسجيل انتهاء الطلب
            logging.getLogger('performance').info(
                f"REQUEST_END - {g.request_id} - {response.status_code} - {duration:.3f}s"
            )
            
            # إضافة معلومات الأداء للرد
            response.headers['X-Response-Time'] = f"{duration:.3f}s"
            response.headers['X-Request-ID'] = g.request_id
            
            # تسجيل الطلبات البطيئة
            if duration > 2.0:  # أكثر من ثانيتين
                logging.getLogger('performance').warning(
                    f"SLOW_REQUEST - {g.request_id} - {duration:.3f}s - {request.method} {request.path}"
                )
            
            # إضافة المقاييس للطابور
            self.add_metric({
                'type': 'request',
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'duration': duration,
                'timestamp': datetime.utcnow().isoformat(),
                'ip': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')[:100]
            })
        
        return response
    
    def teardown_request(self, exception):
        """معالج تنظيف الطلب"""
        if exception:
            logging.getLogger('errors').error(
                f"REQUEST_ERROR - {getattr(g, 'request_id', 'unknown')} - {str(exception)}",
                exc_info=True
            )
    
    def add_metric(self, metric):
        """إضافة مقياس للطابور"""
        try:
            self.metrics_queue.put_nowait(metric)
        except queue.Full:
            # إذا امتلأ الطابور، تجاهل المقياس
            pass
    
    def start_monitoring(self):
        """بدء خيط المراقبة"""
        if not self.is_monitoring:
            self.is_monitoring = True
            monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            monitor_thread.start()
    
    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.is_monitoring:
            try:
                # معالجة المقاييس
                self._process_metrics()
                
                # جمع مقاييس النظام
                self._collect_system_metrics()
                
                # انتظار 30 ثانية
                time.sleep(30)
                
            except Exception as e:
                logging.getLogger('errors').error(f"Monitoring loop error: {str(e)}")
                time.sleep(60)  # انتظار أطول في حالة الخطأ
    
    def _process_metrics(self):
        """معالجة المقاييس من الطابور"""
        metrics_batch = []
        
        # جمع المقاييس من الطابور
        while not self.metrics_queue.empty() and len(metrics_batch) < 100:
            try:
                metric = self.metrics_queue.get_nowait()
                metrics_batch.append(metric)
            except queue.Empty:
                break
        
        if metrics_batch:
            # حفظ المقاييس في Redis
            self._save_metrics_to_redis(metrics_batch)
    
    def _save_metrics_to_redis(self, metrics):
        """حفظ المقاييس في Redis"""
        if redis_manager.is_available():
            try:
                # حفظ المقاييس مع انتهاء صلاحية
                for metric in metrics:
                    key = f"metrics:{metric['type']}:{int(time.time())}"
                    redis_manager.redis_client.setex(key, 86400, json.dumps(metric))  # 24 ساعة
                
            except Exception as e:
                logging.getLogger('errors').error(f"Error saving metrics to Redis: {str(e)}")
    
    def _collect_system_metrics(self):
        """جمع مقاييس النظام"""
        try:
            # مقاييس النظام
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # مقاييس قاعدة البيانات
            db_metrics = self._get_database_metrics()
            
            # مقاييس Redis
            redis_metrics = self._get_redis_metrics()
            
            # مقاييس التطبيق
            app_metrics = self._get_application_metrics()
            
            # دمج جميع المقاييس
            system_metrics = {
                'type': 'system',
                'timestamp': datetime.utcnow().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': memory.used / (1024**3),
                'memory_total_gb': memory.total / (1024**3),
                'disk_percent': disk.percent,
                'disk_used_gb': disk.used / (1024**3),
                'disk_total_gb': disk.total / (1024**3),
                'database': db_metrics,
                'redis': redis_metrics,
                'application': app_metrics
            }
            
            # حفظ المقاييس
            self.add_metric(system_metrics)
            
            # تسجيل تحذيرات إذا لزم الأمر
            if cpu_percent > 80:
                logging.getLogger('performance').warning(f"High CPU usage: {cpu_percent}%")
            
            if memory.percent > 85:
                logging.getLogger('performance').warning(f"High memory usage: {memory.percent}%")
            
            if disk.percent > 90:
                logging.getLogger('performance').warning(f"High disk usage: {disk.percent}%")
            
        except Exception as e:
            logging.getLogger('errors').error(f"Error collecting system metrics: {str(e)}")
    
    def _get_database_metrics(self):
        """الحصول على مقاييس قاعدة البيانات"""
        try:
            with db.engine.connect() as conn:
                # عدد الاتصالات النشطة
                result = conn.execute(text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"))
                active_connections = result.scalar()
                
                # حجم قاعدة البيانات
                result = conn.execute(text("SELECT pg_database_size(current_database())"))
                db_size = result.scalar()
                
                # عدد الاستعلامات البطيئة
                result = conn.execute(text("""
                    SELECT count(*) FROM pg_stat_statements 
                    WHERE mean_time > 1000
                """))
                slow_queries = result.scalar() or 0
                
                return {
                    'active_connections': active_connections,
                    'database_size_mb': db_size / (1024*1024) if db_size else 0,
                    'slow_queries_count': slow_queries
                }
        except Exception as e:
            logging.getLogger('database').error(f"Error getting database metrics: {str(e)}")
            return {}
    
    def _get_redis_metrics(self):
        """الحصول على مقاييس Redis"""
        if redis_manager.is_available():
            try:
                info = redis_manager.redis_client.info()
                return {
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory_mb': info.get('used_memory', 0) / (1024*1024),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0)
                }
            except Exception as e:
                logging.getLogger('errors').error(f"Error getting Redis metrics: {str(e)}")
                return {}
        return {}
    
    def _get_application_metrics(self):
        """الحصول على مقاييس التطبيق"""
        try:
            # عدد المستخدمين المتصلين
            online_users = UserSession.get_online_users_count()
            
            # إجمالي المستخدمين
            total_users = User.query.count()
            
            # المستخدمين النشطين
            active_users = User.query.filter_by(_is_active=True).count()
            
            return {
                'online_users': online_users,
                'total_users': total_users,
                'active_users': active_users,
                'inactive_users': total_users - active_users
            }
        except Exception as e:
            logging.getLogger('errors').error(f"Error getting application metrics: {str(e)}")
            return {}
    
    def get_performance_summary(self, hours=24):
        """الحصول على ملخص الأداء"""
        if not redis_manager.is_available():
            return {}
        
        try:
            # البحث عن المقاييس في الفترة المحددة
            end_time = int(time.time())
            start_time = end_time - (hours * 3600)
            
            metrics = []
            for timestamp in range(start_time, end_time, 300):  # كل 5 دقائق
                key_pattern = f"metrics:*:{timestamp}"
                keys = redis_manager.redis_client.keys(key_pattern)
                
                for key in keys:
                    try:
                        metric_data = redis_manager.redis_client.get(key)
                        if metric_data:
                            metrics.append(json.loads(metric_data))
                    except:
                        continue
            
            # تحليل المقاييس
            return self._analyze_metrics(metrics)
            
        except Exception as e:
            logging.getLogger('errors').error(f"Error getting performance summary: {str(e)}")
            return {}
    
    def _analyze_metrics(self, metrics):
        """تحليل المقاييس"""
        if not metrics:
            return {}
        
        request_metrics = [m for m in metrics if m.get('type') == 'request']
        system_metrics = [m for m in metrics if m.get('type') == 'system']
        
        analysis = {
            'total_requests': len(request_metrics),
            'avg_response_time': 0,
            'slow_requests': 0,
            'error_rate': 0,
            'avg_cpu_usage': 0,
            'avg_memory_usage': 0,
            'peak_online_users': 0
        }
        
        if request_metrics:
            # تحليل الطلبات
            durations = [m.get('duration', 0) for m in request_metrics]
            analysis['avg_response_time'] = sum(durations) / len(durations)
            analysis['slow_requests'] = len([d for d in durations if d > 2.0])
            
            error_requests = [m for m in request_metrics if m.get('status_code', 200) >= 400]
            analysis['error_rate'] = (len(error_requests) / len(request_metrics)) * 100
        
        if system_metrics:
            # تحليل النظام
            cpu_values = [m.get('cpu_percent', 0) for m in system_metrics]
            memory_values = [m.get('memory_percent', 0) for m in system_metrics]
            online_users = [m.get('application', {}).get('online_users', 0) for m in system_metrics]
            
            if cpu_values:
                analysis['avg_cpu_usage'] = sum(cpu_values) / len(cpu_values)
            if memory_values:
                analysis['avg_memory_usage'] = sum(memory_values) / len(memory_values)
            if online_users:
                analysis['peak_online_users'] = max(online_users)
        
        return analysis

# إنشاء مثيل عام لمراقب الأداء
performance_monitor = PerformanceMonitor()

# ديكوريتر لمراقبة الدوال
def monitor_performance(func_name=None):
    """ديكوريتر لمراقبة أداء الدوال"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = func_name or func.__name__
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # تسجيل الأداء
                logging.getLogger('performance').info(
                    f"FUNCTION - {function_name} - {duration:.3f}s"
                )
                
                # تحذير للدوال البطيئة
                if duration > 1.0:
                    logging.getLogger('performance').warning(
                        f"SLOW_FUNCTION - {function_name} - {duration:.3f}s"
                    )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logging.getLogger('errors').error(
                    f"FUNCTION_ERROR - {function_name} - {duration:.3f}s - {str(e)}"
                )
                raise
        
        return wrapper
    return decorator
