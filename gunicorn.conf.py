# إعدادات Gunicorn للإنتاج - Ta9affi
# محسن للأداء العالي مع 8 workers و gevent للتزامن الأمثل
import multiprocessing
import os

# إصلاح مشكلة SSL مع gevent
def when_ready(server):
    """عندما يصبح الخادم جاهز - تطبيق SSL patch"""
    try:
        import gevent.monkey
        gevent.monkey.patch_all()
        server.log.info("✅ تم تطبيق gevent SSL patch بنجاح")
    except ImportError:
        server.log.warning("⚠️ gevent غير متاح - تشغيل بدون patch")

    server.log.info("🚀 Ta9affi Server is ready on %s", server.address)

# إعدادات الخادم
bind = "0.0.0.0:8000"
backlog = 2048

# عدد العمال - محسن لاستغلال جميع الـ 8 cores
workers = 8  # استغلال جميع الـ 8 vCPU cores
worker_class = "gevent"  # عودة إلى gevent للأداء العالي والتزامن
worker_connections = 1000  # اتصالات متزامنة لكل worker

# إعدادات المهلة الزمنية - محسنة لـ gevent worker
timeout = 120  # زيادة timeout لـ gevent worker
keepalive = 5  # زيادة keepalive للاستقرار

# إعدادات الأمان - محسنة للأداء العالي
max_requests = 2000  # زيادة عدد الطلبات لكل worker
max_requests_jitter = 200  # زيادة التنويع العشوائي

# السجلات
accesslog = "-"
errorlog = "-"
loglevel = "info"

# إعدادات الأداء
preload_app = True

# متغيرات البيئة
raw_env = [
    'FLASK_ENV=production',
    'PYTHONPATH=/app',
]

# دوال التحكم


def on_starting(server):
    server.log.info("🎯 Ta9affi High-Performance Production Server Starting...")
    server.log.info("📊 Workers: %d, Class: %s, Connections per worker: %d", workers, worker_class, worker_connections)
    server.log.info("🚀 Optimized for high concurrency with 8 vCPU cores")

def on_exit(server):
    server.log.info("👋 Ta9affi Server Shutting Down...")

# تحسينات Docker
if os.path.exists('/.dockerenv'):
    bind = "0.0.0.0:8000"  # تصحيح المنفذ ليتطابق مع nginx ودokploy
    tmp_upload_dir = "/tmp"
    worker_tmp_dir = "/tmp"
    print("🐳 Running in Docker container - optimizations applied")
    print(f"🔌 Gunicorn binding to: {bind}")








