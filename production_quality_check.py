#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جودة المشروع قبل الإنتاج - Ta9affi
يتحقق من جاهزية المشروع للنشر في الإنتاج
"""

import os
import sys
import re
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple

class ProductionQualityChecker:
    """فاحص جودة الإنتاج"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.issues = []
        self.warnings = []
        self.passed_checks = []
        
    def log_issue(self, message: str, severity: str = "error"):
        """تسجيل مشكلة"""
        if severity == "error":
            self.issues.append(f"❌ {message}")
        elif severity == "warning":
            self.warnings.append(f"⚠️ {message}")
        else:
            self.passed_checks.append(f"✅ {message}")
    
    def check_required_files(self) -> bool:
        """فحص وجود الملفات المطلوبة"""
        print("🔍 فحص الملفات المطلوبة...")
        
        required_files = [
            'app_postgresql.py',
            'requirements.txt',
            'docker-compose.production.yml',
            'Dockerfile.production',
            'deploy.sh',
            'gunicorn_config.py',
            '.env.production.example',
            'README.md',
            'LICENSE'
        ]
        
        missing_files = []
        for file in required_files:
            if not (self.project_root / file).exists():
                missing_files.append(file)
        
        if missing_files:
            self.log_issue(f"ملفات مطلوبة مفقودة: {', '.join(missing_files)}")
            return False
        else:
            self.log_issue("جميع الملفات المطلوبة موجودة", "success")
            return True
    
    def check_sensitive_files(self) -> bool:
        """فحص وجود ملفات حساسة"""
        print("🔒 فحص الملفات الحساسة...")
        
        sensitive_patterns = [
            '.env',
            '.env.local',
            '*.key',
            '*.pem',
            '*.p12',
            '*.pfx',
            'secrets.txt',
            'passwords.txt',
            'config.local.py'
        ]
        
        found_sensitive = []
        for pattern in sensitive_patterns:
            files = list(self.project_root.glob(pattern))
            if files:
                found_sensitive.extend([f.name for f in files])
        
        if found_sensitive:
            self.log_issue(f"ملفات حساسة موجودة: {', '.join(found_sensitive)}", "warning")
            return False
        else:
            self.log_issue("لا توجد ملفات حساسة", "success")
            return True
    
    def check_development_files(self) -> bool:
        """فحص وجود ملفات التطوير"""
        print("🧹 فحص ملفات التطوير...")
        
        dev_files = [
            'run_local.sh',
            'docker-compose.local.yml',
            'Dockerfile.local',
            '.env.local',
            'README_LOCAL.md',
            'cleanup_for_production.sh'
        ]
        
        found_dev_files = []
        for file in dev_files:
            if (self.project_root / file).exists():
                found_dev_files.append(file)
        
        if found_dev_files:
            self.log_issue(f"ملفات تطوير موجودة: {', '.join(found_dev_files)}", "warning")
            return False
        else:
            self.log_issue("لا توجد ملفات تطوير", "success")
            return True
    
    def check_python_syntax(self) -> bool:
        """فحص صحة بناء ملفات Python"""
        print("🐍 فحص صحة بناء ملفات Python...")
        
        python_files = list(self.project_root.glob("*.py"))
        syntax_errors = []
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    compile(f.read(), py_file, 'exec')
            except SyntaxError as e:
                syntax_errors.append(f"{py_file}: {e}")
            except Exception as e:
                syntax_errors.append(f"{py_file}: {e}")
        
        if syntax_errors:
            self.log_issue(f"أخطاء في بناء Python: {'; '.join(syntax_errors)}")
            return False
        else:
            self.log_issue(f"جميع ملفات Python ({len(python_files)}) صحيحة", "success")
            return True
    
    def check_requirements(self) -> bool:
        """فحص ملف requirements.txt"""
        print("📦 فحص ملف requirements.txt...")
        
        req_file = self.project_root / 'requirements.txt'
        if not req_file.exists():
            self.log_issue("ملف requirements.txt غير موجود")
            return False
        
        try:
            with open(req_file, 'r') as f:
                requirements = f.read()
            
            # فحص وجود المكتبات الأساسية
            essential_packages = [
                'Flask',
                'psycopg2-binary',
                'redis',
                'gunicorn',
                'SQLAlchemy'
            ]
            
            missing_packages = []
            for package in essential_packages:
                if package not in requirements:
                    missing_packages.append(package)
            
            if missing_packages:
                self.log_issue(f"مكتبات أساسية مفقودة: {', '.join(missing_packages)}")
                return False
            
            # فحص وجود مكتبات تطوير
            dev_packages = ['pytest', 'black', 'flake8', 'flask-debugtoolbar']
            found_dev_packages = []
            for package in dev_packages:
                if package in requirements:
                    found_dev_packages.append(package)
            
            if found_dev_packages:
                self.log_issue(f"مكتبات تطوير في requirements.txt: {', '.join(found_dev_packages)}", "warning")
            
            self.log_issue("ملف requirements.txt صحيح", "success")
            return True
            
        except Exception as e:
            self.log_issue(f"خطأ في قراءة requirements.txt: {e}")
            return False
    
    def check_docker_files(self) -> bool:
        """فحص ملفات Docker"""
        print("🐳 فحص ملفات Docker...")
        
        docker_files = [
            'Dockerfile.production',
            'docker-compose.production.yml'
        ]
        
        for docker_file in docker_files:
            file_path = self.project_root / docker_file
            if not file_path.exists():
                self.log_issue(f"ملف Docker مفقود: {docker_file}")
                return False
            
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # فحص أساسي للمحتوى
                if docker_file.endswith('.yml'):
                    if 'version:' not in content:
                        self.log_issue(f"ملف {docker_file} لا يحتوي على version")
                        return False
                elif docker_file.startswith('Dockerfile'):
                    if 'FROM' not in content:
                        self.log_issue(f"ملف {docker_file} لا يحتوي على FROM")
                        return False
                
            except Exception as e:
                self.log_issue(f"خطأ في قراءة {docker_file}: {e}")
                return False
        
        self.log_issue("ملفات Docker صحيحة", "success")
        return True
    
    def check_security_configurations(self) -> bool:
        """فحص إعدادات الأمان"""
        print("🛡️ فحص إعدادات الأمان...")
        
        # فحص ملف nginx.conf
        nginx_conf = self.project_root / 'nginx' / 'nginx.conf'
        if nginx_conf.exists():
            try:
                with open(nginx_conf, 'r') as f:
                    nginx_content = f.read()
                
                security_headers = [
                    'X-Frame-Options',
                    'X-XSS-Protection',
                    'X-Content-Type-Options',
                    'Strict-Transport-Security'
                ]
                
                missing_headers = []
                for header in security_headers:
                    if header not in nginx_content:
                        missing_headers.append(header)
                
                if missing_headers:
                    self.log_issue(f"Headers أمنية مفقودة في Nginx: {', '.join(missing_headers)}", "warning")
                else:
                    self.log_issue("إعدادات Nginx الأمنية موجودة", "success")
                    
            except Exception as e:
                self.log_issue(f"خطأ في قراءة nginx.conf: {e}", "warning")
        
        # فحص إعدادات Flask الأمنية في الكود
        app_file = self.project_root / 'app_postgresql.py'
        if app_file.exists():
            try:
                with open(app_file, 'r') as f:
                    app_content = f.read()
                
                security_features = [
                    'CSRF',
                    'SECRET_KEY',
                    'Talisman',
                    'Limiter'
                ]
                
                missing_features = []
                for feature in security_features:
                    if feature not in app_content:
                        missing_features.append(feature)
                
                if missing_features:
                    self.log_issue(f"ميزات أمنية مفقودة: {', '.join(missing_features)}", "warning")
                else:
                    self.log_issue("ميزات الأمان موجودة في التطبيق", "success")
                    
            except Exception as e:
                self.log_issue(f"خطأ في قراءة app_postgresql.py: {e}")
        
        return True
    
    def check_environment_template(self) -> bool:
        """فحص قالب ملف البيئة"""
        print("⚙️ فحص قالب ملف البيئة...")
        
        env_template = self.project_root / '.env.production.example'
        if not env_template.exists():
            self.log_issue("ملف .env.production.example غير موجود")
            return False
        
        try:
            with open(env_template, 'r') as f:
                env_content = f.read()
            
            required_vars = [
                'SECRET_KEY',
                'DATABASE_URL',
                'REDIS_URL',
                'FLASK_ENV',
                'DB_PASSWORD'
            ]
            
            missing_vars = []
            for var in required_vars:
                if var not in env_content:
                    missing_vars.append(var)
            
            if missing_vars:
                self.log_issue(f"متغيرات بيئة مطلوبة مفقودة: {', '.join(missing_vars)}")
                return False
            
            # فحص وجود قيم افتراضية غير آمنة
            unsafe_values = [
                'your-secret-key',
                'password123',
                'admin123',
                'change-this'
            ]
            
            found_unsafe = []
            for unsafe in unsafe_values:
                if unsafe in env_content.lower():
                    found_unsafe.append(unsafe)
            
            if found_unsafe:
                self.log_issue(f"قيم افتراضية غير آمنة: {', '.join(found_unsafe)}", "warning")
            
            self.log_issue("قالب ملف البيئة صحيح", "success")
            return True
            
        except Exception as e:
            self.log_issue(f"خطأ في قراءة .env.production.example: {e}")
            return False
    
    def check_static_files(self) -> bool:
        """فحص الملفات الثابتة"""
        print("📁 فحص الملفات الثابتة...")
        
        static_dir = self.project_root / 'static'
        if not static_dir.exists():
            self.log_issue("مجلد static غير موجود", "warning")
            return False
        
        # فحص وجود ملفات CSS و JS أساسية
        css_files = list(static_dir.glob("**/*.css"))
        js_files = list(static_dir.glob("**/*.js"))
        
        if not css_files:
            self.log_issue("لا توجد ملفات CSS", "warning")
        
        if not js_files:
            self.log_issue("لا توجد ملفات JavaScript", "warning")
        
        self.log_issue(f"الملفات الثابتة: {len(css_files)} CSS, {len(js_files)} JS", "success")
        return True
    
    def check_database_migrations(self) -> bool:
        """فحص ملفات migrations قاعدة البيانات"""
        print("🗄️ فحص ملفات migrations...")
        
        migrations_dir = self.project_root / 'migrations'
        if migrations_dir.exists():
            migration_files = list(migrations_dir.glob("**/*.py"))
            self.log_issue(f"ملفات migrations موجودة: {len(migration_files)}", "success")
        else:
            self.log_issue("مجلد migrations غير موجود", "warning")
        
        return True
    
    def run_all_checks(self) -> Dict:
        """تشغيل جميع الفحوصات"""
        print("🔍 بدء فحص جودة الإنتاج لـ Ta9affi")
        print("=" * 50)
        
        checks = [
            self.check_required_files,
            self.check_sensitive_files,
            self.check_development_files,
            self.check_python_syntax,
            self.check_requirements,
            self.check_docker_files,
            self.check_security_configurations,
            self.check_environment_template,
            self.check_static_files,
            self.check_database_migrations
        ]
        
        passed = 0
        total = len(checks)
        
        for check in checks:
            try:
                if check():
                    passed += 1
            except Exception as e:
                self.log_issue(f"خطأ في الفحص {check.__name__}: {e}")
        
        print("\n" + "=" * 50)
        print("📊 ملخص فحص الجودة")
        print("=" * 50)
        
        # عرض النتائج
        if self.passed_checks:
            print("\n✅ الفحوصات الناجحة:")
            for check in self.passed_checks:
                print(f"  {check}")
        
        if self.warnings:
            print("\n⚠️ التحذيرات:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.issues:
            print("\n❌ المشاكل:")
            for issue in self.issues:
                print(f"  {issue}")
        
        # النتيجة النهائية
        score = (passed / total) * 100
        print(f"\n📈 النتيجة: {passed}/{total} ({score:.1f}%)")
        
        if score >= 90:
            print("🎉 المشروع جاهز للإنتاج!")
            status = "ready"
        elif score >= 70:
            print("⚠️ المشروع يحتاج تحسينات قبل الإنتاج")
            status = "needs_improvement"
        else:
            print("❌ المشروع غير جاهز للإنتاج")
            status = "not_ready"
        
        return {
            'status': status,
            'score': score,
            'passed': passed,
            'total': total,
            'issues': len(self.issues),
            'warnings': len(self.warnings),
            'passed_checks': len(self.passed_checks)
        }

def main():
    """الدالة الرئيسية"""
    checker = ProductionQualityChecker()
    result = checker.run_all_checks()
    
    # إنشاء تقرير JSON
    report_file = Path('production_quality_report.json')
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 تم حفظ التقرير في: {report_file}")
    
    # إرجاع كود الخروج المناسب
    if result['status'] == 'ready':
        sys.exit(0)
    elif result['status'] == 'needs_improvement':
        sys.exit(1)
    else:
        sys.exit(2)

if __name__ == "__main__":
    main()
