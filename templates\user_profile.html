{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ user.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-circle me-2 text-primary"></i>
                        الملف الشخصي
                    </h2>
                    <p class="text-muted mb-0">عرض تفاصيل المستخدم</p>
                </div>
                <div>
                    <a href="{{ url_for('users_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات المستخدم الأساسية -->
    <div class="row">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <!-- صورة المستخدم -->
                    <div class="mb-3">
                        {% if user.role == 'admin' %}
                        <i class="fas fa-user-shield fa-5x text-danger"></i>
                        {% elif user.role == 'user_manager' %}
                        <i class="fas fa-users-cog fa-5x text-info"></i>
                        {% elif user.role == 'inspector' %}
                        <i class="fas fa-user-tie fa-5x text-warning"></i>
                        {% elif user.role == 'teacher' %}
                        <i class="fas fa-chalkboard-teacher fa-5x text-success"></i>
                        {% else %}
                        <i class="fas fa-user fa-5x text-secondary"></i>
                        {% endif %}
                    </div>

                    <!-- اسم المستخدم -->
                    <h4 class="card-title mb-1">{{ user.username }}</h4>

                    <!-- الدور -->
                    <span class="badge 
                        {% if user.role == 'admin' %}bg-danger
                        {% elif user.role == 'user_manager' %}bg-info
                        {% elif user.role == 'inspector' %}bg-warning
                        {% elif user.role == 'teacher' %}bg-success
                        {% else %}bg-secondary{% endif %} fs-6 mb-3">
                        {% if user.role == 'admin' %}إدارة
                        {% elif user.role == 'user_manager' %}مدير المستخدمين
                        {% elif user.role == 'inspector' %}مفتش
                        {% elif user.role == 'teacher' %}أستاذ
                        {% else %}{{ user.role }}{% endif %}
                    </span>

                    <!-- حالة الحساب -->
                    <div class="mt-3">
                        {% if user.is_active %}
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-check-circle me-1"></i>
                            حساب مفعل
                        </span>
                        {% else %}
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-times-circle me-1"></i>
                            حساب معطل
                        </span>
                        {% endif %}
                    </div>

                    <!-- أزرار الإجراءات -->
                    {% if (user.role != 'admin') and ((current_user.role == 'admin') or (current_user.role ==
                    'user_manager' and user.role in ['teacher', 'inspector'])) %}
                    <div class="mt-4">
                        {% if user.is_active %}
                        <form method="POST" action="{{ url_for('user_manager_deactivate_user', user_id=user.id) }}"
                            style="display: inline;">
                            <button type="submit" class="btn btn-outline-danger btn-sm"
                                onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')">
                                <i class="fas fa-user-times me-1"></i>
                                تعطيل الحساب
                            </button>
                        </form>
                        {% else %}
                        <form method="POST" action="{{ url_for('user_manager_activate_user', user_id=user.id) }}"
                            style="display: inline;">
                            <button type="submit" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-user-check me-1"></i>
                                تفعيل الحساب
                            </button>
                        </form>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <!-- تفاصيل الاتصال -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-address-card me-2"></i>
                        معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                                <div class="form-control-plaintext">
                                    {% if current_user.role == 'admin' or
                                    (current_user.role == 'user_manager' and user.role != 'admin') or
                                    current_user.id == user.id %}
                                    {{ user.email }}
                                    {% else %}
                                    {{ user.masked_email }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </label>
                                <div class="form-control-plaintext">
                                    {% if user.phone_number %}
                                    {% if current_user.role == 'admin' or
                                    (current_user.role == 'user_manager' and user.role != 'admin') or
                                    current_user.id == user.id %}
                                    <span class="phone-number">{{ user.formatted_phone }}</span>
                                    {% else %}
                                    <span class="phone-number">{{ user.masked_phone }}</span>
                                    {% endif %}
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    الولاية
                                </label>
                                <div class="form-control-plaintext">
                                    {% if user.wilaya_code %}
                                    {{ user.wilaya_name }}
                                    {% else %}
                                    <span class="text-muted">غير محددة</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    تاريخ التسجيل
                                </label>
                                <div class="form-control-plaintext">
                                    {{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'غير محدد' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الاشتراك -->
            {% if user.role in ['teacher', 'inspector'] %}
            <div class="card border-0 shadow-sm mb-4">
                <div
                    class="card-header {% if user.has_active_subscription %}bg-success{% elif user.subscription_status == 'trial' %}bg-warning{% else %}bg-danger{% endif %} text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-crown me-2"></i>
                        معلومات الاشتراك
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            {% if user.subscription_status == 'active' %}
                            {% set active_sub = user.subscriptions|selectattr('is_active', 'equalto', True)|first %}
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-check-circle me-1"></i>
                                    حالة الاشتراك
                                </label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check-circle me-1"></i>
                                        اشتراك نشط
                                    </span>
                                </div>
                            </div>
                            {% if active_sub %}
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-box me-1"></i>
                                    نوع الباقة
                                </label>
                                <div class="form-control-plaintext">{{ active_sub.plan.name }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-calendar-times me-1"></i>
                                    تاريخ الانتهاء
                                </label>
                                <div class="form-control-plaintext">{{ active_sub.end_date.strftime('%Y-%m-%d') }}</div>
                            </div>
                            {% endif %}
                            {% elif user.subscription_status == 'trial' %}
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    حالة الاشتراك
                                </label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-warning fs-6">
                                        <i class="fas fa-clock me-1"></i>
                                        فترة تجريبية
                                    </span>
                                </div>
                            </div>
                            {% if user.free_trial_end %}
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-calendar-times me-1"></i>
                                    انتهاء الفترة التجريبية
                                </label>
                                <div class="form-control-plaintext">{{ user.free_trial_end.strftime('%Y-%m-%d') }}</div>
                            </div>
                            {% endif %}
                            {% else %}
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-times-circle me-1"></i>
                                    حالة الاشتراك
                                </label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-times-circle me-1"></i>
                                        منتهي الصلاحية
                                    </span>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if user.subscription_status == 'active' %}
                            {% set active_sub = user.subscriptions|selectattr('is_active', 'equalto', True)|first %}
                            {% if active_sub %}
                            <div class="text-center">
                                <div class="display-4 fw-bold text-success">{{ active_sub.days_remaining }}</div>
                                <small class="text-muted">يوم متبقي</small>
                                {% if active_sub.is_expiring_soon %}
                                <div class="alert alert-warning mt-2 mb-0" role="alert">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    ينتهي قريباً!
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                            {% elif user.subscription_status == 'trial' and user.free_trial_end %}
                            <div class="text-center">
                                <div class="display-4 fw-bold text-warning">{{ trial_days_left }}</div>
                                <small class="text-muted">يوم متبقي في الفترة التجريبية</small>
                                {% if trial_days_left <= 7 %} <div class="alert alert-warning mt-2 mb-0" role="alert">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    تنتهي الفترة التجريبية قريباً!
                            </div>
                            {% endif %}
                        </div>
                        {% else %}
                        <div class="text-center">
                            <div class="display-4 fw-bold text-danger">0</div>
                            <small class="text-muted">يوم متبقي</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- إدارة الاشتراك (للأدمن ومدير المستخدمين فقط) -->
        {% if (current_user.role == 'admin' or current_user.role == 'user_manager') and user.role in ['teacher',
        'inspector'] %}
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إدارة الاشتراك
                    <small class="text-muted">(للإدارة فقط)</small>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> هذه الميزة مخصصة للاتفاقيات مع المؤسسات الخاصة والنقابات المعتمدة.
                    يمكن إضافة حد أقصى 300 يوم للاشتراك.
                </div>

                <form id="extendSubscriptionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="daysToAdd" class="form-label">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    عدد الأيام المراد إضافتها
                                </label>
                                <input type="number" class="form-control" id="daysToAdd" name="days_to_add" min="1"
                                    max="300" required>
                                <div class="form-text">الحد الأدنى: 1 يوم، الحد الأقصى: 300 يوم</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="extensionReason" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    سبب الإضافة (اختياري)
                                </label>
                                <input type="text" class="form-control" id="extensionReason" name="reason"
                                    placeholder="مثال: اتفاقية مع نقابة المعلمين">
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-plus-circle me-2"></i>
                            إضافة الأيام
                        </button>
                    </div>
                </form>

                <!-- رسائل النتيجة -->
                <div id="extensionResult" class="mt-3" style="display: none;"></div>
            </div>
        </div>
        {% endif %}

        <!-- إحصائيات النشاط -->
        {% if stats %}
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات النشاط
                </h5>
            </div>
            <div class="card-body">
                <!-- معلومات آخر نشاط (لجميع المستخدمين) -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="border rounded p-3 bg-light">
                            <i class="fas fa-clock fa-2x text-info mb-2"></i>
                            <h6 class="mb-1">آخر تسجيل دخول</h6>
                            {% if stats.last_login %}
                            <p class="mb-0 text-primary">
                                <strong>{{ stats.last_login.strftime('%Y-%m-%d') }}</strong>
                            </p>
                            <small class="text-muted">{{ stats.last_login.strftime('%H:%M:%S') }}</small>
                            {% if stats.days_since_last_login is not none %}
                            <div class="mt-2">
                                {% if stats.days_since_last_login == 0 %}
                                <span class="badge bg-success">نشط اليوم</span>
                                {% elif stats.days_since_last_login <= 7 %} <span class="badge bg-info">{{
                                    stats.days_since_last_login }} أيام</span>
                                    {% elif stats.days_since_last_login <= 30 %} <span class="badge bg-warning">{{
                                        stats.days_since_last_login }} يوم</span>
                                        {% else %}
                                        <span class="badge bg-danger">{{ stats.days_since_last_login }} يوم</span>
                                        {% endif %}
                            </div>
                            {% endif %}
                            {% else %}
                            <p class="mb-0 text-warning">لم يسجل دخول بعد</p>
                            <span class="badge bg-secondary">حساب جديد</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 bg-light">
                            <i class="fas fa-user-plus fa-2x text-secondary mb-2"></i>
                            <h6 class="mb-1">تاريخ إنشاء الحساب</h6>
                            <p class="mb-0 text-primary">
                                <strong>{{ stats.created_at.strftime('%Y-%m-%d') }}</strong>
                            </p>
                            <small class="text-muted">{{ stats.created_at.strftime('%H:%M:%S') }}</small>
                        </div>
                    </div>
                </div>

                <!-- تقييم حالة النشاط العامة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert
                                {% if stats.days_since_last_login is none %}
                                    alert-secondary
                                {% elif stats.days_since_last_login == 0 %}
                                    alert-success
                                {% elif stats.days_since_last_login <= 7 %}
                                    alert-info
                                {% elif stats.days_since_last_login <= 30 %}
                                    alert-warning
                                {% elif stats.days_since_last_login <= 180 %}
                                    alert-danger
                                {% else %}
                                    alert-dark
                                {% endif %}
                                text-center">
                            <i class="fas
                                    {% if stats.days_since_last_login is none %}
                                        fa-user-clock
                                    {% elif stats.days_since_last_login == 0 %}
                                        fa-check-circle
                                    {% elif stats.days_since_last_login <= 7 %}
                                        fa-clock
                                    {% elif stats.days_since_last_login <= 30 %}
                                        fa-exclamation-triangle
                                    {% elif stats.days_since_last_login <= 180 %}
                                        fa-times-circle
                                    {% else %}
                                        fa-ban
                                    {% endif %}
                                    fa-2x mb-2"></i>
                            <h5 class="mb-1">
                                {% if stats.days_since_last_login is none %}
                                حساب جديد - لم يسجل دخول بعد
                                {% elif stats.days_since_last_login == 0 %}
                                مستخدم نشط - سجل دخول اليوم
                                {% elif stats.days_since_last_login <= 7 %} مستخدم نشط - آخر نشاط خلال الأسبوع {% elif
                                    stats.days_since_last_login <=30 %} مستخدم غير نشط - آخر نشاط خلال الشهر {% elif
                                    stats.days_since_last_login <=180 %} مستخدم خامل - آخر نشاط منذ أكثر من شهر {% else
                                    %} حساب مهجور - آخر نشاط منذ أكثر من 6 أشهر {% endif %} </h5>
                                    {% if stats.days_since_last_login is not none and stats.days_since_last_login >
                                    30 %}
                                    <p class="mb-0">
                                        <small>قد يحتاج هذا المستخدم إلى متابعة أو تذكير بالنشاط</small>
                                    </p>
                                    {% endif %}
                        </div>
                    </div>
                </div>

                {% if user.role == 'teacher' %}
                <!-- إحصائيات المعلم -->
                <div class="row text-center mb-3">
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                            <h4 class="mb-1">{{ stats.total_schedules }}</h4>
                            <small class="text-muted">إجمالي الجداول</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <i class="fas fa-tasks fa-2x text-success mb-2"></i>
                            <h4 class="mb-1">{{ stats.total_progress }}</h4>
                            <small class="text-muted">إدخالات التقدم</small>
                        </div>
                    </div>
                </div>

                <!-- آخر نشاط في التقدم -->
                {% if stats.last_progress_activity %}
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>آخر نشاط في التقدم:</strong>
                            {{ stats.last_progress_activity.strftime('%Y-%m-%d في %H:%M:%S') }}
                        </div>
                    </div>
                </div>
                {% endif %}

                {% elif user.role == 'inspector' %}
                <!-- إحصائيات المفتش -->
                <div class="row text-center mb-3">
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <i class="fas fa-users fa-2x text-warning mb-2"></i>
                            <h4 class="mb-1">{{ stats.assigned_teachers }}</h4>
                            <small class="text-muted">الأساتذة المكلفين</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <i class="fas fa-bell fa-2x text-info mb-2"></i>
                            <h4 class="mb-1">{{ stats.total_notifications }}</h4>
                            <small class="text-muted">إجمالي الإشعارات</small>
                        </div>
                    </div>
                </div>

                <!-- آخر نشاط في الإشعارات -->
                {% if stats.last_notification_activity %}
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>آخر نشاط في الإشعارات:</strong>
                            {{ stats.last_notification_activity.strftime('%Y-%m-%d في %H:%M:%S') }}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const extendForm = document.getElementById('extendSubscriptionForm');
        const resultDiv = document.getElementById('extensionResult');

        if (extendForm) {
            extendForm.addEventListener('submit', function (e) {
                e.preventDefault();

                const formData = new FormData(extendForm);
                const daysToAdd = parseInt(formData.get('days_to_add'));
                const reason = formData.get('reason');

                // التحقق من صحة البيانات
                if (!daysToAdd || daysToAdd < 1 || daysToAdd > 300) {
                    showResult('error', 'يجب أن يكون عدد الأيام بين 1 و 300');
                    return;
                }

                // تأكيد العملية
                const confirmMessage = `هل أنت متأكد من إضافة ${daysToAdd} يوم للمستخدم {{ user.username }}؟`;
                if (!confirm(confirmMessage)) {
                    return;
                }

                // تعطيل الزر أثناء المعالجة
                const submitBtn = extendForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';

                // إرسال الطلب
                fetch('/api/extend_subscription', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: {{ user.id }},
                    days_to_add: daysToAdd,
                    reason: reason
                })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResult('success', data.message);
                // إعادة تحميل الصفحة بعد 2 ثانية لإظهار التحديثات
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showResult('error', data.error || 'حدث خطأ غير متوقع');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showResult('error', 'حدث خطأ في الاتصال بالخادم');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
        });
    }

    function showResult(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

        resultDiv.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        resultDiv.style.display = 'block';

        // التمرير إلى النتيجة
        resultDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
});
</script>

{% endblock %}