#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعدادات التطبيق للبيئات المختلفة
يدعم SQLite للتطوير و PostgreSQL للإنتاج
"""

import os
from datetime import timedelta

class Config:
    """الإعدادات الأساسية"""
    
    # إعدادات Flask الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {"check_same_thread": False} if 'sqlite' in os.environ.get('DATABASE_URL', '') else {}
    }
    
    # إعدادات الجلسات
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # سيتم تفعيله في الإنتاج
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات Redis (للجلسات والتخزين المؤقت)
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # إعدادات التطبيق
    NOTIFICATIONS_PER_PAGE = 10
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file upload
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات Rate Limiting - محسنة للأداء العالي
    RATELIMIT_STORAGE_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/1'
    RATELIMIT_DEFAULT = "2000 per hour"  # زيادة الحد للأداء العالي
    
    # إعدادات التسجيل
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'logs/ta9affi.log'

    # إعدادات Chargily
    CHARGILY_PUBLIC_KEY = os.environ.get('CHARGILY_PUBLIC_KEY') or 'live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk'
    CHARGILY_SECRET_KEY = os.environ.get('CHARGILY_SECRET_KEY') or 'live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU'
    CHARGILY_WEBHOOK_URL = os.environ.get('CHARGILY_WEBHOOK_URL') or 'http://ta9affi.com/chargily-webhook'
    BASE_URL = os.environ.get('BASE_URL') or 'http://ta9affi.com'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق مع الإعدادات"""
        pass

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    
    DEBUG = True
    
    # استخدام SQLite في التطوير (للحفاظ على التوافق)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(__file__), 'instance', 'ta9affi.db')
    
    # إعدادات أقل صرامة للتطوير
    WTF_CSRF_ENABLED = False  # تعطيل CSRF في التطوير لسهولة الاختبار
    SESSION_COOKIE_SECURE = False
    
    # تسجيل مفصل في التطوير
    LOG_LEVEL = 'DEBUG'

    # إعدادات Chargily للتطوير
    BASE_URL = os.environ.get('BASE_URL') or 'http://127.0.0.1:5000'
    CHARGILY_WEBHOOK_URL = os.environ.get('CHARGILY_WEBHOOK_URL') or 'http://127.0.0.1:5000/chargily-webhook'
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        # إنشاء مجلد instance إذا لم يكن موجوداً
        instance_dir = os.path.join(os.path.dirname(__file__), 'instance')
        os.makedirs(instance_dir, exist_ok=True)

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    
    DEBUG = False
    
    # استخدام SQLite في الإنتاج (أبسط وأكثر استقرار)
    # استخدام مسار مطلق لقاعدة البيانات في Docker
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:////app/ta9affi.db'
    
    # إعدادات محسنة للإنتاج - مُحسنة للتزامن العالي
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 40,           # زيادة pool size للـ 8 workers
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'max_overflow': 60,        # زيادة overflow للتزامن العالي
        'pool_timeout': 30,        # مهلة انتظار الاتصال
        'connect_args': {
            "check_same_thread": False,
            "timeout": 20,         # مهلة اتصال SQLite
            "isolation_level": None  # تحسين الأداء
        }
    }
    
    # أمان محسن للإنتاج
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True
    
    # مفتاح سري قوي (يجب تغييره في الإنتاج)
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key-must-be-changed'
    
    # تسجيل محسن للإنتاج
    LOG_LEVEL = 'WARNING'

    # إعدادات Chargily للإنتاج
    BASE_URL = os.environ.get('BASE_URL') or 'http://ta9affi.com'
    CHARGILY_WEBHOOK_URL = os.environ.get('CHARGILY_WEBHOOK_URL') or 'http://ta9affi.com/chargily-webhook'
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        # إنشاء مجلد السجلات
        log_dir = os.path.dirname(Config.LOG_FILE)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    
    TESTING = True
    DEBUG = True
    
    # استخدام قاعدة بيانات في الذاكرة للاختبارات
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # تعطيل CSRF في الاختبارات
    WTF_CSRF_ENABLED = False
    
    # تسريع الاختبارات
    SQLALCHEMY_ENGINE_OPTIONS = {}

# قاموس الإعدادات
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """الحصول على إعدادات البيئة الحالية"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])

# إعدادات PostgreSQL للترحيل
POSTGRESQL_CONFIG = {
    'host': os.environ.get('POSTGRES_HOST', 'localhost'),
    'port': int(os.environ.get('POSTGRES_PORT', 5432)),
    'database': os.environ.get('POSTGRES_DB', 'ta9affi'),
    'user': os.environ.get('POSTGRES_USER', 'ta9affi_user'),
    'password': os.environ.get('POSTGRES_PASSWORD', 'ta9affi_password')
}

# إعدادات Redis
REDIS_CONFIG = {
    'host': os.environ.get('REDIS_HOST', 'localhost'),
    'port': int(os.environ.get('REDIS_PORT', 6379)),
    'db': int(os.environ.get('REDIS_DB', 0)),
    'decode_responses': True
}
