#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جداول المعلمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models_new import User, Schedule, Role

def check_teacher_schedules():
    """فحص جداول المعلمين"""
    
    with app.app_context():
        print("=" * 60)
        print("فحص جداول المعلمين")
        print("=" * 60)
        
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        print(f"إجمالي المعلمين: {len(teachers)}")
        print("-" * 40)
        
        teachers_with_schedules = 0
        teachers_without_schedules = 0
        
        for teacher in teachers:
            schedules = Schedule.query.filter_by(user_id=teacher.id).all()
            
            if schedules:
                teachers_with_schedules += 1
                print(f"✅ {teacher.username}: {len(schedules)} جدول دراسي")
                for schedule in schedules:
                    level_name = schedule.level.name if schedule.level else "غير محدد"
                    subject_name = schedule.subject.name if schedule.subject else "غير محدد"
                    print(f"   - المستوى: {level_name}, المادة: {subject_name}")
            else:
                teachers_without_schedules += 1
                print(f"❌ {teacher.username}: لا يوجد جدول دراسي")
        
        print("\n" + "=" * 60)
        print("الملخص:")
        print(f"المعلمون الذين لديهم جداول: {teachers_with_schedules}")
        print(f"المعلمون الذين ليس لديهم جداول: {teachers_without_schedules}")
        
        if teachers_without_schedules > 0:
            print("\n⚠️  المعلمون بدون جداول لن تظهر لهم أقسام الموارد المعرفية!")
            print("💡 الحل: إضافة جداول دراسية لهؤلاء المعلمين")
        
        print("=" * 60)

if __name__ == "__main__":
    check_teacher_schedules()
