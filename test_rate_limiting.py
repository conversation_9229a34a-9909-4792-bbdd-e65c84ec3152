#!/usr/bin/env python3
"""
اختبار نظام Rate Limiting في البيئة الإنتاجية
"""

import os
import requests
import time
import json

def test_rate_limiting_production():
    """اختبار Rate Limiting في البيئة الإنتاجية"""
    
    print("🧪 اختبار Rate Limiting في البيئة الإنتاجية...")
    print("=" * 60)
    
    base_url = "http://ta9affi.com"  # أو https://ta9affi.com
    
    # بيانات تسجيل الدخول
    login_data = {
        'username': 'teacher',  # استخدم حساب موجود
        'password': 'teacher123'
    }
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        # 1. تسجيل الدخول
        print("🔄 تسجيل الدخول...")
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
        
        # 2. اختبار إضافة تقدمات متعددة
        print("\n🔄 اختبار إضافة تقدمات متعددة...")
        
        success_count = 0
        rate_limited_count = 0
        
        for i in range(15):  # محاولة إضافة 15 تقدم (الحد 10)
            print(f"📝 محاولة إضافة تقدم #{i+1}")
            
            # بيانات التقدم (تحتاج تعديل حسب API الفعلي)
            progress_data = {
                'title': f'تقدم اختبار #{i+1}',
                'description': 'اختبار Rate Limiting',
                'subject_id': 1,  # تحتاج تعديل
                'lesson_id': 1    # تحتاج تعديل
            }
            
            response = session.post(f"{base_url}/add-progress", data=progress_data)
            
            if response.status_code == 200:
                success_count += 1
                print(f"   ✅ نجح التقدم #{i+1}")
            elif response.status_code == 429:
                rate_limited_count += 1
                print(f"   🚫 تم حظر التقدم #{i+1} - Rate Limited")
                
                # محاولة قراءة رسالة الخطأ
                try:
                    error_data = response.json()
                    print(f"   📋 الرسالة: {error_data.get('message', 'N/A')}")
                except:
                    print(f"   📋 النص: {response.text[:100]}...")
                    
                break
            else:
                print(f"   ❌ خطأ في التقدم #{i+1}: {response.status_code}")
            
            # انتظار قصير بين الطلبات
            time.sleep(0.5)
        
        print(f"\n📊 نتائج الاختبار:")
        print(f"   - تقدمات ناجحة: {success_count}")
        print(f"   - تقدمات محظورة: {rate_limited_count}")
        
        # 3. اختبار حذف تقدمات
        print(f"\n🔄 اختبار حذف تقدمات...")
        
        delete_success = 0
        delete_limited = 0
        
        for i in range(5):  # محاولة حذف 5 تقدمات (الحد 3)
            print(f"🗑️ محاولة حذف تقدم #{i+1}")
            
            # بيانات الحذف (تحتاج تعديل حسب API الفعلي)
            delete_data = {
                'progress_id': i+1  # تحتاج تعديل
            }
            
            response = session.post(f"{base_url}/delete-progress", data=delete_data)
            
            if response.status_code == 200:
                delete_success += 1
                print(f"   ✅ نجح حذف التقدم #{i+1}")
            elif response.status_code == 429:
                delete_limited += 1
                print(f"   🚫 تم حظر حذف التقدم #{i+1} - Rate Limited")
                break
            else:
                print(f"   ❌ خطأ في حذف التقدم #{i+1}: {response.status_code}")
            
            time.sleep(0.5)
        
        print(f"\n📊 نتائج اختبار الحذف:")
        print(f"   - حذف ناجح: {delete_success}")
        print(f"   - حذف محظور: {delete_limited}")
        
        # 4. التحقق من حالة Rate Limiting
        print(f"\n🔄 فحص حالة Rate Limiting...")
        
        status_response = session.get(f"{base_url}/rate-limit-status")
        if status_response.status_code == 200:
            try:
                status_data = status_response.json()
                print(f"📊 حالة Rate Limiting:")
                print(json.dumps(status_data, indent=2, ensure_ascii=False))
            except:
                print(f"📋 استجابة الحالة: {status_response.text[:200]}...")
        
        # تقييم النتائج
        if rate_limited_count > 0 or delete_limited > 0:
            print(f"\n✅ Rate Limiting يعمل في البيئة الإنتاجية!")
            return True
        else:
            print(f"\n❌ Rate Limiting لا يعمل - لم يتم حظر أي طلبات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_ip_detection():
    """اختبار كشف IP في البيئة الإنتاجية"""
    
    print("\n🔍 اختبار كشف IP...")
    
    try:
        # طلب لصفحة تُظهر معلومات IP
        response = requests.get("http://ta9affi.com/debug-ip")  # تحتاج إنشاء هذا المسار
        
        if response.status_code == 200:
            print(f"📊 معلومات IP: {response.text}")
        else:
            print(f"❌ فشل في الحصول على معلومات IP: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار IP: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار Rate Limiting - Ta9affi Production")
    print("=" * 60)
    
    # اختبار Rate Limiting
    if test_rate_limiting_production():
        print("\n🎉 Rate Limiting يعمل بشكل صحيح!")
    else:
        print("\n❌ Rate Limiting لا يعمل - يحتاج إصلاح")
    
    # اختبار كشف IP
    test_ip_detection()
    
    print("\n📋 ملاحظات:")
    print("1. تأكد من تحديث nginx config")
    print("2. راجع logs التطبيق للتشخيص المفصل")
    print("3. تأكد من وجود Redis في البيئة الإنتاجية")

if __name__ == '__main__':
    main()
