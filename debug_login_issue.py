#!/usr/bin/env python3
"""
سكريبت تشخيص مشكلة تسجيل الدخول - Ta9affi
"""

import os
import sys
from werkzeug.security import check_password_hash, generate_password_hash

def test_login_process():
    """اختبار عملية تسجيل الدخول"""
    
    print("🔍 تشخيص مشكلة تسجيل الدخول...")
    print("=" * 50)
    
    # تعيين متغيرات البيئة
    os.environ['FLASK_ENV'] = 'production'
    os.environ['PRODUCTION_MODE'] = 'true'
    
    try:
        # استيراد التطبيق والنماذج
        from app import app, db
        from models_new import User, Role
        
        with app.app_context():
            print("✅ تم تحميل التطبيق وقاعدة البيانات")
            
            # فحص المستخدمين الموجودين
            users = User.query.all()
            print(f"📊 عدد المستخدمين في قاعدة البيانات: {len(users)}")
            
            if not users:
                print("❌ لا يوجد مستخدمين في قاعدة البيانات!")
                print("🔄 إنشاء مستخدم تجريبي...")
                
                # إنشاء مستخدم تجريبي
                test_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password=generate_password_hash('admin123'),
                    role=Role.ADMIN,
                    phone_number='0123456789',
                    is_active=True
                )
                
                db.session.add(test_user)
                db.session.commit()
                print("✅ تم إنشاء مستخدم تجريبي: admin / admin123")
                
                users = User.query.all()
            
            print("\n📋 قائمة المستخدمين:")
            print("-" * 50)
            
            for user in users[:5]:  # عرض أول 5 مستخدمين فقط
                print(f"👤 المستخدم: {user.username}")
                print(f"   📧 البريد: {user.email}")
                print(f"   🔑 الدور: {user.role}")
                print(f"   ✅ نشط: {user.is_active}")
                print(f"   🔐 كلمة المرور مشفرة: {user.password[:20]}...")
                
                # اختبار كلمة مرور افتراضية
                test_passwords = ['admin123', 'password', '123456', user.username]
                
                for test_pass in test_passwords:
                    try:
                        if check_password_hash(user.password, test_pass):
                            print(f"   ✅ كلمة المرور الصحيحة: {test_pass}")
                            break
                    except Exception as e:
                        print(f"   ❌ خطأ في فحص كلمة المرور: {str(e)}")
                        # قد تكون كلمة المرور غير مشفرة
                        if user.password == test_pass:
                            print(f"   ⚠️ كلمة المرور غير مشفرة! تحديث...")
                            user.password = generate_password_hash(test_pass)
                            db.session.commit()
                            print(f"   ✅ تم تشفير كلمة المرور")
                
                print("-" * 30)
            
            # اختبار تسجيل دخول محاكي
            print("\n🧪 اختبار تسجيل دخول محاكي...")
            
            # جرب مع أول مستخدم
            if users:
                test_user = users[0]
                print(f"🔄 اختبار تسجيل دخول للمستخدم: {test_user.username}")
                
                # جرب كلمات مرور مختلفة
                test_passwords = ['admin123', 'password', '123456', test_user.username]
                
                for password in test_passwords:
                    try:
                        if check_password_hash(test_user.password, password):
                            print(f"✅ نجح تسجيل الدخول بكلمة المرور: {password}")
                            
                            # اختبار Flask-Login
                            from flask_login import login_user
                            
                            # محاكاة تسجيل الدخول
                            print("🔄 اختبار Flask-Login...")
                            
                            # التحقق من خصائص المستخدم المطلوبة لـ Flask-Login
                            print(f"   is_authenticated: {test_user.is_authenticated}")
                            print(f"   is_active: {test_user.is_active}")
                            print(f"   is_anonymous: {test_user.is_anonymous}")
                            print(f"   get_id(): {test_user.get_id()}")
                            
                            return True
                    except Exception as e:
                        print(f"❌ خطأ في اختبار كلمة المرور {password}: {str(e)}")
                
                print("❌ فشل في جميع كلمات المرور المختبرة")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشخيص تسجيل الدخول: {str(e)}")
        import traceback
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

def fix_password_hashing():
    """إصلاح تشفير كلمات المرور"""
    
    print("\n🔧 إصلاح تشفير كلمات المرور...")
    
    try:
        from app import app, db
        from models_new import User
        
        with app.app_context():
            users = User.query.all()
            
            fixed_count = 0
            for user in users:
                # التحقق من أن كلمة المرور مشفرة
                if not user.password.startswith('pbkdf2:sha256:'):
                    print(f"🔄 إصلاح كلمة مرور المستخدم: {user.username}")
                    
                    # افتراض كلمات مرور شائعة
                    possible_passwords = [
                        'admin123', 'password', '123456', 
                        user.username, user.username + '123'
                    ]
                    
                    # جرب كلمة مرور افتراضية
                    new_password = 'admin123'  # كلمة مرور افتراضية
                    user.password = generate_password_hash(new_password)
                    fixed_count += 1
                    
                    print(f"   ✅ تم تعيين كلمة مرور جديدة: {new_password}")
            
            if fixed_count > 0:
                db.session.commit()
                print(f"✅ تم إصلاح {fixed_count} كلمة مرور")
            else:
                print("✅ جميع كلمات المرور مشفرة بشكل صحيح")
                
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح كلمات المرور: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔍 تشخيص مشكلة تسجيل الدخول - Ta9affi")
    print("=" * 60)
    
    # اختبار عملية تسجيل الدخول
    if test_login_process():
        print("\n🎉 تسجيل الدخول يعمل بشكل صحيح!")
    else:
        print("\n❌ مشكلة في تسجيل الدخول - محاولة الإصلاح...")
        
        # محاولة إصلاح كلمات المرور
        if fix_password_hashing():
            print("\n🔄 إعادة اختبار تسجيل الدخول...")
            if test_login_process():
                print("\n🎉 تم إصلاح المشكلة!")
            else:
                print("\n❌ المشكلة لا تزال موجودة")
        
    print("\n📋 معلومات تسجيل الدخول:")
    print("   - اسم المستخدم: admin")
    print("   - كلمة المرور: admin123")
    print("   - الرابط: http://ta9affi.com/login")

if __name__ == '__main__':
    main()
