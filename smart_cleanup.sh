#!/bin/bash
# تنظيف ذكي - الاحتفاظ بالملفات الضرورية للإنتاج فقط

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# إنشاء نسخة احتياطية
create_backup() {
    log_header "💾 إنشاء نسخة احتياطية"
    
    local backup_name="backup_before_smart_cleanup_$(date +%Y%m%d_%H%M%S)"
    tar --exclude='.git' -czf "${backup_name}.tar.gz" .
    
    log_success "تم إنشاء نسخة احتياطية: ${backup_name}.tar.gz"
}

# قائمة الملفات Python المطلوبة
get_required_python_files() {
    echo "app_postgresql.py
models_optimized.py
redis_manager.py
cache_manager.py
database_optimizer.py
monitoring_system.py
alerting_system.py
security_manager.py
auth_security.py
file_security.py
backup_manager.py
frontend_optimizer.py"
}

# قائمة ملفات الإعداد المطلوبة
get_required_config_files() {
    echo "requirements.txt
docker-compose.production.yml
Dockerfile.production
gunicorn_config.py
deploy.sh
.env.production.example
.gitignore
README.md
LICENSE
CHANGELOG.md"
}

# قائمة المجلدات المطلوبة
get_required_directories() {
    echo "static
templates
nginx
monitoring
database"
}

# حذف جميع ملفات Python غير المطلوبة
cleanup_python_files() {
    log_header "🐍 تنظيف ملفات Python"
    
    local required_files=$(get_required_python_files)
    local deleted_count=0
    
    # حذف جميع ملفات .py غير المطلوبة
    for py_file in *.py; do
        if [ -f "$py_file" ]; then
            if ! echo "$required_files" | grep -q "^${py_file}$"; then
                log_info "حذف ملف Python غير مطلوب: $py_file"
                rm -f "$py_file"
                ((deleted_count++))
            fi
        fi
    done
    
    # حذف ملفات Python المؤقتة
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true
    find . -name "*.pyd" -delete 2>/dev/null || true
    
    log_success "تم حذف $deleted_count ملف Python غير مطلوب"
}

# حذف ملفات الإعداد غير المطلوبة
cleanup_config_files() {
    log_header "⚙️ تنظيف ملفات الإعداد"
    
    local required_files=$(get_required_config_files)
    local deleted_count=0
    
    # قائمة أنواع الملفات المراد فحصها
    local extensions=("*.txt" "*.yml" "*.yaml" "*.sh" "*.md" "*.example" "*.json" "*.conf")
    
    for ext in "${extensions[@]}"; do
        for file in $ext; do
            if [ -f "$file" ]; then
                if ! echo "$required_files" | grep -q "^${file}$"; then
                    log_info "حذف ملف إعداد غير مطلوب: $file"
                    rm -f "$file"
                    ((deleted_count++))
                fi
            fi
        done
    done
    
    log_success "تم حذف $deleted_count ملف إعداد غير مطلوب"
}

# تنظيف المجلدات
cleanup_directories() {
    log_header "📁 تنظيف المجلدات"
    
    local required_dirs=$(get_required_directories)
    local deleted_count=0
    
    # حذف المجلدات غير المطلوبة
    for dir in */; do
        if [ -d "$dir" ]; then
            local dir_name=${dir%/}  # إزالة الشرطة المائلة
            
            # تجاهل مجلد .git
            if [ "$dir_name" = ".git" ]; then
                continue
            fi
            
            if ! echo "$required_dirs" | grep -q "^${dir_name}$"; then
                log_info "حذف مجلد غير مطلوب: $dir_name"
                rm -rf "$dir"
                ((deleted_count++))
            fi
        fi
    done
    
    # حذف مجلدات Python المؤقتة
    rm -rf build/ dist/ *.egg-info/ 2>/dev/null || true
    
    # حذف مجلدات IDE
    rm -rf .vscode/ .idea/ 2>/dev/null || true
    
    # حذف مجلدات الاختبار
    rm -rf tests/ .pytest_cache/ htmlcov/ .tox/ 2>/dev/null || true
    
    log_success "تم حذف $deleted_count مجلد غير مطلوب"
}

# تنظيف مجلد static
cleanup_static_directory() {
    log_header "🎨 تنظيف مجلد static"
    
    if [ ! -d "static" ]; then
        log_warning "مجلد static غير موجود"
        return
    fi
    
    cd static
    
    # حذف ملفات التطوير
    rm -f js/lazy-loading.js js/performance.js 2>/dev/null || true
    rm -f css/performance.css 2>/dev/null || true
    rm -f sw.js manifest.json 2>/dev/null || true
    
    # حذف ملفات gzip المؤقتة
    find . -name "*.gz" -delete 2>/dev/null || true
    
    # حذف مجلد optimized إذا كان فارغاً
    if [ -d "optimized" ] && [ -z "$(ls -A optimized)" ]; then
        rmdir optimized
    fi
    
    cd ..
    
    log_success "تم تنظيف مجلد static"
}

# تنظيف مجلد templates
cleanup_templates_directory() {
    log_header "📄 تنظيف مجلد templates"
    
    if [ ! -d "templates" ]; then
        log_warning "مجلد templates غير موجود"
        return
    fi
    
    cd templates
    
    # حذف القوالب المحسنة للتطوير
    rm -f base_optimized.html 2>/dev/null || true
    
    cd ..
    
    log_success "تم تنظيف مجلد templates"
}

# تنظيف مجلد nginx
cleanup_nginx_directory() {
    log_header "🌐 تنظيف مجلد nginx"
    
    if [ ! -d "nginx" ]; then
        log_warning "مجلد nginx غير موجود"
        return
    fi
    
    cd nginx
    
    # حذف إعدادات التطوير المحلي
    rm -f nginx.local.conf 2>/dev/null || true
    
    cd ..
    
    log_success "تم تنظيف مجلد nginx"
}

# تنظيف مجلد monitoring
cleanup_monitoring_directory() {
    log_header "📊 تنظيف مجلد monitoring"
    
    if [ ! -d "monitoring" ]; then
        log_warning "مجلد monitoring غير موجود"
        return
    fi
    
    cd monitoring
    
    # حذف إعدادات التطوير المحلي
    rm -f prometheus.local.yml 2>/dev/null || true
    
    cd ..
    
    log_success "تم تنظيف مجلد monitoring"
}

# حذف ملفات النظام والمؤقتة
cleanup_system_files() {
    log_header "🗂️ حذف ملفات النظام والمؤقتة"
    
    # ملفات النظام
    find . -name ".DS_Store" -delete 2>/dev/null || true
    find . -name "._*" -delete 2>/dev/null || true
    find . -name "Thumbs.db" -delete 2>/dev/null || true
    find . -name "Desktop.ini" -delete 2>/dev/null || true
    find . -name "*~" -delete 2>/dev/null || true
    
    # ملفات المحررات
    find . -name ".*.swp" -delete 2>/dev/null || true
    find . -name ".*.swo" -delete 2>/dev/null || true
    find . -name "\#*\#" -delete 2>/dev/null || true
    find . -name ".\#*" -delete 2>/dev/null || true
    
    # ملفات السجلات والبيانات المؤقتة
    rm -f *.log *.db *.sqlite *.sqlite3 2>/dev/null || true
    
    log_success "تم حذف ملفات النظام والمؤقتة"
}

# إنشاء .gitignore نهائي
create_production_gitignore() {
    log_header "📝 إنشاء .gitignore للإنتاج"
    
    cat > .gitignore << 'EOF'
# ملفات البيئة والإعدادات الحساسة
.env
.env.production
.env.local

# ملفات Python
__pycache__/
*.py[cod]
*$py.class
*.so
build/
dist/
*.egg-info/

# ملفات السجلات
logs/
*.log

# ملفات قاعدة البيانات المحلية
*.db
*.sqlite
*.sqlite3

# ملفات التحميل والتخزين المؤقت
uploads/
cache/
tmp/
temp/
sessions/

# ملفات النسخ الاحتياطي
backups/

# ملفات النظام
.DS_Store
Thumbs.db
Desktop.ini

# ملفات الأمان
*.pem
*.key
*.crt
EOF

    log_success "تم إنشاء .gitignore للإنتاج"
}

# عرض الملفات المتبقية
show_remaining_files() {
    log_header "📋 الملفات المتبقية"
    
    echo ""
    echo "ملفات Python:"
    ls -la *.py 2>/dev/null | awk '{print "  " $9 " (" $5 " bytes)"}' || echo "  لا توجد ملفات Python"
    
    echo ""
    echo "ملفات الإعداد:"
    ls -la *.txt *.yml *.sh *.md *.example 2>/dev/null | awk '{print "  " $9 " (" $5 " bytes)"}' || echo "  لا توجد ملفات إعداد"
    
    echo ""
    echo "المجلدات:"
    for dir in */; do
        if [ -d "$dir" ]; then
            local size=$(du -sh "$dir" 2>/dev/null | cut -f1)
            echo "  ${dir%/} ($size)"
        fi
    done
    
    echo ""
    local file_count=$(find . -type f | wc -l)
    local dir_count=$(find . -type d | wc -l)
    local total_size=$(du -sh . | cut -f1)
    
    echo "📊 الإحصائيات:"
    echo "  الملفات: $file_count"
    echo "  المجلدات: $dir_count"
    echo "  الحجم الإجمالي: $total_size"
}

# إنشاء حزمة الإنتاج النظيفة
create_clean_package() {
    log_header "📦 إنشاء حزمة الإنتاج النظيفة"
    
    local package_name="ta9affi-production-clean-$(date +%Y%m%d_%H%M%S)"
    
    # إنشاء أرشيف مضغوط
    tar --exclude='.git' --exclude='backup_*' --exclude='*.tar.gz' \
        -czf "${package_name}.tar.gz" .
    
    local package_size=$(du -h "${package_name}.tar.gz" | cut -f1)
    local file_count=$(tar -tzf "${package_name}.tar.gz" | wc -l)
    
    log_success "تم إنشاء حزمة الإنتاج النظيفة:"
    echo "  📦 الاسم: ${package_name}.tar.gz"
    echo "  📏 الحجم: $package_size"
    echo "  📄 عدد الملفات: $file_count"
    
    echo ""
    echo "🚀 لرفع الحزمة للخادم:"
    echo "  scp ${package_name}.tar.gz user@server:/opt/"
    echo ""
    echo "🔧 لفك الضغط في الخادم:"
    echo "  tar -xzf ${package_name}.tar.gz"
    echo "  cd ta9affi-production-clean-*"
    echo "  cp .env.production.example .env.production"
    echo "  # تعديل .env.production"
    echo "  ./deploy.sh deploy"
}

# عرض الملخص النهائي
show_final_summary() {
    log_header "🎉 ملخص التنظيف الذكي"
    
    echo "=============================================="
    echo "✅ تم تنظيف Ta9affi بذكاء للإنتاج!"
    echo "=============================================="
    echo ""
    echo "🧹 ما تم حذفه:"
    echo "   - جميع ملفات التطوير والاختبار"
    echo "   - ملفات IDE والمحررات"
    echo "   - ملفات النظام المؤقتة"
    echo "   - المجلدات غير الضرورية"
    echo "   - ملفات Python غير المطلوبة"
    echo ""
    echo "✅ ما تم الاحتفاظ به:"
    echo "   - الملفات الأساسية للتطبيق فقط"
    echo "   - ملفات الإعداد والنشر"
    echo "   - المجلدات الضرورية"
    echo "   - ملفات التوثيق الأساسية"
    echo ""
    echo "🚀 المشروع جاهز للرفع والنشر!"
    echo "=============================================="
}

# الدالة الرئيسية
main() {
    log_header "🧠 التنظيف الذكي لـ Ta9affi"
    echo "=============================================="
    echo "سيتم الاحتفاظ بالملفات الضرورية للإنتاج فقط"
    echo "وحذف جميع ملفات التطوير والاختبار"
    echo "=============================================="
    echo ""
    
    # تأكيد من المستخدم
    read -p "هل تريد المتابعة مع التنظيف الذكي؟ (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "تم إلغاء التنظيف"
        exit 0
    fi
    
    # تنفيذ التنظيف الذكي
    create_backup
    cleanup_python_files
    cleanup_config_files
    cleanup_directories
    cleanup_static_directory
    cleanup_templates_directory
    cleanup_nginx_directory
    cleanup_monitoring_directory
    cleanup_system_files
    create_production_gitignore
    
    echo ""
    show_remaining_files
    echo ""
    
    create_clean_package
    show_final_summary
    
    log_success "🎉 تم التنظيف الذكي بنجاح!"
}

# تشغيل السكريبت
main "$@"
