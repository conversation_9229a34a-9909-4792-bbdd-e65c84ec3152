{% extends 'base.html' %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <i class="fas fa-crown animated-icon me-3"></i>
                        اختر الباقة المناسبة لك
                    </h1>
                    <p class="hero-subtitle">
                        استمتع بجميع ميزات منصة تقفي التعليمية مع باقاتنا المرنة والمناسبة لجميع الاحتياجات
                    </p>
                    <div class="hero-buttons">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-light btn-lg rounded-pill me-3">
                            <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                        </a>
                        <a href="#pricing-section" class="btn btn-light btn-lg rounded-pill scroll-to-plans me-3">
                            <i class="fas fa-star me-2"></i>باقاتنا المميزة
                        </a>
                        <a href="#chargily-payment" class="btn btn-success btn-lg rounded-pill scroll-to-chargily">
                            <i class="fas fa-credit-card me-2"></i>تمديد الفترة التجريبية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid py-5">
    <div class="row">
        <div class="col-md-12">

            <!-- معلومات الاشتراك الحالي -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8">
                    {% if user.subscription_type == 'free_trial' %}
                    <div class="current-subscription-card trial">
                        <div class="subscription-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="subscription-content">
                            <h4 class="subscription-title">الفترة التجريبية المجانية</h4>
                            <p class="subscription-details">
                                متبقي <span class="highlight">{{ user.subscription_days_remaining }} يوم</span> من
                                الفترة التجريبية المجانية.
                                {% if user.is_subscription_expiring_soon %}
                                <br><span class="warning-text"><i
                                        class="fas fa-exclamation-triangle me-1"></i><strong>تنبيه:</strong> ستنتهي
                                    فترتك التجريبية قريباً!</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    {% elif user.subscription_type == 'paid' and current_subscription %}
                    <div class="current-subscription-card active">
                        <div class="subscription-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="subscription-content">
                            <h4 class="subscription-title">اشتراك نشط</h4>
                            <p class="subscription-details">
                                الباقة: <span class="highlight">{{ current_subscription.plan.name }}</span><br>
                                ينتهي في: <span class="highlight">{{ current_subscription.end_date.strftime('%Y-%m-%d')
                                    }}</span><br>
                                متبقي <span class="highlight">{{ user.subscription_days_remaining }} يوم</span>
                                {% if user.is_subscription_expiring_soon %}
                                <br><span class="warning-text"><i
                                        class="fas fa-exclamation-triangle me-1"></i><strong>تنبيه:</strong> سينتهي
                                    اشتراكك قريباً!</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    {% elif user.subscription_type == 'expired' %}
                    <div class="current-subscription-card expired">
                        <div class="subscription-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="subscription-content">
                            <h4 class="subscription-title">انتهى الاشتراك</h4>
                            <p class="subscription-details">
                                يجب تجديد اشتراكك للوصول إلى الميزات المدفوعة.
                            </p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- باقات الاشتراك -->
            <div id="pricing-section" class="row justify-content-center">
                <div class="col-12 text-center mb-4">
                    <h2 class="section-title">
                        <i class="fas fa-star me-2"></i>
                        باقاتنا المميزة
                    </h2>
                    <p class="section-subtitle">اختر الباقة التي تناسب احتياجاتك التعليمية</p>
                    <div class="automatic-activation-notice">
                        <div class="notice-content">
                            <i class="fas fa-robot me-2"></i>
                            <span>تفعيل الحساب بهذه الطريقة يتم آلياً دون تدخل بشري من إدارة المستخدمين</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center mb-5">
                {% for plan in plans %}
                <div class="col-md-6 col-lg-3 mb-4">
                    <div
                        class="pricing-card {% if plan.is_institutional %}institutional{% else %}standard{% endif %} {% if current_subscription and current_subscription.plan_id == plan.id %}current-plan{% endif %}">
                        {% if current_subscription and current_subscription.plan_id == plan.id %}
                        <div class="current-badge">
                            <i class="fas fa-crown"></i>
                            الباقة الحالية
                        </div>
                        {% endif %}

                        <div class="pricing-header">
                            <div class="plan-icon">
                                {% if plan.is_institutional %}
                                <i class="fas fa-building"></i>
                                {% elif plan.duration_months == 1 %}
                                <i class="fas fa-calendar-alt"></i>
                                {% elif plan.duration_months == 3 %}
                                <i class="fas fa-calendar-check"></i>
                                {% elif plan.duration_months == 9 %}
                                <i class="fas fa-graduation-cap"></i>
                                {% endif %}
                            </div>
                            <h4 class="plan-name">{{ plan.name }}</h4>
                            <div class="plan-price">
                                {% if plan.is_institutional %}
                                <span class="price-text">اتصل بالإدارة</span>
                                <span class="price-subtitle">سعر قابل للتفاوض</span>
                                {% else %}
                                <span class="price-amount">{{ "{:,.0f}".format(plan.price) }}</span>
                                <span class="price-currency">دج</span>
                                <span class="price-duration">لمدة {{ plan.duration_months }} شهر</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="pricing-body">
                            <p class="plan-description">{{ plan.description }}</p>

                            <div class="pricing-footer">
                                {% if plan.is_institutional %}
                                <div class="institutional-info">
                                    <p class="institutional-text">للمؤسسات الخاصة والنقابات المعتمدة</p>
                                    <button class="btn-pricing institutional" disabled>
                                        <i class="fas fa-phone me-2"></i>اتصل بالإدارة
                                    </button>
                                </div>
                                {% else %}
                                {% if user.subscription_type == 'expired' or user.is_subscription_expiring_soon %}
                                <a href="{{ url_for('subscription_checkout', plan_id=plan.id) }}"
                                    class="btn-pricing primary">
                                    <i class="fas fa-shopping-cart me-2"></i>اشترك الآن
                                </a>
                                {% elif user.subscription_type == 'free_trial' %}
                                <a href="{{ url_for('subscription_checkout', plan_id=plan.id) }}"
                                    class="btn-pricing secondary">
                                    <i class="fas fa-upgrade me-2"></i>ترقية الاشتراك
                                </a>
                                {% elif current_subscription and current_subscription.plan_id == plan.id %}
                                <button class="btn-pricing success" disabled>
                                    <i class="fas fa-check me-2"></i>الباقة الحالية
                                </button>
                                {% else %}
                                <a href="{{ url_for('subscription_checkout', plan_id=plan.id) }}"
                                    class="btn-pricing secondary">
                                    <i class="fas fa-exchange-alt me-2"></i>تغيير الباقة
                                </a>
                                {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- قسم تمديد الفترة التجريبية بالدفع السريع عبر Chargily -->
            <div id="chargily-payment" class="row justify-content-center mb-5">
                <div class="col-lg-10">
                    <div class="chargily-payment-section">
                        <div class="chargily-header">
                            <h2 class="chargily-title">
                                <i class="fas fa-bolt me-3"></i>
                                تمديد الفترة التجريبية بالدفع السريع عبر Chargily
                            </h2>
                            <p class="chargily-subtitle">
                                ادفع مباشرة عبر منصة Chargily الآمنة - سريع وموثوق ومضمون
                            </p>
                        </div>

                        <!-- ملاحظات مهمة -->
                        <div class="important-notes">
                            <div class="important-notes-header">
                                <h3 class="important-notes-title">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    مهم جداً
                                </h3>
                            </div>

                            <div class="important-notes-content">
                                <div class="note-item">
                                    <div class="note-number">1</div>
                                    <div class="note-content">
                                        <p><strong>قبل القيام بعملية الدفع</strong></p>
                                        <p>تأكد من أنك على تواصل مباشر وفعلي مع إدارة المستخدمين:</p>
                                        <div class="contact-buttons">
                                            <a href="https://wa.me/213540809687" target="_blank" class="contact-btn whatsapp">
                                                <i class="fab fa-whatsapp me-2"></i>
                                                واتساب
                                            </a>
                                            <a href="https://m.me/113844974919183" target="_blank" class="contact-btn messenger">
                                                <i class="fab fa-facebook-messenger me-2"></i>
                                                مسنجر
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="note-item">
                                    <div class="note-number">2</div>
                                    <div class="note-content">
                                        <p>امنح إدارة مستخدمي موقع تقفي <strong>رقم هاتفك الذي سجلت به</strong> ليتمكنوا من تحديد حسابك وتفعيله</p>
                                    </div>
                                </div>

                                <div class="note-item">
                                    <div class="note-number">3</div>
                                    <div class="note-content">
                                        <p>بعد إنجازك لعملية دفع ناجحة ستمنحك منصة شارجيلي <strong>وصلاً يثبت نجاح عملية الدفع</strong></p>
                                        <p>قم بحفظه أو قم بـ capture له وأرسله لنا لتثبت عملية الدفع</p>
                                    </div>
                                </div>

                                <div class="note-item">
                                    <div class="note-number">4</div>
                                    <div class="note-content">
                                        <p>ستضاف الأيام الخاصة بالباقة لحسابك</p>
                                        <p>يمكنك الإطلاع على عدد الأيام المفعلة من <strong>الملف الشخصي لحسابك</strong></p>
                                    </div>
                                </div>

                                <div class="note-item">
                                    <div class="note-number">5</div>
                                    <div class="note-content">
                                        <p>قد تستغرق عملية التفعيل وقتاً إذا كان الضغط على خدمة العملاء</p>
                                        <p><strong>نرجو تفهم ذلك</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="chargily-plans-grid">
                            <!-- تمديد 7 أيام -->
                            <div class="chargily-plan-card weekly">
                                <div class="chargily-plan-header">
                                    <div class="chargily-plan-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h4 class="chargily-plan-name">تمديد 7 أيام</h4>
                                    <div class="chargily-plan-price">
                                        <span class="price-amount">400</span>
                                        <span class="price-currency">دج</span>
                                        <span class="price-duration">7 أيام إضافية</span>
                                    </div>
                                </div>
                                <div class="chargily-plan-body">
                                    <p class="chargily-plan-description">
                                        تمديد سريع للفترة التجريبية - مثالي للاختبار السريع
                                    </p>
                                    <a href="http://pay.chargily.com/payment-links/01k3gzanwp108e4jsbk8m52n1h"
                                       target="_blank"
                                       class="btn-chargily weekly">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        ادفع الآن عبر Chargily
                                    </a>
                                </div>
                            </div>

                            <!-- تمديد 15 يوم -->
                            <div class="chargily-plan-card biweekly featured">
                                <div class="featured-badge">
                                    <i class="fas fa-star"></i>
                                    الأكثر شعبية
                                </div>
                                <div class="chargily-plan-header">
                                    <div class="chargily-plan-icon">
                                        <i class="fas fa-calendar-plus"></i>
                                    </div>
                                    <h4 class="chargily-plan-name">تمديد 15 يوم</h4>
                                    <div class="chargily-plan-price">
                                        <span class="price-amount">600</span>
                                        <span class="price-currency">دج</span>
                                        <span class="price-duration">15 يوم إضافي</span>
                                        <span class="price-save">توفير 200 دج</span>
                                    </div>
                                </div>
                                <div class="chargily-plan-body">
                                    <p class="chargily-plan-description">
                                        الخيار الأمثل للتمديد - توازن مثالي بين السعر والمدة
                                    </p>
                                    <a href="http://pay.chargily.com/payment-links/01k3gzccenasydtr0xprfg7bq1"
                                       target="_blank"
                                       class="btn-chargily biweekly">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        ادفع الآن عبر Chargily
                                    </a>
                                </div>
                            </div>

                            <!-- تمديد 21 يوم -->
                            <div class="chargily-plan-card triweekly">
                                <div class="chargily-plan-header">
                                    <div class="chargily-plan-icon">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                    <h4 class="chargily-plan-name">تمديد 21 يوم</h4>
                                    <div class="chargily-plan-price">
                                        <span class="price-amount">800</span>
                                        <span class="price-currency">دج</span>
                                        <span class="price-duration">21 يوم إضافي</span>
                                        <span class="price-save">توفير 400 دج</span>
                                    </div>
                                </div>
                                <div class="chargily-plan-body">
                                    <p class="chargily-plan-description">
                                        تمديد طويل للفترة التجريبية - أفضل قيمة للاستكشاف المتعمق
                                    </p>
                                    <a href="http://pay.chargily.com/payment-links/01k3gzd4pmnqsqzhez53535ztz"
                                       target="_blank"
                                       class="btn-chargily triweekly">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        ادفع الآن عبر Chargily
                                    </a>
                                </div>
                            </div>

                            <!-- المؤسسات الخاصة -->
                            <div class="chargily-plan-card institutional">
                                <div class="chargily-plan-header">
                                    <div class="chargily-plan-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <h4 class="chargily-plan-name">المؤسسات الخاصة</h4>
                                    <div class="chargily-plan-price">
                                        <span class="price-text">اتصل بإدارة موقعنا</span>
                                        <span class="price-subtitle">للمؤسسات الخاصة والنقابات المعتمدة</span>
                                    </div>
                                </div>
                                <div class="chargily-plan-body">
                                    <p class="chargily-plan-description">
                                        للمؤسسات الخاصة والنقابات المعتمدة - أسعار خاصة ومرونة في الدفع
                                    </p>
                                    <a href="https://m.me/113844974919183"
                                       target="_blank"
                                       class="btn-chargily institutional">
                                        <i class="fab fa-facebook-messenger me-2"></i>
                                        تواصل عبر Messenger
                                    </a>
                                </div>
                            </div>
                        </div>



                        <div class="chargily-features">
                            <div class="chargily-feature">
                                <i class="fas fa-shield-alt"></i>
                                <span>دفع آمن ومضمون</span>
                            </div>
                            <div class="chargily-feature">
                                <i class="fas fa-clock"></i>
                                <span>تفعيل سريع</span>
                            </div>
                            <div class="chargily-feature">
                                <i class="fas fa-credit-card"></i>
                                <span>جميع وسائل الدفع</span>
                            </div>
                            <div class="chargily-feature">
                                <i class="fas fa-headset"></i>
                                <span>دعم فني متاح</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- معلومات إضافية -->
            <div class="row justify-content-center mt-5">
                <div class="col-lg-10">
                    <div class="info-section">
                        <div class="info-header">
                            <h3 class="info-title">
                                <i class="fas fa-shield-alt me-2"></i>
                                لماذا تختار منصة تقفي؟
                            </h3>
                        </div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <div class="info-content">
                                    <h5>شهر مجاني</h5>
                                    <p>جميع المستخدمين الجدد يحصلون على شهر مجاني</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <div class="info-content">
                                    <h5>دفع آمن</h5>
                                    <p>الدفع آمن ومضمون عبر بوابة Chargily</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-undo"></i>
                                </div>
                                <div class="info-content">
                                    <h5>مرونة كاملة</h5>
                                    <p>يمكن إلغاء الاشتراك في أي وقت</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-headset"></i>
                                </div>
                                <div class="info-content">
                                    <h5>دعم فني</h5>
                                    <p>دعم فني متاح على مدار الساعة</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="info-content">
                                    <h5>باقات مؤسسية</h5>
                                    <p>للمؤسسات الخاصة والنقابات المعتمدة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 80px 0;
        margin-bottom: 0;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        color: white;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .hero-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .hero-buttons .btn {
        transition: all 0.3s ease;
    }

    .hero-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .animated-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    /* Current Subscription Card */
    .current-subscription-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 1.5rem;
        border-left: 5px solid;
        transition: transform 0.3s ease;
    }

    .current-subscription-card:hover {
        transform: translateY(-5px);
    }

    .current-subscription-card.trial {
        border-left-color: #17a2b8;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    }

    .current-subscription-card.active {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    }

    .current-subscription-card.expired {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    }

    .subscription-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        flex-shrink: 0;
    }

    .trial .subscription-icon {
        background: linear-gradient(135deg, #17a2b8, #138496);
    }

    .active .subscription-icon {
        background: linear-gradient(135deg, #28a745, #1e7e34);
    }

    .expired .subscription-icon {
        background: linear-gradient(135deg, #dc3545, #c82333);
    }

    .subscription-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    .subscription-details {
        margin: 0;
        color: #6c757d;
        line-height: 1.6;
    }

    .highlight {
        font-weight: 600;
        color: #495057;
    }

    .warning-text {
        color: #e67e22 !important;
        font-weight: 500;
    }

    /* Chargily Payment Section */
    .chargily-payment-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 25px;
        padding: 3rem 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
    }

    .chargily-payment-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        opacity: 0.5;
    }

    .chargily-header {
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
        z-index: 2;
    }

    .chargily-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .chargily-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
        line-height: 1.6;
    }

    .chargily-plans-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
        position: relative;
        z-index: 2;
    }

    .chargily-plan-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        color: #2c3e50;
    }

    .chargily-plan-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
    }

    .chargily-plan-card.featured {
        transform: scale(1.05);
        border: 3px solid #ffd700;
    }

    .featured-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background: linear-gradient(135deg, #ffd700, #ffb300);
        color: #2c3e50;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 10;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
    }

    .chargily-plan-header {
        padding: 2rem 1.5rem 1rem;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .chargily-plan-icon {
        width: 70px;
        height: 70px;
        margin: 0 auto 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
    }

    .chargily-plan-name {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2c3e50;
    }

    .chargily-plan-price .price-amount {
        font-size: 2.2rem;
        font-weight: 800;
        color: #667eea;
        display: block;
    }

    .chargily-plan-price .price-currency {
        font-size: 1rem;
        font-weight: 600;
        color: #6c757d;
        margin-left: 0.5rem;
    }

    .chargily-plan-price .price-duration {
        display: block;
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }

    .chargily-plan-price .price-save {
        display: block;
        font-size: 0.8rem;
        color: #28a745;
        font-weight: 600;
        margin-top: 0.3rem;
    }

    .chargily-plan-price .price-text {
        font-size: 1.6rem;
        font-weight: 700;
        color: #667eea;
        display: block;
    }

    .chargily-plan-price .price-subtitle {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 0.5rem;
        display: block;
    }

    .chargily-plan-body {
        padding: 1.5rem;
    }

    .chargily-plan-description {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .btn-chargily {
        display: block;
        width: 100%;
        padding: 12px 20px;
        border: none;
        border-radius: 50px;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        font-size: 0.95rem;
        color: white;
    }

    .btn-chargily.weekly {
        background: linear-gradient(135deg, #17a2b8, #138496);
    }

    .btn-chargily.biweekly {
        background: linear-gradient(135deg, #28a745, #1e7e34);
    }

    .btn-chargily.triweekly {
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .btn-chargily.institutional {
        background: linear-gradient(135deg, #f093fb, #f5576c);
    }

    .btn-chargily:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .chargily-features {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2rem;
        flex-wrap: wrap;
        position: relative;
        z-index: 2;
    }

    .chargily-feature {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        opacity: 0.9;
    }

    .chargily-feature i {
        font-size: 1.1rem;
    }

    /* Important Notes Section */
    .important-notes {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 2px solid #ffc107;
        border-radius: 20px;
        padding: 2rem;
        margin: 3rem 0;
        position: relative;
        z-index: 2;
    }

    .important-notes-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .important-notes-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #856404;
        margin-bottom: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .important-notes-content {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .note-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        background: rgba(255, 255, 255, 0.8);
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid #ffc107;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .note-item:hover {
        transform: translateX(5px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .note-number {
        width: 35px;
        height: 35px;
        background: linear-gradient(135deg, #ffc107, #ffb300);
        color: #856404;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.1rem;
        flex-shrink: 0;
        box-shadow: 0 3px 10px rgba(255, 193, 7, 0.4);
    }

    .note-content {
        flex: 1;
        color: #856404;
        line-height: 1.6;
    }

    .note-content p {
        margin-bottom: 0.8rem;
    }

    .note-content p:last-child {
        margin-bottom: 0;
    }

    .note-content strong {
        color: #6c5ce7;
        font-weight: 600;
    }

    .contact-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }

    .contact-btn {
        display: inline-flex;
        align-items: center;
        padding: 10px 20px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        color: white;
    }

    .contact-btn.whatsapp {
        background: linear-gradient(135deg, #25d366, #128c7e);
    }

    .contact-btn.whatsapp:hover {
        background: linear-gradient(135deg, #128c7e, #075e54);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
        color: white;
        text-decoration: none;
    }

    .contact-btn.messenger {
        background: linear-gradient(135deg, #0084ff, #0066cc);
    }

    .contact-btn.messenger:hover {
        background: linear-gradient(135deg, #0066cc, #004499);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 132, 255, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Section Titles */
    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 1.5rem;
    }

    /* Automatic Activation Notice */
    .automatic-activation-notice {
        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        border: 2px solid #28a745;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem auto;
        max-width: 600px;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
        position: relative;
        overflow: hidden;
    }

    .automatic-activation-notice::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="check-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(40,167,69,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23check-pattern)"/></svg>');
        opacity: 0.3;
    }

    .notice-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.8rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: #155724;
        text-align: center;
        line-height: 1.5;
    }

    .notice-content i {
        font-size: 1.3rem;
        color: #28a745;
        animation: robotPulse 2s infinite;
    }

    @keyframes robotPulse {
        0%, 100% {
            transform: scale(1);
            color: #28a745;
        }
        50% {
            transform: scale(1.1);
            color: #20c997;
        }
    }

    /* Pricing Cards */
    .pricing-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .pricing-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    .pricing-card.current-plan {
        border: 3px solid #ffd700;
        transform: scale(1.05);
    }

    .current-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background: linear-gradient(135deg, #ffd700, #ffb300);
        color: #2c3e50;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 10;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
    }

    .pricing-header {
        padding: 2rem 1.5rem 1rem;
        text-align: center;
        position: relative;
    }

    .standard .pricing-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .institutional .pricing-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .plan-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
    }

    .plan-name {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .plan-price {
        margin-bottom: 0;
    }

    .price-amount {
        font-size: 2.5rem;
        font-weight: 800;
        display: block;
    }

    .price-currency {
        font-size: 1.2rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .price-duration {
        display: block;
        font-size: 0.9rem;
        opacity: 0.8;
        margin-top: 0.5rem;
    }

    .price-text {
        font-size: 1.8rem;
        font-weight: 700;
        display: block;
    }

    .price-subtitle {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-top: 0.5rem;
        display: block;
    }

    .pricing-body {
        padding: 1.5rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .plan-description {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 2rem;
        flex-grow: 1;
    }

    .pricing-footer {
        padding: 0 1.5rem 1.5rem;
        margin-top: auto;
    }

    .btn-pricing {
        display: block;
        width: 100%;
        padding: 12px 24px;
        border: none;
        border-radius: 50px;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        font-size: 1rem;
        cursor: pointer;
    }

    .btn-pricing.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-pricing.primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-pricing.secondary {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .btn-pricing.secondary:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
    }

    .btn-pricing.success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        cursor: not-allowed;
    }

    .btn-pricing.institutional {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        cursor: not-allowed;
    }

    .institutional-info {
        text-align: center;
    }

    .institutional-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    /* Info Section */
    .info-section {
        background: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    }

    .info-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .info-title {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .info-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        border-radius: 15px;
        background: #f8f9fa;
        transition: transform 0.3s ease;
    }

    .info-item:hover {
        transform: translateY(-5px);
        background: #e9ecef;
    }

    .info-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .info-content h5 {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .info-content p {
        color: #6c757d;
        margin: 0;
        line-height: 1.5;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .hero-buttons {
            flex-direction: column;
            gap: 0.5rem;
        }

        .hero-buttons .btn {
            width: 100%;
            max-width: 300px;
        }

        .section-title {
            font-size: 2rem;
        }

        .current-subscription-card {
            flex-direction: column;
            text-align: center;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .pricing-card.current-plan {
            transform: none;
        }

        .chargily-plan-card.featured {
            transform: none;
        }

        .chargily-plans-grid {
            grid-template-columns: 1fr;
        }

        .chargily-features {
            flex-direction: column;
            gap: 1rem;
        }

        .chargily-title {
            font-size: 2rem;
        }

        .chargily-subtitle {
            font-size: 1rem;
        }

        .chargily-payment-section {
            padding: 2rem 1rem;
        }

        .important-notes {
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .important-notes-title {
            font-size: 1.5rem;
        }

        .note-item {
            flex-direction: column;
            text-align: center;
            gap: 0.8rem;
        }

        .note-number {
            align-self: center;
        }

        .contact-buttons {
            justify-content: center;
        }

        .contact-btn {
            flex: 1;
            min-width: 120px;
            justify-content: center;
        }

        .automatic-activation-notice {
            margin: 1.5rem auto;
            padding: 1rem;
        }

        .notice-content {
            font-size: 1rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .notice-content i {
            font-size: 1.5rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // التمرير السلس للباقات المتاحة
        const scrollButton = document.querySelector('.scroll-to-plans');
        if (scrollButton) {
            scrollButton.addEventListener('click', function (e) {
                e.preventDefault();
                const targetSection = document.querySelector('#pricing-section');
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        }

        // التمرير السلس لقسم Chargily
        const chargilyButton = document.querySelector('.scroll-to-chargily');
        if (chargilyButton) {
            chargilyButton.addEventListener('click', function (e) {
                e.preventDefault();
                const targetSection = document.querySelector('#chargily-payment');
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        }

        // تأثيرات بصرية للبطاقات
        const chargilyCards = document.querySelectorAll('.chargily-plan-card');
        chargilyCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                if (this.classList.contains('featured')) {
                    this.style.transform = 'scale(1.05)';
                } else {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            });
        });
    });
</script>
{% endblock %}