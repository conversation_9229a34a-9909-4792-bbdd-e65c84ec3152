#!/usr/bin/env python3
"""
فحص الحالة النهائية للمستخدم splintergags
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models_new import User

def check_final_status():
    """فحص الحالة النهائية"""
    with app.app_context():
        print("🔍 الحالة النهائية للمستخدم splintergags")
        print("=" * 60)
        
        user = User.query.filter_by(username='splintergags').first()
        if not user:
            print("❌ المستخدم غير موجود")
            return
        
        print(f"👤 المستخدم: {user.username}")
        print(f"📊 حالة الاشتراك: {user.subscription_status}")
        print(f"🔄 نوع الاشتراك: {user.subscription_type}")
        print(f"💼 لديه اشتراك نشط: {user.has_active_subscription}")
        print(f"🆓 انتهاء الفترة التجريبية: {user.free_trial_end}")
        
        current_subscription = user.current_subscription
        if current_subscription:
            print(f"\n📋 الاشتراك الحالي:")
            print(f"    - ID: {current_subscription.id}")
            print(f"    - الباقة: {current_subscription.plan.name}")
            print(f"    - البداية: {current_subscription.start_date}")
            print(f"    - النهاية: {current_subscription.end_date}")
            print(f"    - نشط: {current_subscription.is_active}")
            print(f"    - فترة تجريبية: {current_subscription.is_free_trial}")
            print(f"    - الأيام المتبقية: {current_subscription.days_remaining}")
            
            print(f"\n✅ النتيجة:")
            if (user.subscription_status == 'active' and 
                user.subscription_type == 'paid' and
                user.has_active_subscription and
                user.free_trial_end is None and
                current_subscription.is_active and
                not current_subscription.is_free_trial):
                print(f"🎉 تم الإصلاح بنجاح!")
                print(f"📱 المستخدم سيرى الآن:")
                print(f"   - اشتراك مدفوع (ليس فترة تجريبية)")
                print(f"   - {current_subscription.days_remaining} يوم متبقي")
                print(f"   - حالة نشطة")
            else:
                print(f"⚠️ لا تزال هناك مشاكل")
        else:
            print(f"\n❌ لا يوجد اشتراك حالي")

if __name__ == "__main__":
    check_final_status()
