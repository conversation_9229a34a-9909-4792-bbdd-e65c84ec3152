@echo off
chcp 65001 >nul
echo 🚀 تثبيت متطلبات Ta9affi على Windows
echo =====================================

:: فحص Python
echo 🐍 فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 💡 قم بتثبيت Python 3.8+ من: https://python.org
    pause
    exit /b 1
)

python --version
echo ✅ Python موجود

:: فحص pip
echo 🔄 فحص pip...
python -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip غير متاح
    pause
    exit /b 1
)

echo ✅ pip موجود

:: ترقية pip
echo 🔄 ترقية pip...
python -m pip install --upgrade pip

:: إنشاء البيئة الافتراضية
echo 🔄 إنشاء البيئة الافتراضية...
if not exist "venv" (
    python -m venv venv
    echo ✅ تم إنشاء البيئة الافتراضية
) else (
    echo ✅ البيئة الافتراضية موجودة
)

:: تفعيل البيئة الافتراضية
echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

:: تثبيت المتطلبات
echo 🔄 تثبيت المتطلبات...
if exist "requirements.txt" (
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت بعض المتطلبات
        echo 💡 جرب تشغيل الأمر يدوياً
    ) else (
        echo ✅ تم تثبيت المتطلبات بنجاح
    )
) else (
    echo ⚠️ ملف requirements.txt غير موجود
    echo 🔄 تثبيت المكتبات الأساسية...
    python -m pip install flask flask-sqlalchemy flask-login flask-wtf werkzeug pandas openpyxl chargily-pay schedule pymysql psycopg2-binary redis flask-session flask-limiter flask-talisman gunicorn gevent flask-migrate python-dotenv requests
)

:: إنشاء المجلدات
echo 🔄 إنشاء المجلدات المطلوبة...
if not exist "instance" mkdir instance
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "static\exports" mkdir static\exports
echo ✅ تم إنشاء المجلدات

:: إنشاء ملف .env
echo 🔄 إنشاء ملف .env...
if not exist ".env" (
    echo # إعدادات Flask > .env
    echo FLASK_ENV=development >> .env
    echo SECRET_KEY=dev-secret-key-change-in-production >> .env
    echo. >> .env
    echo # إعدادات قاعدة البيانات >> .env
    echo DATABASE_URL=sqlite:///instance/ta9affi.db >> .env
    echo. >> .env
    echo # إعدادات Redis >> .env
    echo REDIS_URL=redis://localhost:6379/0 >> .env
    echo. >> .env
    echo # إعدادات Chargily >> .env
    echo CHARGILY_PUBLIC_KEY=your-public-key >> .env
    echo CHARGILY_SECRET_KEY=your-secret-key >> .env
    echo BASE_URL=http://127.0.0.1:5000 >> .env
    echo CHARGILY_WEBHOOK_URL=http://127.0.0.1:5000/chargily-webhook >> .env
    echo. >> .env
    echo # إعدادات السجلات >> .env
    echo LOG_LEVEL=DEBUG >> .env
    echo LOG_FILE=logs/ta9affi.log >> .env
    echo ✅ تم إنشاء ملف .env
) else (
    echo ✅ ملف .env موجود
)

:: فحص Redis
echo 🔄 فحص Redis...
python -c "import redis; r=redis.Redis(); r.ping(); print('✅ Redis يعمل')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ Redis غير متاح
    echo 💡 لتشغيل Redis:
    echo    - استخدم Docker: docker run -d -p 6379:6379 redis:7-alpine
    echo    - أو قم بتثبيت Redis for Windows
    echo    - أو استخدم WSL
)

:: اختبار الاستيراد
echo 🔄 اختبار المكتبات...
python -c "
try:
    import flask, flask_sqlalchemy, flask_login, pandas, openpyxl, redis, requests
    print('✅ جميع المكتبات الأساسية مثبتة')
except ImportError as e:
    print(f'❌ مكتبة مفقودة: {e}')
"

echo.
echo 🎉 انتهى التثبيت!
echo.
echo 📋 الخطوات التالية:
echo 1. تأكد من تشغيل Redis server
echo 2. قم بتشغيل: python init_database.py
echo 3. قم بتشغيل: python run.py
echo 4. افتح المتصفح على: http://127.0.0.1:5000
echo.
echo 💡 لتفعيل البيئة الافتراضية في المرات القادمة:
echo    venv\Scripts\activate.bat
echo.
pause
