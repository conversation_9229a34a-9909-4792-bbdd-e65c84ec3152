# دليل التثبيت السريع - Ta9affi

## 🚀 التثبيت التلقائي (الطريقة الأسهل)

### على Windows:
```bash
# تشغيل سكريپت التثبيت التلقائي
install_windows.bat
```

### على Linux/macOS:
```bash
# إعطاء صلاحية التنفيذ
chmod +x install_linux.sh

# تشغيل سكريپت التثبيت
./install_linux.sh
```

### باستخدام Python:
```bash
# تشغيل سكريپت Python للتثبيت
python install_requirements.py
```

---

## 📋 التثبيت اليدوي السريع

### 1. المتطلبات الأساسية:
- Python 3.8+ 
- pip
- Redis Server

### 2. تثبيت المكتبات:
```bash
pip install -r requirements.txt
```

### 3. تشغيل Redis:
```bash
# Windows (Docker)
docker run -d -p 6379:6379 redis:7-alpine

# Linux
sudo systemctl start redis

# macOS
brew services start redis
```

### 4. تهيئة قاعدة البيانات:
```bash
python init_database.py
```

### 5. تشغيل التطبيق:
```bash
python run.py
```

### 6. فتح التطبيق:
افتح المتصفح على: http://127.0.0.1:5000

---

## 🔧 قائمة المكتبات المطلوبة

### المكتبات الأساسية:
- `flask>=2.3.0` - إطار العمل الرئيسي
- `flask-sqlalchemy>=3.0.0` - قاعدة البيانات
- `flask-login>=0.6.0` - نظام تسجيل الدخول
- `pandas>=1.5.0` - معالجة البيانات
- `openpyxl>=3.1.0` - ملفات Excel
- `redis>=4.5.0` - التخزين المؤقت

### مكتبات الأمان:
- `flask-limiter>=3.3.0` - تحديد معدل الطلبات
- `flask-talisman>=1.1.0` - الأمان
- `flask-wtf>=1.1.0` - حماية النماذج

### مكتبات الإنتاج:
- `gunicorn>=20.1.0` - خادم الإنتاج
- `gevent>=22.10.0` - التزامن العالي

### مكتبات إضافية:
- `chargily-pay` - نظام الدفع
- `schedule>=1.2.0` - جدولة المهام
- `requests>=2.28.0` - طلبات HTTP

---

## ⚠️ استكشاف الأخطاء

### مشكلة Redis:
```bash
# فحص Redis
redis-cli ping

# إعادة تشغيل Redis
sudo systemctl restart redis  # Linux
brew services restart redis   # macOS
```

### مشكلة psycopg2:
```bash
pip install psycopg2-binary
```

### مشكلة gevent على Windows:
```bash
# تثبيت Visual Studio Build Tools أولاً
pip install gevent --no-cache-dir
```

---

## 🐳 التشغيل باستخدام Docker

### تشغيل سريع:
```bash
# بناء وتشغيل
docker-compose up --build

# في الخلفية
docker-compose up -d
```

### فقط Redis:
```bash
docker run -d -p 6379:6379 redis:7-alpine
```

---

## 📁 هيكل المشروع

```
ta9affi/
├── app.py                 # التطبيق الرئيسي
├── config.py             # الإعدادات
├── models_new.py         # نماذج قاعدة البيانات
├── requirements.txt      # المكتبات المطلوبة
├── run.py               # ملف التشغيل
├── instance/            # قاعدة البيانات
├── static/              # الملفات الثابتة
├── templates/           # قوالب HTML
└── logs/               # ملفات السجلات
```

---

## 🔐 متغيرات البيئة

إنشاء ملف `.env`:
```env
FLASK_ENV=development
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///instance/ta9affi.db
REDIS_URL=redis://localhost:6379/0
CHARGILY_PUBLIC_KEY=your-public-key
CHARGILY_SECRET_KEY=your-secret-key
BASE_URL=http://127.0.0.1:5000
```

---

## 🎯 اختبار التثبيت

```bash
# فحص Python
python --version

# فحص المكتبات
python -c "import flask, redis, pandas; print('✅ جميع المكتبات مثبتة')"

# فحص Redis
redis-cli ping

# فحص التطبيق
curl http://127.0.0.1:5000
```

---

## 📞 الدعم

إذا واجهت مشاكل في التثبيت:

1. تأكد من إصدار Python (3.8+)
2. تأكد من تشغيل Redis
3. تأكد من تثبيت build tools
4. جرب التثبيت في بيئة افتراضية جديدة

---

## 🚀 البدء السريع

```bash
# 1. استنساخ المشروع
git clone <repository-url>
cd ta9affi

# 2. تشغيل سكريپت التثبيت
./install_linux.sh    # Linux/macOS
install_windows.bat   # Windows

# 3. تشغيل التطبيق
python run.py

# 4. فتح المتصفح
# http://127.0.0.1:5000
```

🎉 **مبروك! التطبيق جاهز للاستخدام**
