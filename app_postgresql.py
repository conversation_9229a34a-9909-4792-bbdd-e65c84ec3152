#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Ta9affi المحدث لدعم PostgreSQL
مع الحفاظ على جميع الخصائص الحالية
"""

import os
import logging
from flask import Flask
from flask_migrate import Migrate
from config import get_config
from models_new import db
import redis

# إنشاء مثيل Flask
def create_app(config_name=None):
    """إنشاء وتكوين تطبيق Flask"""
    
    app = Flask(__name__)
    
    # تحديد بيئة التشغيل
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # تحميل الإعدادات
    config_class = get_config()
    app.config.from_object(config_class)
    
    # تهيئة الإعدادات الخاصة بالتطبيق
    config_class.init_app(app)
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    # تهيئة Flask-Migrate
    migrate = Migrate(app, db)
    
    # تهيئة Redis (إذا كان متاحاً)
    try:
        redis_client = redis.from_url(app.config['REDIS_URL'])
        redis_client.ping()
        app.redis = redis_client
        app.logger.info("✅ تم الاتصال بـ Redis بنجاح")
    except Exception as e:
        app.logger.warning(f"⚠️ لم يتم الاتصال بـ Redis: {str(e)}")
        app.redis = None
    
    # تهيئة نظام التسجيل
    setup_logging(app)
    
    # تسجيل معلومات التطبيق
    app.logger.info(f"🚀 تم تشغيل Ta9affi في بيئة: {config_name}")
    app.logger.info(f"🗄️ قاعدة البيانات: {app.config['SQLALCHEMY_DATABASE_URI'][:50]}...")
    
    return app

def setup_logging(app):
    """إعداد نظام التسجيل"""
    
    if not app.debug and not app.testing:
        # إنشاء مجلد السجلات
        log_dir = os.path.dirname(app.config.get('LOG_FILE', 'logs/ta9affi.log'))
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # إعداد معالج الملف
        file_handler = logging.FileHandler(app.config.get('LOG_FILE', 'logs/ta9affi.log'))
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.addHandler(file_handler)
        
        # إعداد معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s'
        ))
        console_handler.setLevel(logging.INFO)
        app.logger.addHandler(console_handler)
        
        app.logger.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.info('🔧 تم تهيئة نظام التسجيل')

# إنشاء التطبيق
app = create_app()

# استيراد النماذج والمسارات (بعد إنشاء التطبيق)
with app.app_context():
    # استيراد جميع النماذج للتأكد من تسجيلها مع SQLAlchemy
    from models_new import (
        User, Role, RoleSettings, EducationalLevel, Subject, Domain, 
        KnowledgeMaterial, Competency, Schedule, ProgressEntry, 
        LevelDatabase, LevelDataEntry, AdminInspectorNotification, 
        InspectorTeacherNotification, GeneralNotification, 
        GeneralNotificationRead, SubscriptionPlan, Subscription, 
        Payment, SubscriptionNotification, SubscriptionExtensionLog, 
        UserSession, inspector_teacher
    )
    from news_model import NewsUpdate

# استيراد المسارات من التطبيق الأصلي
def register_routes():
    """تسجيل جميع المسارات من التطبيق الأصلي"""
    
    # هنا سنستورد جميع المسارات من app.py الأصلي
    # سنقوم بذلك في خطوة منفصلة للحفاظ على التنظيم
    pass

if __name__ == '__main__':
    with app.app_context():
        # إنشاء الجداول إذا لم تكن موجودة
        try:
            db.create_all()
            app.logger.info("✅ تم إنشاء/تحديث جداول قاعدة البيانات")
        except Exception as e:
            app.logger.error(f"❌ خطأ في إنشاء جداول قاعدة البيانات: {str(e)}")
    
    # تشغيل التطبيق
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 8000)),
        debug=app.config.get('DEBUG', False)
    )
