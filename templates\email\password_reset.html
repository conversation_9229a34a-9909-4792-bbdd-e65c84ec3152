<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - Ta9affi</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .email-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .email-header .icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .email-body {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .message {
            font-size: 16px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 30px;
        }
        
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }
        
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
            color: #856404;
        }
        
        .security-notice h3 {
            margin: 0 0 10px 0;
            color: #856404;
            font-size: 16px;
        }
        
        .security-notice ul {
            margin: 10px 0;
            padding-right: 20px;
        }
        
        .security-notice li {
            margin-bottom: 5px;
        }
        
        .alternative-link {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
            color: #495057;
        }
        
        .email-footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
        .email-footer p {
            margin: 5px 0;
            color: #6c757d;
            font-size: 14px;
        }
        
        .email-footer .logo {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .contact-info {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        .contact-info a {
            color: #667eea;
            text-decoration: none;
        }
        
        .contact-info a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .email-header, .email-body, .email-footer {
                padding: 20px;
            }
            
            .reset-button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <span class="icon">🔐</span>
            <h1>إعادة تعيين كلمة المرور</h1>
        </div>
        
        <div class="email-body">
            <div class="greeting">
                مرحباً {{ user.username }}،
            </div>
            
            <div class="message">
                تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في منصة Ta9affi.
                <br><br>
                إذا كنت قد طلبت إعادة تعيين كلمة المرور، يرجى النقر على الزر أدناه لإنشاء كلمة مرور جديدة:
            </div>
            
            <div class="button-container">
                <a href="{{ reset_url }}" class="reset-button">
                    إعادة تعيين كلمة المرور
                </a>
            </div>
            
            <div class="security-notice">
                <h3>⚠️ ملاحظات أمنية مهمة:</h3>
                <ul>
                    <li>هذا الرابط صالح لمدة ساعة واحدة فقط</li>
                    <li>يمكن استخدام الرابط مرة واحدة فقط</li>
                    <li>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة</li>
                    <li>لا تشارك هذا الرابط مع أي شخص آخر</li>
                </ul>
            </div>
            
            <div class="message">
                إذا لم يعمل الزر أعلاه، يمكنك نسخ الرابط التالي ولصقه في متصفحك:
            </div>
            
            <div class="alternative-link">
                {{ reset_url }}
            </div>
            
            <div class="message">
                إذا لم تطلب إعادة تعيين كلمة المرور، فلا داعي لاتخاذ أي إجراء. حسابك آمن ولن يتم تغيير كلمة المرور.
            </div>
        </div>
        
        <div class="email-footer">
            <div class="logo">Ta9affi</div>
            <p>منصة التكوين والتطوير المهني للأساتذة</p>
            <p>هذه رسالة تلقائية، يرجى عدم الرد عليها</p>
            
            <div class="contact-info">
                <p>للمساعدة والدعم الفني:</p>
                <p>
                    📧 <a href="mailto:<EMAIL>"><EMAIL></a>
                    <br>
                    🌐 <a href="https://ta9affi.dz">ta9affi.dz</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
