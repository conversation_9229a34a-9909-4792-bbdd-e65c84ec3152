#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جداول Rate Limiting في قاعدة البيانات
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_rate_limit_tables():
    """إنشاء جداول Rate Limiting"""
    
    print("🔧 إنشاء جداول Rate Limiting...")
    
    try:
        # استيراد النماذج
        from models_new import db
        from rate_limit_settings import RateLimitSettings, RateLimitHistory, UserRateLimitOverride
        
        # إنشاء التطبيق للسياق
        from app import app
        
        with app.app_context():
            # إنشاء الجداول
            print("📊 إنشاء جداول قاعدة البيانات...")
            
            # إنشاء جداول Rate Limiting
            RateLimitSettings.__table__.create(db.engine, checkfirst=True)
            print("✅ تم إنشاء جدول RateLimitSettings")
            
            RateLimitHistory.__table__.create(db.engine, checkfirst=True)
            print("✅ تم إنشاء جدول RateLimitHistory")
            
            UserRateLimitOverride.__table__.create(db.engine, checkfirst=True)
            print("✅ تم إنشاء جدول UserRateLimitOverride")
            
            # إنشاء الإعدادات الافتراضية
            print("\n⚙️ إنشاء الإعدادات الافتراضية...")
            
            existing_settings = RateLimitSettings.query.first()
            if not existing_settings:
                default_settings = RateLimitSettings(
                    add_progress_limit=10,
                    add_progress_window_hours=12,
                    delete_progress_limit=3,
                    delete_progress_window_hours=12,
                    login_attempts_limit=5,
                    login_attempts_window_hours=1,
                    file_upload_limit=20,
                    file_upload_window_hours=1,
                    notes="الإعدادات الافتراضية للنظام",
                    is_active=True
                )
                
                db.session.add(default_settings)
                db.session.commit()
                
                print("✅ تم إنشاء الإعدادات الافتراضية:")
                print(f"   - إضافة التقدمات: {default_settings.add_progress_limit} كل {default_settings.add_progress_window_hours} ساعة")
                print(f"   - حذف التقدمات: {default_settings.delete_progress_limit} كل {default_settings.delete_progress_window_hours} ساعة")
                
                # تسجيل في السجل
                RateLimitHistory.log_change(
                    admin_id=1,  # افتراض أن الأدمن له معرف 1
                    old_settings=None,
                    new_settings=default_settings.to_dict(),
                    reason="إنشاء الإعدادات الافتراضية للنظام",
                    change_type='create'
                )
                
            else:
                print("✅ الإعدادات موجودة مسبقاً")
                print(f"   - إضافة التقدمات: {existing_settings.add_progress_limit} كل {existing_settings.add_progress_window_hours} ساعة")
                print(f"   - حذف التقدمات: {existing_settings.delete_progress_limit} كل {existing_settings.delete_progress_window_hours} ساعة")
            
            print("\n🎉 تم إنشاء جداول Rate Limiting بنجاح!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rate_limit_settings():
    """اختبار إعدادات Rate Limiting"""
    
    print("\n🧪 اختبار إعدادات Rate Limiting...")
    
    try:
        from models_new import db
        from rate_limit_settings import RateLimitSettings, get_user_rate_limits
        from app import app
        
        with app.app_context():
            # اختبار الحصول على الإعدادات
            settings = RateLimitSettings.get_current_settings()
            print(f"✅ الإعدادات الحالية:")
            print(f"   - إضافة: {settings.add_progress_limit}/{settings.add_progress_window_hours}ساعة")
            print(f"   - حذف: {settings.delete_progress_limit}/{settings.delete_progress_window_hours}ساعة")
            
            # اختبار الحصول على حدود مستخدم
            user_limits = get_user_rate_limits(1)  # مستخدم تجريبي
            print(f"✅ حدود المستخدم 1:")
            print(f"   - إضافة: {user_limits['add_progress']['max_requests']}/{user_limits['add_progress']['window_hours']}ساعة")
            print(f"   - حذف: {user_limits['delete_progress']['max_requests']}/{user_limits['delete_progress']['window_hours']}ساعة")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def show_admin_instructions():
    """عرض تعليمات للأدمن"""
    
    print("\n📖 تعليمات للأدمن:")
    print("=" * 50)
    print("🎯 كيفية استخدام نظام إدارة Rate Limiting:")
    print()
    print("1. 🌐 الوصول للوحة الإدارة:")
    print("   - اذهب إلى لوحة تحكم الأدمن")
    print("   - ابحث عن 'إدارة حدود النظام' في أدوات الإدارة السريعة")
    print("   - أو اذهب مباشرة إلى: /admin/rate-limit-manager/")
    print()
    print("2. ⚙️ تغيير الإعدادات العامة:")
    print("   - حدد عدد الإضافات المسموحة (1-100)")
    print("   - حدد النافذة الزمنية بالساعات (1-168)")
    print("   - حدد عدد الحذف المسموح (1-50)")
    print("   - حدد النافذة الزمنية للحذف")
    print("   - اكتب ملاحظة عن سبب التغيير")
    print("   - اضغط 'حفظ الإعدادات'")
    print()
    print("3. 👤 تخصيص حدود لمستخدم محدد:")
    print("   - اذهب إلى 'تخصيص حدود المستخدمين'")
    print("   - اختر المستخدم من القائمة")
    print("   - حدد الحدود المخصصة (اتركها فارغة للافتراضي)")
    print("   - حدد تاريخ انتهاء (اختياري)")
    print("   - اكتب سبب التخصيص")
    print("   - اضغط 'حفظ التخصيص'")
    print()
    print("4. 📊 مراقبة النظام:")
    print("   - راجع الإحصائيات في اللوحة الرئيسية")
    print("   - راجع سجل التغييرات")
    print("   - استخدم 'إعادة تعيين جميع الحدود' عند الحاجة")
    print()
    print("5. 🔧 الإعدادات المقترحة:")
    print("   - للاستخدام العادي: إضافة 10/12ساعة، حذف 3/12ساعة")
    print("   - للاستخدام المكثف: إضافة 20/12ساعة، حذف 5/12ساعة")
    print("   - للاستخدام المحدود: إضافة 5/12ساعة، حذف 2/12ساعة")
    print()
    print("⚠️ ملاحظات مهمة:")
    print("   - التغييرات تُطبق فوراً على جميع المستخدمين")
    print("   - التخصيصات الشخصية تتفوق على الإعدادات العامة")
    print("   - جميع التغييرات مُسجلة في السجل")
    print("   - يمكن إعادة تعيين جميع الحدود في حالة الطوارئ")

def main():
    """الدالة الرئيسية"""
    
    print("🛡️ إعداد نظام إدارة Rate Limiting")
    print("=" * 50)
    print(f"⏰ وقت التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # إنشاء الجداول
    tables_created = create_rate_limit_tables()
    
    if tables_created:
        # اختبار النظام
        test_passed = test_rate_limit_settings()
        
        if test_passed:
            print("\n🎉 تم إعداد نظام إدارة Rate Limiting بنجاح!")
            
            # عرض التعليمات
            show_admin_instructions()
            
            print("\n🚀 الخطوات التالية:")
            print("1. شغل التطبيق: python app.py")
            print("2. سجل دخول كأدمن")
            print("3. اذهب إلى 'إدارة حدود النظام' في لوحة التحكم")
            print("4. اضبط الإعدادات حسب احتياجاتك")
            
        else:
            print("\n⚠️ فشل في اختبار النظام")
    else:
        print("\n❌ فشل في إنشاء الجداول")
    
    print("\n📞 للدعم:")
    print("   - تأكد من تشغيل قاعدة البيانات")
    print("   - راجع ملف QUICK_START_RATE_LIMITING.md")

if __name__ == "__main__":
    main()
