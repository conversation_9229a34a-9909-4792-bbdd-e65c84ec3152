@echo off
echo Starting Ta9affi Application...

REM Check if virtual environment exists
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Initialize database
echo Initializing database...
flask db init
flask db migrate -m "Initial migration"
flask db upgrade

REM Run the application
echo Starting the application...
python app.py

pause
