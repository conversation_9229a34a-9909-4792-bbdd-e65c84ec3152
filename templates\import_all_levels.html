{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12 mb-4">
            <h2>استيراد جميع المستويات</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <i class="fas fa-file-import me-1"></i> استيراد جميع المستويات والبيانات المرتبطة
                </div>
                <div class="card-body">
                    <p class="card-text">
                        استخدم هذه الصفحة لاستيراد جميع المستويات التعليمية والمواد الدراسية والميادين والمواد المعرفية
                        والكفاءات المستهدفة من ملف Excel واحد.
                    </p>

                    <div class="alert alert-info">
                        <h5 class="alert-heading"><i class="fas fa-info-circle me-1"></i> تنسيق الملف المطلوب</h5>
                        <p>يجب أن يحتوي ملف Excel على الأوراق التالية:</p>
                        <ul>
                            <li><strong>Levels</strong>: تحتوي على أسماء المستويات التعليمية</li>
                            <li><strong>Hierarchical_Data</strong>: تحتوي على البيانات الهرمية (المستوى، المادة،
                                الميدان/النشاط، الموارد المعرفية، الكفاءة)</li>
                        </ul>
                    </div>

                    <form action="{{ url_for('import_all_levels') }}" method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="file" class="form-label">ملف Excel</label>
                            <input type="file" class="form-control" id="file" name="file" accept=".xlsx" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-file-import me-1"></i> استيراد جميع المستويات
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('manage_databases') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> العودة إلى إدارة قواعد البيانات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}