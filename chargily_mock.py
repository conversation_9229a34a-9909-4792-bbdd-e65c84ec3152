#!/usr/bin/env python3
"""
محاكي Chargily للاختبار - يتجنب مشاكل SSL تماماً
"""

import uuid
import time
from datetime import datetime

class MockChargilyClient:
    """عميل Chargily محاكي للاختبار"""
    
    def __init__(self, public_key, secret_key, base_url=None):
        self.public_key = public_key
        self.secret_key = secret_key
        self.base_url = base_url or "https://pay.chargily.dz/api/v2"
        
        print("🧪 [MockChargily] تم تهيئة العميل المحاكي للاختبار")
        print("⚠️ [MockChargily] هذا للاختبار فقط - لا يتم دفع حقيقي!")
    
    def create_product(self, product_data):
        """محاكاة إنشاء منتج"""
        print(f"🧪 [MockChargily] محاكاة إنشاء منتج: {product_data.get('name', 'N/A')}")
        
        # محاكاة تأخير API
        time.sleep(0.5)
        
        # إرجاع استجابة محاكية
        return {
            "id": f"prod_{uuid.uuid4().hex[:8]}",
            "name": product_data.get('name', ''),
            "description": product_data.get('description', ''),
            "created_at": datetime.now().isoformat(),
            "status": "active"
        }
    
    def create_price(self, price_data):
        """محاكاة إنشاء سعر"""
        print(f"🧪 [MockChargily] محاكاة إنشاء سعر: {price_data.get('amount', 0)} {price_data.get('currency', 'DZD')}")
        
        # محاكاة تأخير API
        time.sleep(0.5)
        
        # إرجاع استجابة محاكية
        return {
            "id": f"price_{uuid.uuid4().hex[:8]}",
            "amount": price_data.get('amount', 0),
            "currency": price_data.get('currency', 'dzd'),
            "product_id": price_data.get('product_id', ''),
            "created_at": datetime.now().isoformat(),
            "status": "active"
        }
    
    def create_checkout(self, checkout_data):
        """محاكاة إنشاء checkout"""
        print(f"🧪 [MockChargily] محاكاة إنشاء checkout")
        
        # محاكاة تأخير API
        time.sleep(0.5)
        
        checkout_id = f"checkout_{uuid.uuid4().hex[:8]}"
        
        # إنشاء URL محاكي للدفع
        mock_payment_url = f"http://ta9affi.com/mock_payment?checkout_id={checkout_id}"
        
        print(f"🧪 [MockChargily] URL الدفع المحاكي: {mock_payment_url}")
        
        # إرجاع استجابة محاكية
        return {
            "id": checkout_id,
            "url": mock_payment_url,
            "items": checkout_data.get('items', []),
            "success_url": checkout_data.get('success_url', ''),
            "failure_url": checkout_data.get('failure_url', ''),
            "metadata": checkout_data.get('metadata', {}),
            "created_at": datetime.now().isoformat(),
            "status": "pending"
        }

def test_mock_chargily():
    """اختبار العميل المحاكي"""
    print("🧪 اختبار MockChargilyClient...")
    
    client = MockChargilyClient(
        "test_public_key",
        "test_secret_key"
    )
    
    # اختبار إنشاء منتج
    product = client.create_product({
        'name': "اختبار منتج Ta9affi",
        'description': "منتج اختبار"
    })
    print(f"✅ منتج محاكي: {product['id']}")
    
    # اختبار إنشاء سعر
    price = client.create_price({
        'amount': 1000,
        'currency': 'dzd',
        'product_id': product['id']
    })
    print(f"✅ سعر محاكي: {price['id']}")
    
    # اختبار إنشاء checkout
    checkout = client.create_checkout({
        'items': [{"price": price['id'], "quantity": 1}],
        'success_url': "http://ta9affi.com/payment/success",
        'failure_url': "http://ta9affi.com/payment/failure",
        'metadata': {"test": "true"}
    })
    print(f"✅ checkout محاكي: {checkout['id']}")
    print(f"🔗 URL الدفع: {checkout['url']}")
    
    return True

if __name__ == '__main__':
    test_mock_chargily()
