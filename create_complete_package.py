#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة Ta9affi الكاملة للنشر المباشر على السيرفر
"""

import os
import shutil
import subprocess
from datetime import datetime

def get_essential_files():
    """قائمة شاملة بجميع الملفات الأساسية"""
    
    # الملفات الأساسية للتطبيق
    core_files = [
        # ملفات Python الأساسية
        'app.py',                           # التطبيق الرئيسي
        'models_new.py',                    # نماذج قاعدة البيانات
        'news_model.py',                    # نموذج الأخبار
        'subscription_manager.py',          # إدارة الاشتراكات
        'subscription_scheduler.py',        # جدولة الاشتراكات
        
        # نظام Rate Limiting
        'rate_limiter.py',
        'rate_limit_monitor.py',
        'rate_limit_settings.py',
        'admin_rate_limit_manager.py',
        
        # ملفات الإعدادات
        'config.py',
        'config_production.py',
        
        # متطلبات النظام
        'requirements.txt',
        'requirements_production.txt',
        
        # ملفات الخادم
        'gunicorn.conf.py',
        'gunicorn_config.py',
        
        # ملفات Docker (للمرجع)
        'Dockerfile',
        'docker-compose.prod.yml',
        '.dockerignore',
        
        # ملفات البيانات
        'materials_data_updated.json',
        'educational_data.xlsx',
        
        # ملفات النظام
        'ta9affi_service.py',
        
        # ملفات أخرى مهمة
        'auth_security.py',
        'cache_manager.py',
        'session_manager.py',
        'security_manager.py',
        'file_security.py',
        'monitoring_system.py',
        'backup_manager.py',
        'redis_manager.py',
        
        # ملفات التحسين
        'database_optimization.py',
        'frontend_optimizer.py',
        'optimized_queries.py',
        
        # ملفات الإعداد
        '.env.example',
        'LICENSE.txt',
        'README.md',
    ]
    
    # المجلدات الأساسية
    essential_dirs = [
        'templates',                        # قوالب HTML
        'static',                          # الملفات الثابتة
        'instance',                        # قاعدة البيانات
        'nginx',                           # إعدادات Nginx
    ]
    
    # ملفات اختيارية (إذا وجدت)
    optional_files = [
        'alerting_system.py',
        'monitoring_dashboard.py',
        'load_testing.py',
        'production_quality_check.py',
        'nginx_ta9affi.conf',
        'backup_script.sh',
        'start_production.sh',
    ]
    
    return core_files, essential_dirs, optional_files

def check_files_exist():
    """التحقق من وجود الملفات"""
    
    core_files, essential_dirs, optional_files = get_essential_files()
    
    print("🔍 فحص الملفات الأساسية...")
    
    missing_files = []
    present_files = []
    
    # فحص الملفات الأساسية
    for file_name in core_files:
        if os.path.exists(file_name):
            present_files.append(file_name)
            print(f"✅ {file_name}")
        else:
            missing_files.append(file_name)
            print(f"❌ مفقود: {file_name}")
    
    # فحص المجلدات
    for dir_name in essential_dirs:
        if os.path.exists(dir_name):
            present_files.append(f"{dir_name}/")
            print(f"✅ {dir_name}/")
        else:
            missing_files.append(f"{dir_name}/")
            print(f"❌ مفقود: {dir_name}/")
    
    # فحص الملفات الاختيارية
    print("\n📋 الملفات الاختيارية:")
    for file_name in optional_files:
        if os.path.exists(file_name):
            present_files.append(file_name)
            print(f"✅ {file_name}")
        else:
            print(f"⚪ غير موجود: {file_name}")
    
    print(f"\n📊 النتيجة:")
    print(f"✅ الملفات الموجودة: {len(present_files)}")
    print(f"❌ الملفات المفقودة: {len(missing_files)}")
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة:")
        for file_name in missing_files:
            print(f"   - {file_name}")
    
    return len(missing_files) == 0, present_files, missing_files

def create_package():
    """إنشاء حزمة Ta9affi الكاملة"""
    
    print("📦 إنشاء حزمة Ta9affi الكاملة...")
    
    # التحقق من الملفات
    all_present, present_files, missing_files = check_files_exist()
    
    if not all_present:
        print("❌ لا يمكن إنشاء الحزمة - ملفات مفقودة!")
        return None
    
    # إنشاء اسم الحزمة
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    package_name = f"ta9affi_complete_{timestamp}.tar.gz"
    
    # قائمة الملفات للضغط
    core_files, essential_dirs, optional_files = get_essential_files()
    
    # إضافة الملفات الموجودة فقط
    files_to_include = []
    
    for file_name in core_files + optional_files:
        if os.path.exists(file_name):
            files_to_include.append(file_name)
    
    for dir_name in essential_dirs:
        if os.path.exists(dir_name):
            files_to_include.append(dir_name)
    
    # إنشاء الأمر
    cmd = ['tar', '-czf', package_name] + files_to_include
    
    try:
        print(f"🔄 إنشاء الحزمة: {package_name}")
        subprocess.run(cmd, check=True)
        
        # حساب حجم الملف
        size = os.path.getsize(package_name)
        size_mb = size / (1024 * 1024)
        
        print(f"✅ تم إنشاء الحزمة: {package_name}")
        print(f"📊 الحجم: {size_mb:.1f} MB")
        print(f"📁 الملفات المضمنة: {len(files_to_include)}")
        
        return package_name
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إنشاء الحزمة: {e}")
        return None

def show_deployment_instructions(package_name):
    """عرض تعليمات النشر"""
    
    print(f"\n🚀 تعليمات النشر المباشر:")
    print("=" * 60)
    
    print(f"\n1. 📁 رفع الحزمة للسيرفر:")
    print(f"   scp {package_name} root@*************:/opt/")
    
    print(f"\n2. 🖥️ على السيرفر:")
    print(f"   ssh root@*************")
    print(f"   cd /opt")
    print(f"   tar -xzf {package_name}")
    print(f"   cd ta9affi")
    
    print(f"\n3. 🔧 تثبيت المتطلبات:")
    print(f"   apt update && apt install -y python3 python3-pip python3-venv nginx")
    print(f"   python3 -m venv venv")
    print(f"   source venv/bin/activate")
    print(f"   pip install -r requirements_production.txt")
    
    print(f"\n4. 🚀 تشغيل التطبيق:")
    print(f"   # تشغيل مباشر للاختبار:")
    print(f"   python3 app.py")
    print(f"   ")
    print(f"   # أو مع Gunicorn للإنتاج:")
    print(f"   gunicorn --bind 0.0.0.0:5000 --workers 2 app:app")
    
    print(f"\n5. 🌐 اختبار التطبيق:")
    print(f"   http://*************:5000")
    
    print(f"\n6. ⚙️ إعداد Nginx (اختياري):")
    print(f"   cp nginx/nginx.conf /etc/nginx/sites-available/ta9affi")
    print(f"   ln -s /etc/nginx/sites-available/ta9affi /etc/nginx/sites-enabled/")
    print(f"   systemctl reload nginx")

def main():
    """الدالة الرئيسية"""
    
    print("📦 إنشاء حزمة Ta9affi الكاملة للنشر المباشر")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # إنشاء الحزمة
    package_name = create_package()
    
    if not package_name:
        print("\n❌ فشل في إنشاء الحزمة")
        return
    
    # عرض التعليمات
    show_deployment_instructions(package_name)
    
    print(f"\n🎉 Ta9affi جاهز للنشر المباشر!")
    print(f"📦 الحزمة: {package_name}")
    print(f"⏱️ وقت النشر المتوقع: 15 دقيقة")

if __name__ == "__main__":
    main()
