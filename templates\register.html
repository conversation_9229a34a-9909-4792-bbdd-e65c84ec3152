{% extends 'base.html' %}

{% block extra_css %}
<style>
    /* تحسينات CSS للصفحة */
    .password-toggle-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        padding: 5px;
        border-radius: 3px;
        transition: color 0.3s ease;
    }

    .password-toggle-btn:hover {
        color: #495057;
        background-color: rgba(0,0,0,0.05);
    }

    .form-floating {
        position: relative;
    }

    .form-floating .password-toggle-btn {
        right: 15px;
    }

    .btn-hover-effect {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-hover-effect:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .animated-icon {
        animation: fadeInDown 0.8s ease-out;
    }

    .pulse-icon {
        animation: pulse 2s infinite;
    }

    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    .form-control.is-valid {
        border-color: #28a745;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    }

    .form-control.is-invalid {
        border-color: #dc3545;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    }

    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* تحسين الاستجابة للشاشات الصغيرة */
    @media (max-width: 768px) {
        .col-lg-7 {
            margin: 0 15px;
        }

        .card {
            margin-top: 20px !important;
        }

        .form-text {
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-7">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center font-weight-light my-2">
                    <i class="fas fa-user-plus animated-icon pulse-icon me-2"></i>
                    إنشاء حساب جديد
                </h3>
            </div>
            <div class="card-body">
                <!-- تحذير عام حول فرادة البيانات -->
                <div class="alert alert-info border-0 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle fa-lg me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">متطلبات التسجيل</h6>
                            <p class="mb-0">
                                يجب أن تكون جميع البيانات (اسم المستخدم، البريد الإلكتروني، رقم الهاتف)
                                <strong>فريدة وغير مستخدمة من قبل</strong> في النظام.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- رسائل الخطأ العامة -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            {% if category == 'danger' %}
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>خطأ:</strong> {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% elif category == 'warning' %}
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <strong>تنبيه:</strong> {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% elif category == 'info' %}
                                <div class="alert alert-info alert-dismissible fade show" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" action="{{ url_for('register') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                <input class="form-control" id="username" name="username" type="text"
                                    placeholder="اسم المستخدم" required />
                                <label for="username">اسم المستخدم</label>
                                <div class="form-text text-muted mt-1">
                                    <i class="fas fa-info-circle me-1"></i>
                                    أحرف لاتينية وأرقام فقط، بدون فراغات أو رموز خاصة<br>
                                    <i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                                    يجب أن يكون اسم المستخدم فريد (غير مستخدم من قبل)
                                </div>
                                <div id="usernameValidation" class="form-text"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input class="form-control" id="email" name="email" type="email"
                                    placeholder="البريد الإلكتروني" required />
                                <label for="email">البريد الإلكتروني</label>
                                <div class="form-text text-muted mt-1">
                                    <i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                                    يجب أن يكون البريد الإلكتروني فريد (غير مستخدم من قبل)
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0 position-relative">
                                <input class="form-control" id="password" name="password" type="password"
                                    placeholder="كلمة المرور" required />
                                <label for="password">كلمة المرور</label>
                                <button type="button" class="password-toggle-btn" id="togglePassword">
                                    <i class="fas fa-eye" id="togglePasswordIcon"></i>
                                </button>
                                <div class="form-text text-muted small">
                                    <strong>متطلبات كلمة المرور:</strong><br>
                                    • أكثر من 6 أحرف<br>
                                    • حرف كبير واحد على الأقل (A-Z)<br>
                                    • رقم واحد على الأقل (0-9)<br>
                                    • رمز واحد على الأقل (!@#$%^&*(),.?":{}|<>)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0 position-relative">
                                <input class="form-control" id="confirm_password" name="confirm_password"
                                    type="password" placeholder="تأكيد كلمة المرور" required />
                                <label for="confirm_password">تأكيد كلمة المرور</label>
                                <button type="button" class="password-toggle-btn" id="toggleConfirmPassword">
                                    <i class="fas fa-eye" id="toggleConfirmPasswordIcon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                <input class="form-control" id="phone_number" name="phone_number" type="tel"
                                    placeholder="رقم الهاتف"
                                    pattern="^(0[5-7]\d{8}|0[2-4]\d{7}|\+213[5-7]\d{8}|\+213[2-4]\d{7})$"
                                    title="أدخل رقم هاتف جزائري صحيح (مثال: 0555123456)" required />
                                <label for="phone_number">رقم الهاتف *</label>
                            </div>
                            <div class="form-text text-muted">
                                <small>مثال: 0555123456 أو 021234567</small><br>
                                <i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                                <small>يجب أن يكون رقم الهاتف فريد (غير مستخدم من قبل)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="wilaya_code" name="wilaya_code">
                                    <option value="">اختر الولاية (اختياري)</option>
                                    {% if wilayas %}
                                    {% for code, name in wilayas %}
                                    <option value="{{ code }}">{{ code }} - {{ name }}</option>
                                    {% endfor %}
                                    {% endif %}
                                </select>
                                <label for="wilaya_code">الولاية</label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        {% if enabled_roles and enabled_roles|length > 0 %}
                        <div class="form-floating">
                            <select class="form-select" id="role" name="role" required>
                                <option value="" selected disabled>اختر الدور</option>
                                {% for role_value, role_display in enabled_roles %}
                                <option value="{{ role_value }}">{{ role_display }}</option>
                                {% endfor %}
                            </select>
                            <label for="role">الدور *</label>
                        </div>
                        <div class="form-text text-muted mt-2">
                            <i class="fas fa-info-circle me-1"></i>
                            ملاحظة: سيتم تفعيل حسابك تلقائياً بعد إنشائه
                        </div>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>التسجيل معطل مؤقتاً</strong>
                            <br>
                            عذراً، التسجيل غير متاح حالياً. يرجى المحاولة لاحقاً أو التواصل مع الإدارة.
                        </div>
                        <input type="hidden" id="role" name="role" value="">
                        {% endif %}
                    </div>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>تفعيل فوري:</strong> سيتم تفعيل حسابك تلقائياً بعد إنشائه ويمكنك تسجيل الدخول مباشرة.
                    </div>

                    {% if show_success %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>تم إنشاء حسابك بنجاح!</strong>
                        <br>
                        حسابك مفعل الآن ويمكنك تسجيل الدخول مباشرة.
                        <br>
                        <a href="{{ url_for('login') }}" class="btn btn-outline-success btn-sm mt-2">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول الآن
                        </a>
                    </div>
                    {% endif %}

                    {% if not show_success %}
                    <div class="d-grid">
                        {% if enabled_roles and enabled_roles|length > 0 %}
                        <button class="btn btn-primary btn-lg btn-hover-effect" type="submit" id="submitBtn">
                            <span id="submitBtnText">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء الحساب
                            </span>
                            <span id="submitBtnLoading" class="d-none">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                جاري إنشاء الحساب...
                            </span>
                        </button>
                        {% else %}
                        <button class="btn btn-secondary btn-lg" type="button" disabled>
                            <i class="fas fa-ban me-2"></i>
                            التسجيل معطل مؤقتاً
                        </button>
                        {% endif %}
                    </div>
                    {% else %}
                    <div class="d-grid">
                        <a href="{{ url_for('register') }}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء حساب آخر
                        </a>
                    </div>
                    {% endif %}
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{{ url_for('login') }}">لديك حساب بالفعل؟ سجل دخولك!</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');
        const phoneField = document.getElementById('phone_number');
        const usernameField = document.getElementById('username');
        const submitBtn = document.getElementById('submitBtn');

        // التحقق من قوة كلمة المرور
        function validatePasswordStrength() {
            const password = passwordField.value;

            if (!password) {
                passwordField.setCustomValidity('');
                passwordField.classList.remove('is-invalid', 'is-valid');
                return true;
            }

            // التحقق من المتطلبات
            const hasMinLength = password.length > 6;
            const hasUpperCase = /[A-Z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(password);

            if (!hasMinLength) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تكون أكثر من 6 أحرف');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else if (!hasUpperCase) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else if (!hasNumber) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else if (!hasSymbol) {
                passwordField.setCustomValidity('كلمة المرور يجب أن تحتوي على رمز واحد على الأقل');
                passwordField.classList.add('is-invalid');
                passwordField.classList.remove('is-valid');
                return false;
            } else {
                passwordField.setCustomValidity('');
                passwordField.classList.remove('is-invalid');
                passwordField.classList.add('is-valid');
                return true;
            }
        }

        // التحقق من تطابق كلمة المرور
        function validatePasswords() {
            const password = passwordField.value;
            const confirmPassword = confirmPasswordField.value;

            if (password && confirmPassword) {
                if (password !== confirmPassword) {
                    confirmPasswordField.setCustomValidity('كلمة المرور غير متطابقة');
                    confirmPasswordField.classList.add('is-invalid');
                    return false;
                } else {
                    confirmPasswordField.setCustomValidity('');
                    confirmPasswordField.classList.remove('is-invalid');
                    confirmPasswordField.classList.add('is-valid');
                    return true;
                }
            }
            return true;
        }

        // دالة التحقق من صحة اسم المستخدم
        function validateUsername() {
            const username = usernameField.value;
            const validationDiv = document.getElementById('usernameValidation');

            if (!username) {
                validationDiv.innerHTML = '';
                usernameField.classList.remove('is-valid', 'is-invalid');
                return true; // سيتم التحقق من الحقول المطلوبة في مكان آخر
            }

            // التحقق من الطول
            if (username.length < 3 || username.length > 20) {
                validationDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times me-1"></i>يجب أن يكون بين 3 و 20 حرف</span>';
                usernameField.classList.remove('is-valid');
                usernameField.classList.add('is-invalid');
                return false;
            }

            // التحقق من وجود فراغات
            if (username.includes(' ')) {
                validationDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times me-1"></i>لا يسمح بالفراغات</span>';
                usernameField.classList.remove('is-valid');
                usernameField.classList.add('is-invalid');
                return false;
            }

            // التحقق من الأحرف المسموحة (أحرف لاتينية وأرقام وشرطة سفلية فقط)
            const pattern = /^[a-zA-Z0-9_]+$/;
            if (!pattern.test(username)) {
                validationDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times me-1"></i>أحرف لاتينية وأرقام فقط</span>';
                usernameField.classList.remove('is-valid');
                usernameField.classList.add('is-invalid');
                return false;
            }

            // إذا كان كل شيء صحيح
            validationDiv.innerHTML = '<span class="text-success"><i class="fas fa-check me-1"></i>اسم المستخدم صحيح</span>';
            usernameField.classList.remove('is-invalid');
            usernameField.classList.add('is-valid');
            return true;
        }

        // التحقق من رقم الهاتف الجزائري
        function validatePhone() {
            const phone = phoneField.value.replace(/[\s\-]/g, '');
            const patterns = [
                /^0[5-7]\d{8}$/,  // أرقام الجوال
                /^0[2-4]\d{7}$/,  // أرقام الهاتف الثابت
                /^\+213[5-7]\d{8}$/,  // أرقام دولية للجوال
                /^\+213[2-4]\d{7}$/   // أرقام دولية للثابت
            ];

            const isValid = patterns.some(pattern => pattern.test(phone));

            if (phone && !isValid) {
                phoneField.setCustomValidity('رقم الهاتف غير صحيح');
                phoneField.classList.add('is-invalid');
                return false;
            } else {
                phoneField.setCustomValidity('');
                phoneField.classList.remove('is-invalid');
                if (phone) phoneField.classList.add('is-valid');
                return true;
            }
        }

        // دوال إظهار/إخفاء كلمة المرور
        function setupPasswordToggle(passwordFieldId, toggleButtonId, toggleIconId) {
            const passwordField = document.getElementById(passwordFieldId);
            const toggleBtn = document.getElementById(toggleButtonId);
            const toggleIcon = document.getElementById(toggleIconId);

            if (passwordField && toggleBtn && toggleIcon) {
                toggleBtn.addEventListener('click', function () {
                    // تبديل نوع الحقل بين password و text
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);

                    // تبديل الأيقونة
                    if (type === 'text') {
                        toggleIcon.classList.remove('fa-eye');
                        toggleIcon.classList.add('fa-eye-slash');
                        toggleBtn.setAttribute('title', 'إخفاء كلمة المرور');
                    } else {
                        toggleIcon.classList.remove('fa-eye-slash');
                        toggleIcon.classList.add('fa-eye');
                        toggleBtn.setAttribute('title', 'إظهار كلمة المرور');
                    }
                });

                // إضافة تلميح للزر
                toggleBtn.setAttribute('title', 'إظهار كلمة المرور');
            }
        }

        // تفعيل إظهار/إخفاء كلمة المرور لكلا الحقلين
        setupPasswordToggle('password', 'togglePassword', 'togglePasswordIcon');
        setupPasswordToggle('confirm_password', 'toggleConfirmPassword', 'toggleConfirmPasswordIcon');

        // إضافة مستمعي الأحداث
        usernameField.addEventListener('input', validateUsername);
        passwordField.addEventListener('input', function () {
            validatePasswordStrength();
            validatePasswords();
        });
        confirmPasswordField.addEventListener('input', validatePasswords);
        phoneField.addEventListener('input', validatePhone);

        // دالة لإظهار حالة التحميل
        function showLoadingState() {
            const submitBtn = document.getElementById('submitBtn');
            const submitBtnText = document.getElementById('submitBtnText');
            const submitBtnLoading = document.getElementById('submitBtnLoading');

            if (submitBtn && submitBtnText && submitBtnLoading) {
                submitBtn.disabled = true;
                submitBtnText.classList.add('d-none');
                submitBtnLoading.classList.remove('d-none');
            }
        }

        // دالة لإخفاء حالة التحميل
        function hideLoadingState() {
            const submitBtn = document.getElementById('submitBtn');
            const submitBtnText = document.getElementById('submitBtnText');
            const submitBtnLoading = document.getElementById('submitBtnLoading');

            if (submitBtn && submitBtnText && submitBtnLoading) {
                submitBtn.disabled = false;
                submitBtnText.classList.remove('d-none');
                submitBtnLoading.classList.add('d-none');
            }
        }

        // دالة للتحقق من جميع الحقول
        function validateAllFields() {
            const isUsernameValid = validateUsername();
            const isPasswordValid = validatePasswordStrength();
            const isPasswordsMatch = validatePasswords();
            const isPhoneValid = validatePhone();

            return isUsernameValid && isPasswordValid && isPasswordsMatch && isPhoneValid;
        }

        // التحقق عند الإرسال
        document.querySelector('form').addEventListener('submit', function (e) {
            // التحقق من صحة جميع الحقول
            if (!validateAllFields()) {
                e.preventDefault();
                e.stopPropagation();

                // إظهار رسالة خطأ
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>خطأ في البيانات:</strong> يرجى التحقق من جميع الحقول وتصحيح الأخطاء.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // إدراج الرسالة قبل النموذج
                const form = document.querySelector('form');
                form.parentNode.insertBefore(alertDiv, form);

                // التمرير إلى أعلى الصفحة
                window.scrollTo({ top: 0, behavior: 'smooth' });

                return false;
            }

            // إظهار حالة التحميل
            showLoadingState();

            // إضافة timeout للحماية من التعليق
            setTimeout(function() {
                hideLoadingState();
            }, 30000); // 30 ثانية
        });

        // إخفاء حالة التحميل عند تحميل الصفحة (في حالة الرجوع)
        window.addEventListener('pageshow', function() {
            hideLoadingState();
        });

        // معالجة أخطاء الشبكة
        window.addEventListener('beforeunload', function() {
            hideLoadingState();
        });

        // إضافة تحسينات إضافية للتجربة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية للحقول
            const inputs = document.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });

            // إضافة رسالة ترحيب ديناميكية
            const roleSelect = document.getElementById('role');
            if (roleSelect) {
                roleSelect.addEventListener('change', function() {
                    const selectedRole = this.value;
                    const roleMessages = {
                        'teacher': 'مرحباً بك كمعلم! ستتمكن من تتبع تقدمك التعليمي.',
                        'inspector': 'مرحباً بك كمفتش! ستتمكن من مراقبة تقدم المعلمين.',
                        'user_manager': 'مرحباً بك كمدير مستخدمين! ستتمكن من إدارة الحسابات.'
                    };

                    if (roleMessages[selectedRole]) {
                        // إزالة الرسائل السابقة
                        const existingMessage = document.querySelector('.role-welcome-message');
                        if (existingMessage) {
                            existingMessage.remove();
                        }

                        // إضافة رسالة جديدة
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'alert alert-info role-welcome-message mt-2';
                        messageDiv.innerHTML = `
                            <i class="fas fa-info-circle me-2"></i>
                            ${roleMessages[selectedRole]}
                        `;

                        roleSelect.parentElement.parentElement.appendChild(messageDiv);

                        // إزالة الرسالة بعد 5 ثوان
                        setTimeout(() => {
                            if (messageDiv.parentElement) {
                                messageDiv.remove();
                            }
                        }, 5000);
                    }
                });
            }

            // تحسين تجربة المستخدم مع رسائل الخطأ
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(alert => {
                // إضافة تأثير انزلاق
                alert.style.transform = 'translateY(-20px)';
                alert.style.opacity = '0';

                setTimeout(() => {
                    alert.style.transition = 'all 0.3s ease';
                    alert.style.transform = 'translateY(0)';
                    alert.style.opacity = '1';
                }, 100);

                // إزالة تلقائية بعد 10 ثوان
                setTimeout(() => {
                    if (alert.parentElement) {
                        alert.style.transform = 'translateY(-20px)';
                        alert.style.opacity = '0';
                        setTimeout(() => {
                            if (alert.parentElement) {
                                alert.remove();
                            }
                        }, 300);
                    }
                }, 10000);
            });
        });
    });
</script>

{% endblock %}
