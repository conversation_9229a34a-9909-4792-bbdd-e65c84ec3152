# المتطلبات الأساسية
flask>=2.3.0
flask-sqlalchemy>=3.0.0
flask-login>=0.6.0
flask-wtf>=1.1.0
werkzeug>=2.3.0
pandas>=1.5.0
openpyxl>=3.1.0
chargily-pay
schedule>=1.2.0

# قواعد البيانات
# MySQL
pymysql>=1.0.0

# PostgreSQL
psycopg2-binary>=2.9.0

# SQLite (مدمج في Python)

# Redis والتخزين المؤقت
redis>=4.5.0
flask-session>=0.5.0

# الأمان والحماية
flask-limiter>=3.3.0
flask-talisman>=1.1.0

# خادم الإنتاج
gunicorn>=20.1.0
gevent>=22.10.0

# المراقبة والسجلات
flask-migrate>=4.0.0

# أدوات إضافية
python-dotenv>=1.0.0  # لقراءة ملفات .env
requests>=2.28.0      # للطلبات HTTP
