#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ta9affi - الإصدار المحسن للإنتاج
نظام إدارة التقدم التعليمي محسن لمئات الآلاف من المستخدمين
"""

import os
import sys
from datetime import datetime

# إعداد متغيرات البيئة قبل استيراد Flask
os.environ.setdefault('FLASK_ENV', 'production')

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_caching import Cache
from flask_session import Session
from flask_compress import Compress
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# استيراد إعدادات الإنتاج
from config_production import get_config

def create_app():
    """إنشاء تطبيق Flask محسن للإنتاج"""
    
    app = Flask(__name__)
    
    # تحميل إعدادات الإنتاج
    config_class = get_config()
    app.config.from_object(config_class)
    
    # تهيئة قاعدة البيانات
    from models_new import db
    db.init_app(app)
    
    # تهيئة نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from models_new import User
        return User.query.get(int(user_id))
    
    # تهيئة التخزين المؤقت
    cache = Cache()
    cache.init_app(app)
    
    # تهيئة الجلسات
    Session(app)
    
    # تهيئة ضغط الاستجابات
    Compress(app)
    
    # تهيئة Rate Limiting
    limiter = Limiter(
        app,
        key_func=get_remote_address,
        default_limits=["1000 per hour"]
    )
    
    # تسجيل المسارات
    register_routes(app)
    
    # معالجة الأخطاء
    register_error_handlers(app)
    
    # إعداد السجلات
    setup_logging(app)
    
    return app

def register_routes(app):
    """تسجيل جميع المسارات"""
    
    # استيراد المسارات من التطبيق الأصلي
    try:
        # استيراد التطبيق الأصلي للحصول على المسارات
        import app as original_app
        
        # نسخ جميع المسارات
        for rule in original_app.app.url_map.iter_rules():
            if rule.endpoint != 'static':
                # الحصول على دالة المسار
                view_func = original_app.app.view_functions.get(rule.endpoint)
                if view_func:
                    # إضافة المسار للتطبيق الجديد
                    app.add_url_rule(
                        rule.rule,
                        endpoint=rule.endpoint,
                        view_func=view_func,
                        methods=rule.methods
                    )
        
        print("✅ تم تسجيل جميع المسارات")
        
    except Exception as e:
        print(f"⚠️ خطأ في تسجيل المسارات: {e}")
        # في حالة الفشل، استخدم المسارات الأساسية
        register_basic_routes(app)

def register_basic_routes(app):
    """تسجيل المسارات الأساسية"""
    
    @app.route('/')
    def index():
        return "Ta9affi - نظام إدارة التقدم التعليمي"
    
    @app.route('/health')
    def health_check():
        """فحص صحة التطبيق"""
        return {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0'
        }

def register_error_handlers(app):
    """تسجيل معالجات الأخطاء"""
    
    @app.errorhandler(404)
    def not_found(error):
        return {'error': 'الصفحة غير موجودة'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {'error': 'خطأ داخلي في الخادم'}, 500
    
    @app.errorhandler(429)
    def ratelimit_handler(e):
        return {'error': 'تم تجاوز حد الطلبات المسموح'}, 429

def setup_logging(app):
    """إعداد نظام السجلات"""
    
    import logging
    from logging.handlers import RotatingFileHandler
    
    if not app.debug and not app.testing:
        # إنشاء مجلد السجلات
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        # إعداد ملف السجل
        file_handler = RotatingFileHandler(
            'logs/ta9affi.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Ta9affi startup')

def init_database(app):
    """تهيئة قاعدة البيانات"""
    
    with app.app_context():
        from models_new import db
        
        try:
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")
            
            # تهيئة البيانات الأساسية
            init_basic_data()
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")

def init_basic_data():
    """تهيئة البيانات الأساسية"""
    
    from models_new import db, SubscriptionPlan, Role
    
    try:
        # إنشاء باقات الاشتراك الأساسية
        if SubscriptionPlan.query.count() == 0:
            plans = [
                SubscriptionPlan(
                    name="الباقة الأساسية",
                    description="باقة أساسية للمعلمين",
                    price=500.0,
                    duration_days=30,
                    features="إدارة التقدم الأساسية"
                ),
                SubscriptionPlan(
                    name="الباقة المتقدمة",
                    description="باقة متقدمة مع ميزات إضافية",
                    price=1000.0,
                    duration_days=30,
                    features="جميع الميزات + تقارير متقدمة"
                )
            ]
            
            for plan in plans:
                db.session.add(plan)
            
            db.session.commit()
            print("✅ تم إنشاء باقات الاشتراك الأساسية")
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة البيانات الأساسية: {e}")
        db.session.rollback()

# إنشاء التطبيق
app = create_app()

# تهيئة قاعدة البيانات إذا لزم الأمر
if os.environ.get('INIT_DB') == 'true':
    init_database(app)

if __name__ == '__main__':
    # تشغيل التطبيق في وضع التطوير
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=False
    )
