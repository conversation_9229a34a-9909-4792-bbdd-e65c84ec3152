#!/bin/bash
# سكريبت التشغيل المحلي لـ Ta9affi

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# دوال المساعدة
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# متغيرات الإعداد
DOCKER_COMPOSE_FILE="docker-compose.local.yml"
ENV_FILE=".env.local"

# التحقق من المتطلبات
check_requirements() {
    log_header "🔍 التحقق من المتطلبات"
    
    # التحقق من Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker غير مثبت. يرجى تثبيت Docker أولاً"
        echo "تحميل من: https://www.docker.com/products/docker-desktop"
        exit 1
    fi
    
    # التحقق من Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose غير مثبت"
        exit 1
    fi
    
    # التحقق من ملف Docker Compose
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        log_error "ملف Docker Compose $DOCKER_COMPOSE_FILE غير موجود"
        exit 1
    fi
    
    log_success "جميع المتطلبات متوفرة"
}

# إنشاء المجلدات المطلوبة
create_directories() {
    log_info "إنشاء المجلدات المطلوبة..."
    
    mkdir -p uploads
    mkdir -p logs
    mkdir -p backups
    mkdir -p static/optimized
    mkdir -p database/init
    mkdir -p nginx
    mkdir -p monitoring
    
    log_success "تم إنشاء المجلدات"
}

# إعداد ملفات الإعداد
setup_config_files() {
    log_info "إعداد ملفات الإعداد..."
    
    # إنشاء ملف إعداد Nginx المحلي
    cat > nginx/nginx.local.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream ta9affi_app {
        server ta9affi-app:5000;
    }

    server {
        listen 80;
        server_name localhost;

        location / {
            proxy_pass http://ta9affi_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            alias /var/www/static/;
            expires 1h;
        }

        location /uploads/ {
            alias /var/www/uploads/;
            expires 1h;
        }
    }
}
EOF

    # إنشاء ملف إعداد Prometheus المحلي
    cat > monitoring/prometheus.local.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ta9affi-app'
    static_configs:
      - targets: ['ta9affi-app:5000']
EOF

    log_success "تم إعداد ملفات الإعداد"
}

# بناء وتشغيل الخدمات الأساسية
start_basic_services() {
    log_header "🚀 بدء الخدمات الأساسية"
    
    log_info "بناء صور Docker..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build
    
    log_info "تشغيل قاعدة البيانات و Redis..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d postgres redis
    
    # انتظار جاهزية قاعدة البيانات
    log_info "انتظار جاهزية قاعدة البيانات..."
    timeout 60 bash -c 'until docker-compose -f "$DOCKER_COMPOSE_FILE" exec postgres pg_isready -U ta9affi_user; do sleep 2; done'
    
    log_info "تشغيل التطبيق الرئيسي..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d ta9affi-app
    
    log_success "تم تشغيل الخدمات الأساسية"
}

# تشغيل جميع الخدمات
start_all_services() {
    log_header "🚀 بدء جميع الخدمات"
    
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile nginx --profile monitoring --profile mail --profile admin up -d
    
    log_success "تم تشغيل جميع الخدمات"
}

# فحص الصحة
health_check() {
    log_header "🏥 فحص صحة الخدمات"
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "محاولة فحص الصحة ($attempt/$max_attempts)..."
        
        # فحص التطبيق الرئيسي
        if curl -f -s http://localhost:5000/health > /dev/null 2>&1; then
            log_success "التطبيق الرئيسي يعمل بشكل صحيح"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "فشل في فحص صحة التطبيق"
            return 1
        fi
        
        sleep 5
        ((attempt++))
    done
    
    # فحص قاعدة البيانات
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec postgres pg_isready -U ta9affi_user > /dev/null 2>&1; then
        log_success "قاعدة البيانات تعمل بشكل صحيح"
    else
        log_warning "قد تكون هناك مشكلة في قاعدة البيانات"
    fi
    
    # فحص Redis
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
        log_success "Redis يعمل بشكل صحيح"
    else
        log_warning "قد تكون هناك مشكلة في Redis"
    fi
    
    log_success "فحص الصحة مكتمل"
}

# عرض معلومات التشغيل
show_info() {
    log_header "📋 معلومات التشغيل المحلي"
    
    echo "=================================="
    echo "🚀 Ta9affi يعمل محلياً!"
    echo "=================================="
    echo "🌐 التطبيق الرئيسي: http://localhost:5000"
    echo "🔧 Nginx (إذا كان مفعل): http://localhost:8080"
    echo "📊 Grafana (إذا كان مفعل): http://localhost:3000 (admin/admin123)"
    echo "📈 Prometheus (إذا كان مفعل): http://localhost:9090"
    echo "📧 MailHog (إذا كان مفعل): http://localhost:8025"
    echo "🗄️ pgAdmin (إذا كان مفعل): http://localhost:5050 (<EMAIL>/admin123)"
    echo "📦 Redis Commander (إذا كان مفعل): http://localhost:8081"
    echo "=================================="
    echo ""
    echo "📝 أوامر مفيدة:"
    echo "   عرض السجلات: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
    echo "   إعادة التشغيل: docker-compose -f $DOCKER_COMPOSE_FILE restart"
    echo "   الإيقاف: docker-compose -f $DOCKER_COMPOSE_FILE down"
    echo "   فحص الحالة: docker-compose -f $DOCKER_COMPOSE_FILE ps"
    echo "   الدخول للتطبيق: docker-compose -f $DOCKER_COMPOSE_FILE exec ta9affi-app bash"
    echo "=================================="
}

# إيقاف الخدمات
stop_services() {
    log_header "⏹️ إيقاف الخدمات"
    
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    log_success "تم إيقاف جميع الخدمات"
}

# تنظيف البيانات
cleanup() {
    log_header "🧹 تنظيف البيانات"
    
    log_warning "هذا سيحذف جميع البيانات المحلية!"
    read -p "هل أنت متأكد؟ (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" down -v
        docker system prune -f
        log_success "تم تنظيف البيانات"
    else
        log_info "تم إلغاء التنظيف"
    fi
}

# عرض السجلات
show_logs() {
    local service=${1:-}
    
    if [ -n "$service" ]; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f "$service"
    else
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f
    fi
}

# معالجة المعاملات
case "${1:-start}" in
    "start")
        log_header "🚀 بدء Ta9affi محلياً"
        check_requirements
        create_directories
        setup_config_files
        start_basic_services
        health_check
        show_info
        ;;
        
    "start-all")
        log_header "🚀 بدء Ta9affi مع جميع الخدمات"
        check_requirements
        create_directories
        setup_config_files
        start_all_services
        health_check
        show_info
        ;;
        
    "stop")
        stop_services
        ;;
        
    "restart")
        stop_services
        sleep 2
        start_basic_services
        health_check
        show_info
        ;;
        
    "health")
        health_check
        ;;
        
    "logs")
        show_logs "${2:-}"
        ;;
        
    "cleanup")
        cleanup
        ;;
        
    "info")
        show_info
        ;;
        
    "shell")
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec ta9affi-app bash
        ;;
        
    "db-shell")
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec postgres psql -U ta9affi_user -d ta9affi_local
        ;;
        
    "redis-shell")
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec redis redis-cli
        ;;
        
    "help")
        echo "استخدام: $0 [COMMAND]"
        echo ""
        echo "الأوامر:"
        echo "  start       - تشغيل الخدمات الأساسية (افتراضي)"
        echo "  start-all   - تشغيل جميع الخدمات"
        echo "  stop        - إيقاف الخدمات"
        echo "  restart     - إعادة تشغيل الخدمات"
        echo "  health      - فحص صحة الخدمات"
        echo "  logs        - عرض السجلات"
        echo "  cleanup     - تنظيف البيانات"
        echo "  info        - عرض معلومات التشغيل"
        echo "  shell       - الدخول لحاوية التطبيق"
        echo "  db-shell    - الدخول لقاعدة البيانات"
        echo "  redis-shell - الدخول لـ Redis"
        echo "  help        - عرض هذه المساعدة"
        ;;
        
    *)
        log_error "أمر غير معروف: $1"
        echo "استخدم '$0 help' لعرض الأوامر المتاحة"
        exit 1
        ;;
esac
