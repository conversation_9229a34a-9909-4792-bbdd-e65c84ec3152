{% extends "base.html" %}

{% block title %}إرسال إشعار{% endblock %}

{% block content %}
<style>
    .user-item {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
        transition: all 0.2s ease;
        background-color: #f8f9fa;
    }

    .user-item:hover {
        background-color: #e3f2fd;
        border-color: #2196f3;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .user-item input[type="checkbox"]:checked+label {
        color: #1976d2;
        font-weight: bold;
    }

    .user-item label {
        cursor: pointer;
        margin-bottom: 0;
        width: 100%;
    }

    #user_search {
        border-radius: 25px;
        padding-left: 45px;
    }

    .input-group-text {
        border-radius: 25px 0 0 25px;
        background-color: #f8f9fa;
        border-color: #ced4da;
    }

    .btn-outline-secondary {
        border-radius: 0 25px 25px 0;
    }

    .search-highlight {
        background-color: #fff3cd;
        padding: 2px 4px;
        border-radius: 3px;
    }
</style>
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-paper-plane me-2 text-primary"></i>
            إرسال إشعار
        </h1>
        <a href="{{ url_for('view_notifications') }}" class="btn btn-outline-primary">
            <i class="fas fa-bell me-1"></i>
            عرض الإشعارات
        </a>
    </div>

    <!-- نموذج إرسال الإشعار -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>
                        إنشاء إشعار جديد
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- عنوان الإشعار -->
                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان الإشعار</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <!-- نص الإشعار -->
                        <div class="mb-3">
                            <label for="message" class="form-label">نص الإشعار</label>
                            <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                        </div>

                        <!-- نوع الإرسال -->
                        <div class="mb-3">
                            <label for="target_type" class="form-label">إرسال إلى</label>
                            <select class="form-control" id="target_type" name="target_type" required
                                onchange="toggleTargetOptions()">
                                <option value="">اختر نوع الإرسال</option>
                                <option value="all">الجميع</option>
                                <option value="role">دور معين</option>
                                <option value="specific">أشخاص محددين</option>
                            </select>
                        </div>

                        <!-- اختيار الدور -->
                        <div class="mb-3" id="role_selection" style="display: none;">
                            <label for="target_role" class="form-label">اختر الدور</label>
                            <select class="form-control" id="target_role" name="target_role">
                                <option value="">اختر الدور</option>
                                {% if current_user.role in ['admin', 'user_manager'] %}
                                <option value="inspector">المفتشين</option>
                                <option value="teacher">الأساتذة</option>
                                {% else %}
                                <option value="teacher">الأساتذة</option>
                                {% endif %}
                            </select>
                        </div>

                        <!-- اختيار أشخاص محددين -->
                        <div class="mb-3" id="specific_selection" style="display: none;">
                            <label class="form-label">اختر الأشخاص</label>

                            <!-- خانة البحث -->
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="user_search"
                                        placeholder="ابحث بالاسم أو رقم الهاتف..." onkeyup="filterUsers()"
                                        onkeydown="handleSearchKeydown(event)" autocomplete="off">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <small class="text-muted">
                                    ابدأ بكتابة الاسم أو رقم الهاتف لإظهار المستخدمين المطابقين<br>
                                    <strong>اختصارات:</strong> ESC (مسح البحث) | Ctrl+A (تحديد المرئيين) | Ctrl+D (إلغاء
                                    التحديد)
                                </small>
                            </div>

                            {% if current_user.role in ['admin', 'user_manager'] and users_by_role.get('inspectors') %}
                            <div class="mb-3">
                                <h6 class="text-primary">المفتشين</h6>
                                {% for inspector in users_by_role.inspectors %}
                                <div class="form-check user-item" data-username="{{ inspector.username|lower }}"
                                    data-phone="{{ inspector.phone_number or '' }}"
                                    data-email="{{ inspector.email|lower }}" style="display: none;">
                                    <input class="form-check-input" type="checkbox" name="specific_users"
                                        value="{{ inspector.id }}" id="inspector_{{ inspector.id }}">
                                    <label class="form-check-label" for="inspector_{{ inspector.id }}">
                                        <strong>{{ inspector.username }}</strong>
                                        <br><small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>{{ inspector.email }}
                                            {% if inspector.phone_number %}
                                            <br><i class="fas fa-phone me-1"></i>{{ inspector.phone_number }}
                                            {% endif %}
                                        </small>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            {% if users_by_role.get('teachers') %}
                            <div class="mb-3">
                                <h6 class="text-success">الأساتذة</h6>
                                {% for teacher in users_by_role.teachers %}
                                <div class="form-check user-item" data-username="{{ teacher.username|lower }}"
                                    data-phone="{{ teacher.phone_number or '' }}" data-email="{{ teacher.email|lower }}"
                                    style="display: none;">
                                    <input class="form-check-input" type="checkbox" name="specific_users"
                                        value="{{ teacher.id }}" id="teacher_{{ teacher.id }}">
                                    <label class="form-check-label" for="teacher_{{ teacher.id }}">
                                        <strong>{{ teacher.username }}</strong>
                                        <br><small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>{{ teacher.email }}
                                            {% if teacher.phone_number %}
                                            <br><i class="fas fa-phone me-1"></i>{{ teacher.phone_number }}
                                            {% endif %}
                                        </small>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <!-- أزرار تحديد الكل -->
                            <div class="mb-3" id="selection-buttons" style="display: none;">
                                <button type="button" class="btn btn-sm btn-outline-primary me-2"
                                    onclick="selectVisibleUsers()">
                                    <i class="fas fa-check-square me-1"></i>
                                    تحديد المرئيين
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAll()">
                                    <i class="fas fa-square me-1"></i>
                                    إلغاء التحديد
                                </button>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>
                                إرسال الإشعار
                            </button>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الإرسال
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">أنواع الإرسال:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-globe text-info me-2"></i><strong>الجميع:</strong> إرسال لجميع
                                المستخدمين</li>
                            <li><i class="fas fa-users text-success me-2"></i><strong>دور معين:</strong> إرسال لدور محدد
                            </li>
                            <li><i class="fas fa-user text-warning me-2"></i><strong>أشخاص محددين:</strong> إرسال لأشخاص
                                مختارين</li>
                        </ul>
                    </div>

                    {% if current_user.role == 'admin' %}
                    <div class="alert alert-info">
                        <i class="fas fa-crown me-2"></i>
                        <strong>صلاحيات الإدارة:</strong><br>
                        يمكنك إرسال إشعارات لجميع المستخدمين في النظام.
                    </div>
                    {% elif current_user.role == 'user_manager' %}
                    <div class="alert alert-success">
                        <i class="fas fa-users-cog me-2"></i>
                        <strong>صلاحيات مدير المستخدمين:</strong><br>
                        يمكنك إرسال إشعارات لجميع المفتشين والأساتذة في النظام.
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-user-tie me-2"></i>
                        <strong>صلاحيات المفتش:</strong><br>
                        يمكنك إرسال إشعارات للأساتذة تحت إشرافك فقط.
                    </div>
                    {% endif %}

                    <div class="mt-3">
                        <h6 class="text-success">إحصائيات:</h6>
                        <ul class="list-unstyled">
                            {% if users_by_role.get('inspectors') %}
                            <li><i class="fas fa-user-tie text-primary me-2"></i>المفتشين: {{
                                users_by_role.inspectors|length }}</li>
                            {% endif %}
                            {% if users_by_role.get('teachers') %}
                            <li><i class="fas fa-chalkboard-teacher text-success me-2"></i>الأساتذة: {{
                                users_by_role.teachers|length }}</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function toggleTargetOptions() {
        const targetType = document.getElementById('target_type').value;
        const roleSelection = document.getElementById('role_selection');
        const specificSelection = document.getElementById('specific_selection');

        // إخفاء جميع الخيارات
        roleSelection.style.display = 'none';
        specificSelection.style.display = 'none';

        // إظهار الخيار المناسب
        if (targetType === 'role') {
            roleSelection.style.display = 'block';
        } else if (targetType === 'specific') {
            specificSelection.style.display = 'block';
        }
    }



    function deselectAll() {
        const checkboxes = document.querySelectorAll('input[name="specific_users"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
    }

    // دالة البحث والتصفية
    function filterUsers() {
        const searchTerm = document.getElementById('user_search').value.toLowerCase().trim();
        const userItems = document.querySelectorAll('.user-item');
        let visibleCount = 0;

        userItems.forEach(item => {
            const username = item.getAttribute('data-username') || '';
            const phone = item.getAttribute('data-phone') || '';
            const email = item.getAttribute('data-email') || '';

            // البحث في الاسم أو رقم الهاتف أو البريد الإلكتروني
            const isMatch = username.includes(searchTerm) ||
                phone.includes(searchTerm) ||
                email.includes(searchTerm);

            // إظهار النتائج فقط عند وجود نص بحث وتطابق
            if (searchTerm !== '' && isMatch) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        // إظهار رسالة إذا لم توجد نتائج
        updateSearchResults(visibleCount, searchTerm);
    }

    // دالة مسح البحث
    function clearSearch() {
        document.getElementById('user_search').value = '';
        // إخفاء جميع المستخدمين عند مسح البحث
        const userItems = document.querySelectorAll('.user-item');
        userItems.forEach(item => {
            item.style.display = 'none';
        });
        // إخفاء أزرار التحديد
        const selectionButtons = document.getElementById('selection-buttons');
        selectionButtons.style.display = 'none';
        // إزالة رسائل البحث
        const existingMessage = document.getElementById('search-results-message');
        if (existingMessage) {
            existingMessage.remove();
        }
    }

    // دالة تحديث نتائج البحث
    function updateSearchResults(count, searchTerm) {
        let existingMessage = document.getElementById('search-results-message');
        const selectionButtons = document.getElementById('selection-buttons');

        // إزالة الرسالة السابقة إن وجدت
        if (existingMessage) {
            existingMessage.remove();
        }

        if (searchTerm === '') {
            // إخفاء أزرار التحديد عند عدم وجود نص بحث
            selectionButtons.style.display = 'none';
            return;
        } else if (searchTerm && count === 0) {
            // إخفاء أزرار التحديد عند عدم وجود نتائج
            selectionButtons.style.display = 'none';

            // إنشاء رسالة عدم وجود نتائج
            const message = document.createElement('div');
            message.id = 'search-results-message';
            message.className = 'alert alert-warning mt-2';
            message.innerHTML = `
                <i class="fas fa-search me-2"></i>
                لم يتم العثور على مستخدمين يطابقون البحث: "<strong>${searchTerm}</strong>"
            `;

            // إدراج الرسالة بعد خانة البحث
            const searchContainer = document.getElementById('user_search').closest('.mb-3');
            searchContainer.insertAdjacentElement('afterend', message);
        } else if (searchTerm && count > 0) {
            // إظهار أزرار التحديد عند وجود نتائج
            selectionButtons.style.display = 'block';

            // إنشاء رسالة عدد النتائج
            const message = document.createElement('div');
            message.id = 'search-results-message';
            message.className = 'alert alert-info mt-2';
            message.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                تم العثور على <strong>${count}</strong> مستخدم يطابق البحث: "<strong>${searchTerm}</strong>"
            `;

            // إدراج الرسالة بعد خانة البحث
            const searchContainer = document.getElementById('user_search').closest('.mb-3');
            searchContainer.insertAdjacentElement('afterend', message);
        }
    }

    // دالة التعامل مع اختصارات لوحة المفاتيح
    function handleSearchKeydown(event) {
        // ESC لمسح البحث
        if (event.key === 'Escape') {
            clearSearch();
            event.preventDefault();
        }
        // Ctrl+A لتحديد الكل من النتائج المرئية
        else if (event.ctrlKey && event.key === 'a') {
            event.preventDefault();
            selectVisibleUsers();
        }
        // Ctrl+D لإلغاء تحديد الكل
        else if (event.ctrlKey && event.key === 'd') {
            event.preventDefault();
            deselectAll();
        }
    }

    // دالة تحديد المستخدمين المرئيين فقط
    function selectVisibleUsers() {
        const visibleItems = document.querySelectorAll('.user-item[style="display: block"], .user-item:not([style*="display: none"])');
        visibleItems.forEach(item => {
            const checkbox = item.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    // إضافة مستمع للأحداث عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function () {
        // التركيز على خانة البحث عند فتح قسم الأشخاص المحددين
        const targetTypeSelect = document.getElementById('target_type');
        if (targetTypeSelect) {
            targetTypeSelect.addEventListener('change', function () {
                if (this.value === 'specific') {
                    setTimeout(() => {
                        const searchInput = document.getElementById('user_search');
                        if (searchInput) {
                            searchInput.focus();
                        }
                    }, 100);
                }
            });
        }
    });
</script>
{% endblock %}