#!/usr/bin/env python3
"""
أداة دمج قواعد البيانات - لحل مشكلة عدم استيراد التقدمات
"""

import sqlite3
import os
import shutil
from datetime import datetime

def analyze_database(db_path):
    """تحليل محتوى قاعدة البيانات"""
    
    if not os.path.exists(db_path):
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return None
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    analysis = {}
    
    try:
        # فحص الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        analysis['tables'] = tables
        
        # فحص عدد السجلات في كل جدول
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                analysis[table] = count
            except Exception as e:
                analysis[table] = f"خطأ: {str(e)}"
        
        # فحص التقدمات بالتفصيل
        if 'progress_entry' in tables:
            cursor.execute("""
                SELECT user_id, COUNT(*) as count, MIN(date) as first_date, MAX(date) as last_date
                FROM progress_entry 
                GROUP BY user_id
            """)
            progress_by_user = cursor.fetchall()
            analysis['progress_details'] = progress_by_user
        
        # فحص المستخدمين
        if 'user' in tables:
            cursor.execute("SELECT id, username, role FROM user")
            users = cursor.fetchall()
            analysis['users'] = users
    
    except Exception as e:
        analysis['error'] = str(e)
    
    finally:
        conn.close()
    
    return analysis

def merge_databases(source_db, target_db, backup_dir="database_backup"):
    """دمج قاعدة بيانات المصدر مع الهدف"""
    
    print("🔄 بدء عملية دمج قواعد البيانات...")
    
    # إنشاء نسخة احتياطية
    os.makedirs(backup_dir, exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join(backup_dir, f'target_backup_{timestamp}.db')
    shutil.copy2(target_db, backup_path)
    print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
    
    # الاتصال بقواعد البيانات
    source_conn = sqlite3.connect(source_db)
    target_conn = sqlite3.connect(target_db)
    
    source_cursor = source_conn.cursor()
    target_cursor = target_conn.cursor()
    
    try:
        # دمج التقدمات
        print("📊 دمج التقدمات...")
        
        # الحصول على التقدمات من المصدر
        source_cursor.execute("""
            SELECT user_id, competency_id, date, status, notes, created_at, updated_at
            FROM progress_entry
        """)
        source_progress = source_cursor.fetchall()
        
        merged_count = 0
        skipped_count = 0
        
        for progress in source_progress:
            user_id, competency_id, date, status, notes, created_at, updated_at = progress
            
            # التحقق من وجود التقدم في الهدف
            target_cursor.execute("""
                SELECT id FROM progress_entry 
                WHERE user_id = ? AND competency_id = ? AND date = ?
            """, (user_id, competency_id, date))
            
            existing = target_cursor.fetchone()
            
            if not existing:
                # إدراج التقدم الجديد
                target_cursor.execute("""
                    INSERT INTO progress_entry 
                    (user_id, competency_id, date, status, notes, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (user_id, competency_id, date, status, notes, created_at, updated_at))
                merged_count += 1
            else:
                # تحديث التقدم الموجود إذا كان أحدث
                target_cursor.execute("""
                    UPDATE progress_entry 
                    SET status = ?, notes = ?, updated_at = ?
                    WHERE user_id = ? AND competency_id = ? AND date = ?
                    AND updated_at < ?
                """, (status, notes, updated_at, user_id, competency_id, date, updated_at))
                if target_cursor.rowcount > 0:
                    merged_count += 1
                else:
                    skipped_count += 1
        
        # دمج المستخدمين الجدد
        print("👥 دمج المستخدمين...")
        
        source_cursor.execute("""
            SELECT username, email, password, role, phone_number, wilaya_code, 
                   is_active, created_at, last_login, free_trial_end, subscription_status
            FROM user
        """)
        source_users = source_cursor.fetchall()
        
        user_merged_count = 0
        
        for user in source_users:
            username = user[0]
            
            # التحقق من وجود المستخدم
            target_cursor.execute("SELECT id FROM user WHERE username = ?", (username,))
            existing_user = target_cursor.fetchone()
            
            if not existing_user:
                target_cursor.execute("""
                    INSERT INTO user 
                    (username, email, password, role, phone_number, wilaya_code, 
                     is_active, created_at, last_login, free_trial_end, subscription_status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, user)
                user_merged_count += 1
        
        # حفظ التغييرات
        target_conn.commit()
        
        print(f"✅ تم دمج {merged_count} تقدم")
        print(f"⏭️ تم تخطي {skipped_count} تقدم موجود")
        print(f"👥 تم دمج {user_merged_count} مستخدم جديد")
        
        return True
        
    except Exception as e:
        target_conn.rollback()
        print(f"❌ خطأ في الدمج: {str(e)}")
        return False
        
    finally:
        source_conn.close()
        target_conn.close()

def main():
    """الدالة الرئيسية"""
    
    print("🔧 أداة دمج قواعد البيانات - Ta9affi")
    print("=" * 50)
    
    # مسارات قواعد البيانات
    production_db = "ta9affi_production.db"  # قاعدة البيانات المُصدرة من الإنتاج
    local_db = "ta9affi.db"  # قاعدة البيانات المحلية
    
    print("📋 الخيارات المتاحة:")
    print("1. تحليل قاعدة البيانات المحلية")
    print("2. تحليل قاعدة البيانات المُصدرة")
    print("3. دمج قاعدة البيانات المُصدرة مع المحلية")
    print("4. مقارنة قواعد البيانات")
    
    choice = input("\nاختر رقم (1-4): ").strip()
    
    if choice == "1":
        print(f"\n🔍 تحليل قاعدة البيانات المحلية: {local_db}")
        analysis = analyze_database(local_db)
        if analysis:
            print(f"📊 الجداول الموجودة: {len(analysis.get('tables', []))}")
            for table, count in analysis.items():
                if table not in ['tables', 'error', 'progress_details', 'users']:
                    print(f"   - {table}: {count} سجل")
            
            if 'progress_details' in analysis:
                print(f"📈 تفاصيل التقدمات:")
                for user_id, count, first_date, last_date in analysis['progress_details']:
                    print(f"   - المستخدم {user_id}: {count} تقدم ({first_date} إلى {last_date})")
    
    elif choice == "2":
        print(f"\n🔍 تحليل قاعدة البيانات المُصدرة: {production_db}")
        analysis = analyze_database(production_db)
        if analysis:
            print(f"📊 الجداول الموجودة: {len(analysis.get('tables', []))}")
            for table, count in analysis.items():
                if table not in ['tables', 'error', 'progress_details', 'users']:
                    print(f"   - {table}: {count} سجل")
            
            if 'progress_details' in analysis:
                print(f"📈 تفاصيل التقدمات:")
                for user_id, count, first_date, last_date in analysis['progress_details']:
                    print(f"   - المستخدم {user_id}: {count} تقدم ({first_date} إلى {last_date})")
    
    elif choice == "3":
        print(f"\n🔄 دمج {production_db} مع {local_db}")
        if os.path.exists(production_db) and os.path.exists(local_db):
            success = merge_databases(production_db, local_db)
            if success:
                print("✅ تم الدمج بنجاح!")
            else:
                print("❌ فشل الدمج!")
        else:
            print("❌ أحد قواعد البيانات غير موجودة")
    
    elif choice == "4":
        print(f"\n📊 مقارنة قواعد البيانات")
        local_analysis = analyze_database(local_db)
        production_analysis = analyze_database(production_db)
        
        if local_analysis and production_analysis:
            print("📈 مقارنة عدد السجلات:")
            all_tables = set(local_analysis.get('tables', [])) | set(production_analysis.get('tables', []))
            
            for table in sorted(all_tables):
                local_count = local_analysis.get(table, 0)
                prod_count = production_analysis.get(table, 0)
                print(f"   - {table}: محلي={local_count}, إنتاج={prod_count}")

if __name__ == '__main__':
    main()
