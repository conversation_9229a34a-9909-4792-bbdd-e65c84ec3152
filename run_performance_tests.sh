#!/bin/bash
# سكريبت شامل لاختبار الأداء والحمولة لتطبيق Ta9affi

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# دوال المساعدة
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# متغيرات الإعداد
APP_URL="http://localhost:5000"
DATABASE_URL="postgresql://ta9affi_user:ta9affi_password@localhost:5432/ta9affi"
VENV_PATH="./venv"
RESULTS_DIR="performance_test_results"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# إنشاء مجلد النتائج
create_results_dir() {
    mkdir -p "$RESULTS_DIR"
    log_info "تم إنشاء مجلد النتائج: $RESULTS_DIR"
}

# التحقق من متطلبات النظام
check_requirements() {
    log_header "🔍 التحقق من متطلبات النظام"
    
    # التحقق من Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 غير مثبت"
        exit 1
    fi
    
    # التحقق من البيئة الافتراضية
    if [ ! -d "$VENV_PATH" ]; then
        log_error "البيئة الافتراضية غير موجودة في: $VENV_PATH"
        exit 1
    fi
    
    # التحقق من PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_warning "psql غير مثبت - قد لا تعمل اختبارات قاعدة البيانات"
    fi
    
    # التحقق من اتصال التطبيق
    if ! curl -s "$APP_URL" > /dev/null; then
        log_error "لا يمكن الوصول للتطبيق على: $APP_URL"
        log_info "تأكد من تشغيل التطبيق أولاً"
        exit 1
    fi
    
    log_success "جميع المتطلبات متوفرة"
}

# تفعيل البيئة الافتراضية
activate_venv() {
    log_info "تفعيل البيئة الافتراضية..."
    source "$VENV_PATH/bin/activate"
    
    # تثبيت المكتبات المطلوبة للاختبار
    pip install -q aiohttp psutil
    
    log_success "تم تفعيل البيئة الافتراضية"
}

# اختبار الحمولة الخفيفة
run_light_load_test() {
    log_header "🧪 اختبار الحمولة الخفيفة (50 مستخدم)"
    
    python3 load_testing.py --scenario light --url "$APP_URL" > "$RESULTS_DIR/light_load_$TIMESTAMP.log" 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "انتهى اختبار الحمولة الخفيفة"
    else
        log_error "فشل اختبار الحمولة الخفيفة"
        return 1
    fi
}

# اختبار الحمولة المتوسطة
run_medium_load_test() {
    log_header "⚡ اختبار الحمولة المتوسطة (200 مستخدم)"
    
    python3 load_testing.py --scenario medium --url "$APP_URL" > "$RESULTS_DIR/medium_load_$TIMESTAMP.log" 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "انتهى اختبار الحمولة المتوسطة"
    else
        log_error "فشل اختبار الحمولة المتوسطة"
        return 1
    fi
}

# اختبار الحمولة الثقيلة
run_heavy_load_test() {
    log_header "🔥 اختبار الحمولة الثقيلة (500 مستخدم)"
    
    python3 load_testing.py --scenario heavy --url "$APP_URL" > "$RESULTS_DIR/heavy_load_$TIMESTAMP.log" 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "انتهى اختبار الحمولة الثقيلة"
    else
        log_error "فشل اختبار الحمولة الثقيلة"
        return 1
    fi
}

# اختبار أداء قاعدة البيانات
run_database_test() {
    log_header "🗄️ اختبار أداء قاعدة البيانات"
    
    python3 database_performance_test.py --database-url "$DATABASE_URL" > "$RESULTS_DIR/database_test_$TIMESTAMP.log" 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "انتهى اختبار أداء قاعدة البيانات"
    else
        log_error "فشل اختبار أداء قاعدة البيانات"
        return 1
    fi
}

# مراقبة موارد النظام
monitor_system_resources() {
    log_header "📊 مراقبة موارد النظام"
    
    local duration=${1:-300}  # 5 دقائق افتراضياً
    local output_file="$RESULTS_DIR/system_monitor_$TIMESTAMP.log"
    
    {
        echo "تاريخ البدء: $(date)"
        echo "مدة المراقبة: $duration ثانية"
        echo "=================================="
        
        for ((i=1; i<=duration; i+=5)); do
            echo "الوقت: $(date '+%H:%M:%S')"
            echo "المعالج: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
            echo "الذاكرة: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
            echo "القرص: $(df -h / | awk 'NR==2{print $5}')"
            echo "الشبكة: $(cat /proc/net/dev | grep eth0 | awk '{print "RX: " $2 " TX: " $10}')"
            echo "الاتصالات: $(netstat -an | grep :5000 | wc -l)"
            echo "---"
            sleep 5
        done
        
        echo "انتهت المراقبة: $(date)"
    } > "$output_file" &
    
    local monitor_pid=$!
    log_info "بدأت مراقبة النظام (PID: $monitor_pid) لمدة $duration ثانية"
    
    return $monitor_pid
}

# اختبار الضغط المتدرج
run_stress_test() {
    log_header "💪 اختبار الضغط المتدرج"
    
    local scenarios=("light" "medium" "heavy")
    local wait_time=60  # انتظار دقيقة بين الاختبارات
    
    for scenario in "${scenarios[@]}"; do
        log_info "بدء اختبار $scenario..."
        
        # بدء مراقبة النظام
        monitor_system_resources 600 &  # 10 دقائق
        local monitor_pid=$!
        
        # تشغيل اختبار الحمولة
        python3 load_testing.py --scenario "$scenario" --url "$APP_URL" > "$RESULTS_DIR/stress_${scenario}_$TIMESTAMP.log" 2>&1
        
        # إيقاف مراقبة النظام
        kill $monitor_pid 2>/dev/null || true
        
        if [ $? -eq 0 ]; then
            log_success "انتهى اختبار $scenario"
        else
            log_error "فشل اختبار $scenario"
        fi
        
        # انتظار بين الاختبارات
        if [ "$scenario" != "heavy" ]; then
            log_info "انتظار $wait_time ثانية قبل الاختبار التالي..."
            sleep $wait_time
        fi
    done
}

# تحليل النتائج
analyze_results() {
    log_header "📈 تحليل النتائج"
    
    local summary_file="$RESULTS_DIR/test_summary_$TIMESTAMP.md"
    
    {
        echo "# ملخص اختبارات الأداء"
        echo "تاريخ الاختبار: $(date)"
        echo "رابط التطبيق: $APP_URL"
        echo ""
        
        echo "## ملفات النتائج"
        for file in "$RESULTS_DIR"/*"$TIMESTAMP"*; do
            if [ -f "$file" ]; then
                local size=$(du -h "$file" | cut -f1)
                echo "- $(basename "$file") ($size)"
            fi
        done
        
        echo ""
        echo "## معلومات النظام"
        echo "- نظام التشغيل: $(uname -a)"
        echo "- المعالج: $(nproc) cores"
        echo "- الذاكرة: $(free -h | grep Mem | awk '{print $2}')"
        echo "- القرص المتاح: $(df -h / | awk 'NR==2{print $4}')"
        
        echo ""
        echo "## ملاحظات"
        echo "- تم حفظ جميع النتائج في مجلد: $RESULTS_DIR"
        echo "- يمكن مراجعة الملفات الفردية للحصول على تفاصيل أكثر"
        echo "- تأكد من مراجعة ملفات السجل للأخطاء"
        
    } > "$summary_file"
    
    log_success "تم إنشاء ملخص النتائج: $summary_file"
}

# عرض المساعدة
show_help() {
    echo "استخدام: $0 [OPTIONS]"
    echo ""
    echo "الخيارات:"
    echo "  --light         تشغيل اختبار الحمولة الخفيفة فقط"
    echo "  --medium        تشغيل اختبار الحمولة المتوسطة فقط"
    echo "  --heavy         تشغيل اختبار الحمولة الثقيلة فقط"
    echo "  --database      تشغيل اختبار قاعدة البيانات فقط"
    echo "  --stress        تشغيل اختبار الضغط المتدرج"
    echo "  --all           تشغيل جميع الاختبارات (افتراضي)"
    echo "  --url URL       رابط التطبيق (افتراضي: $APP_URL)"
    echo "  --db-url URL    رابط قاعدة البيانات"
    echo "  --help          عرض هذه المساعدة"
    echo ""
    echo "أمثلة:"
    echo "  $0 --light"
    echo "  $0 --stress"
    echo "  $0 --url http://localhost:8000"
}

# معالجة المعاملات
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --light)
                TEST_TYPE="light"
                shift
                ;;
            --medium)
                TEST_TYPE="medium"
                shift
                ;;
            --heavy)
                TEST_TYPE="heavy"
                shift
                ;;
            --database)
                TEST_TYPE="database"
                shift
                ;;
            --stress)
                TEST_TYPE="stress"
                shift
                ;;
            --all)
                TEST_TYPE="all"
                shift
                ;;
            --url)
                APP_URL="$2"
                shift 2
                ;;
            --db-url)
                DATABASE_URL="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "معامل غير معروف: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # تعيين النوع الافتراضي
    TEST_TYPE=${TEST_TYPE:-"all"}
}

# الدالة الرئيسية
main() {
    log_header "🚀 بدء اختبارات الأداء لتطبيق Ta9affi"
    echo "=============================================="
    
    # إنشاء مجلد النتائج
    create_results_dir
    
    # التحقق من المتطلبات
    check_requirements
    
    # تفعيل البيئة الافتراضية
    activate_venv
    
    local start_time=$(date +%s)
    local tests_passed=0
    local tests_failed=0
    
    # تشغيل الاختبارات حسب النوع
    case $TEST_TYPE in
        "light")
            run_light_load_test && ((tests_passed++)) || ((tests_failed++))
            ;;
        "medium")
            run_medium_load_test && ((tests_passed++)) || ((tests_failed++))
            ;;
        "heavy")
            run_heavy_load_test && ((tests_passed++)) || ((tests_failed++))
            ;;
        "database")
            run_database_test && ((tests_passed++)) || ((tests_failed++))
            ;;
        "stress")
            run_stress_test
            ;;
        "all")
            run_light_load_test && ((tests_passed++)) || ((tests_failed++))
            sleep 30
            run_medium_load_test && ((tests_passed++)) || ((tests_failed++))
            sleep 30
            run_database_test && ((tests_passed++)) || ((tests_failed++))
            ;;
    esac
    
    # تحليل النتائج
    analyze_results
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # عرض النتائج النهائية
    echo "=============================================="
    log_header "📊 ملخص النتائج النهائية"
    echo "⏱️  إجمالي الوقت: $duration ثانية"
    echo "✅ الاختبارات الناجحة: $tests_passed"
    echo "❌ الاختبارات الفاشلة: $tests_failed"
    echo "📁 مجلد النتائج: $RESULTS_DIR"
    echo "=============================================="
    
    if [ $tests_failed -eq 0 ]; then
        log_success "🎉 تم إنجاز جميع الاختبارات بنجاح!"
    else
        log_warning "⚠️ بعض الاختبارات فشلت. راجع ملفات السجل للتفاصيل."
    fi
}

# معالجة المعاملات وتشغيل البرنامج
parse_arguments "$@"
main
