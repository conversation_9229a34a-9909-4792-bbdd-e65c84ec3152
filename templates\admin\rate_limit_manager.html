{% extends "base.html" %}

{% block title %}إدارة حدود النظام - Ta9affi{% endblock %}

{% block extra_css %}
<style>
    .rate-limit-card {
        transition: transform 0.2s;
    }

    .rate-limit-card:hover {
        transform: translateY(-2px);
    }

    .settings-form {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }

    .quick-actions .btn {
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shield-alt text-primary"></i> إدارة حدود النظام (Rate Limiting)</h2>
                <div>
                    <button class="btn btn-warning btn-sm" onclick="resetAllLimits()">
                        <i class="fas fa-redo"></i> إعادة تعيين جميع الحدود
                    </button>
                    <a href="{{ url_for('admin_rate_limit.history') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-history"></i> سجل التغييرات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ current_settings.add_progress_limit }}</h4>
                            <p class="mb-0">حد الإضافة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-plus-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ current_settings.delete_progress_limit }}</h4>
                            <p class="mb-0">حد الحذف</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-trash-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ current_settings.add_progress_window_hours }}</h4>
                            <p class="mb-0">نافذة الإضافة (ساعة)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_overrides }}</h4>
                            <p class="mb-0">تخصيصات نشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-cog fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الإعدادات العامة -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> الإعدادات العامة</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin_rate_limit.update_global_settings') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary"><i class="fas fa-plus-circle"></i> إعدادات إضافة التقدمات</h6>
                                <div class="form-group">
                                    <label for="add_progress_limit">الحد الأقصى للإضافات:</label>
                                    <input type="number" class="form-control" id="add_progress_limit"
                                        name="add_progress_limit" value="{{ current_settings.add_progress_limit }}"
                                        min="1" max="100" required>
                                    <small class="form-text text-muted">عدد التقدمات التي يمكن إضافتها</small>
                                </div>
                                <div class="form-group">
                                    <label for="add_progress_window_hours">النافذة الزمنية (ساعة):</label>
                                    <input type="number" class="form-control" id="add_progress_window_hours"
                                        name="add_progress_window_hours"
                                        value="{{ current_settings.add_progress_window_hours }}" min="1" max="168"
                                        required>
                                    <small class="form-text text-muted">المدة الزمنية للحد (بالساعات)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger"><i class="fas fa-trash-alt"></i> إعدادات حذف التقدمات</h6>
                                <div class="form-group">
                                    <label for="delete_progress_limit">الحد الأقصى للحذف:</label>
                                    <input type="number" class="form-control" id="delete_progress_limit"
                                        name="delete_progress_limit"
                                        value="{{ current_settings.delete_progress_limit }}" min="1" max="50" required>
                                    <small class="form-text text-muted">عدد التقدمات التي يمكن حذفها</small>
                                </div>
                                <div class="form-group">
                                    <label for="delete_progress_window_hours">النافذة الزمنية (ساعة):</label>
                                    <input type="number" class="form-control" id="delete_progress_window_hours"
                                        name="delete_progress_window_hours"
                                        value="{{ current_settings.delete_progress_window_hours }}" min="1" max="168"
                                        required>
                                    <small class="form-text text-muted">المدة الزمنية للحد (بالساعات)</small>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="notes">ملاحظات:</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                placeholder="سبب التغيير أو ملاحظات إضافية">{{ current_settings.notes or '' }}</textarea>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- المستخدمين مع تخصيصات -->
            {% if users_with_overrides %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-users-cog"></i> المستخدمين مع تخصيصات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>حد الإضافة</th>
                                    <th>نافذة الإضافة</th>
                                    <th>حد الحذف</th>
                                    <th>نافذة الحذف</th>
                                    <th>انتهاء الصلاحية</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for override, user in users_with_overrides %}
                                <tr>
                                    <td>
                                        <strong>{{ user.username }}</strong><br>
                                        <small class="text-muted">{{ user.email }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">
                                            {{ override.add_progress_limit or 'افتراضي' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ override.add_progress_window_hours or 'افتراضي' }}ساعة
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-danger">
                                            {{ override.delete_progress_limit or 'افتراضي' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-warning">
                                            {{ override.delete_progress_window_hours or 'افتراضي' }}ساعة
                                        </span>
                                    </td>
                                    <td>
                                        {% if override.expires_at %}
                                        <small class="text-muted">
                                            {{ override.expires_at.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                        {% else %}
                                        <span class="text-success">دائم</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <form method="POST"
                                            action="{{ url_for('admin_rate_limit.remove_user_override', user_id=user.id) }}"
                                            style="display: inline;"
                                            onsubmit="return confirm('هل أنت متأكد من إزالة التخصيص؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-times"></i> إزالة
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- أدوات سريعة -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> أدوات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('admin_rate_limit.user_override') }}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus"></i> تخصيص حدود مستخدم
                        </a>
                        <button class="btn btn-outline-info" onclick="showCurrentSettings()">
                            <i class="fas fa-eye"></i> عرض الإعدادات الحالية
                        </button>
                        <button class="btn btn-outline-warning" onclick="resetAllLimits()">
                            <i class="fas fa-redo"></i> إعادة تعيين الحدود
                        </button>
                    </div>
                </div>
            </div>

            <!-- آخر التغييرات -->
            {% if recent_changes %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> آخر التغييرات</h5>
                </div>
                <div class="card-body">
                    {% for change in recent_changes %}
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">
                                {{ change.changed_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                            <span class="badge badge-secondary">{{ change.change_type }}</span>
                        </div>
                        {% if change.reason %}
                        <p class="mb-0 small">{{ change.reason }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                    <div class="text-center mt-3">
                        <a href="{{ url_for('admin_rate_limit.history') }}" class="btn btn-sm btn-outline-info">
                            عرض جميع التغييرات
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- معلومات النظام -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <p><strong>آخر تحديث:</strong><br>
                        <small class="text-muted">{{ current_settings.updated_at.strftime('%Y-%m-%d %H:%M') if
                            current_settings.updated_at else 'غير محدد' }}</small>
                    </p>

                    <p><strong>حالة النظام:</strong><br>
                        <span class="badge badge-success">نشط</span>
                    </p>

                    <p><strong>نوع التخزين:</strong><br>
                        <small class="text-muted">
                            {% if rate_limiting_enabled %}
                            Redis / محلي
                            {% else %}
                            معطل
                            {% endif %}
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض الإعدادات -->
<div class="modal fade" id="settingsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الإعدادات الحالية</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="settingsContent">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
    function showCurrentSettings() {
        fetch('{{ url_for("admin_rate_limit.api_current_settings") }}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const settings = data.settings;
                    const content = `
                    <div class="row">
                        <div class="col-6">
                            <h6 class="text-primary">إضافة التقدمات:</h6>
                            <p>الحد الأقصى: <strong>${settings.add_progress_limit}</strong></p>
                            <p>النافذة الزمنية: <strong>${settings.add_progress_window_hours} ساعة</strong></p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-danger">حذف التقدمات:</h6>
                            <p>الحد الأقصى: <strong>${settings.delete_progress_limit}</strong></p>
                            <p>النافذة الزمنية: <strong>${settings.delete_progress_window_hours} ساعة</strong></p>
                        </div>
                    </div>
                    <hr>
                    <p><strong>آخر تحديث:</strong> ${settings.updated_at || 'غير محدد'}</p>
                    ${settings.notes ? `<p><strong>ملاحظات:</strong> ${settings.notes}</p>` : ''}
                `;
                    document.getElementById('settingsContent').innerHTML = content;
                    $('#settingsModal').modal('show');
                }
            })
            .catch(error => {
                alert('حدث خطأ في جلب الإعدادات');
                console.error(error);
            });
    }

    function resetAllLimits() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع حدود المستخدمين؟\nهذا سيمحو جميع البيانات المحفوظة.')) {
            fetch('{{ url_for("admin_rate_limit.api_reset_all_limits") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إعادة تعيين جميع الحدود بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                    console.error(error);
                });
        }
    }
</script>
{% endblock %}