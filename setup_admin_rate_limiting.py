#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد شامل لنظام إدارة Rate Limiting للأدمن
"""

import sys
import os
from datetime import datetime

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_files = [
        'app.py',
        'rate_limiter.py', 
        'rate_limit_monitor.py',
        'rate_limit_settings.py',
        'admin_rate_limit_manager.py',
        'templates/admin/rate_limit_manager.html',
        'templates/admin/user_rate_limit_override.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n📊 إعداد قاعدة البيانات...")
    
    try:
        # تشغيل سكريبت إنشاء الجداول
        exec(open('create_rate_limit_tables.py').read())
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def test_system():
    """اختبار النظام"""
    print("\n🧪 اختبار النظام...")
    
    try:
        # اختبار الاستيرادات
        from rate_limiter import AdvancedRateLimiter
        from rate_limit_settings import RateLimitSettings
        from admin_rate_limit_manager import admin_rate_limit_bp
        
        print("✅ جميع الاستيرادات تعمل بشكل صحيح")
        
        # اختبار إنشاء مثيل
        limiter = AdvancedRateLimiter()
        print("✅ Rate Limiter يعمل بشكل صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def create_admin_user():
    """إنشاء مستخدم أدمن إذا لم يكن موجوداً"""
    print("\n👤 فحص مستخدم الأدمن...")
    
    try:
        from models_new import db, User, Role
        from app import app
        
        with app.app_context():
            # البحث عن أدمن موجود
            admin = User.query.filter_by(role=Role.ADMIN).first()
            
            if admin:
                print(f"✅ مستخدم أدمن موجود: {admin.username}")
                return True
            
            # إنشاء أدمن جديد
            print("🔧 إنشاء مستخدم أدمن جديد...")
            
            admin = User(
                username='admin',
                email='<EMAIL>',
                role=Role.ADMIN,
                is_active=True,
                wilaya='الجزائر'
            )
            admin.set_password('admin123')  # كلمة مرور افتراضية
            
            db.session.add(admin)
            db.session.commit()
            
            print("✅ تم إنشاء مستخدم أدمن:")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            print("   ⚠️ يُنصح بتغيير كلمة المرور فوراً")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء مستخدم الأدمن: {e}")
        return False

def show_success_message():
    """عرض رسالة النجاح والتعليمات"""
    print("\n" + "="*60)
    print("🎉 تم إعداد نظام إدارة Rate Limiting بنجاح!")
    print("="*60)
    
    print("\n🚀 الخطوات التالية:")
    print("1. شغل التطبيق:")
    print("   python app.py")
    print()
    print("2. سجل دخول كأدمن:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print()
    print("3. اذهب إلى لوحة تحكم الأدمن:")
    print("   http://localhost:5000/dashboard")
    print()
    print("4. ابحث عن 'إدارة حدود النظام' في أدوات الإدارة السريعة")
    print("   أو اذهب مباشرة إلى:")
    print("   http://localhost:5000/admin/rate-limit-manager/")
    print()
    
    print("🎛️ ما يمكنك فعله الآن:")
    print("✅ تغيير عدد الإضافات المسموحة (افتراضي: 10)")
    print("✅ تغيير عدد الحذف المسموح (افتراضي: 3)")
    print("✅ تغيير النافذة الزمنية (افتراضي: 12 ساعة)")
    print("✅ تخصيص حدود لمستخدمين محددين")
    print("✅ مراقبة استخدام النظام")
    print("✅ مراجعة سجل التغييرات")
    print()
    
    print("📖 للمزيد من التفاصيل:")
    print("   راجع ملف: ADMIN_RATE_LIMITING_GUIDE.md")
    print()
    
    print("⚠️ تذكير أمني:")
    print("   - غير كلمة مرور الأدمن فوراً")
    print("   - راجع الإعدادات الافتراضية")
    print("   - اختبر النظام مع مستخدم تجريبي")

def main():
    """الدالة الرئيسية"""
    print("🛡️ إعداد نظام إدارة Rate Limiting للأدمن")
    print("="*60)
    print(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        print("تأكد من وجود جميع الملفات المطلوبة")
        return False
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("\n❌ فشل في إعداد قاعدة البيانات")
        return False
    
    # اختبار النظام
    if not test_system():
        print("\n❌ فشل في اختبار النظام")
        return False
    
    # إنشاء مستخدم أدمن
    if not create_admin_user():
        print("\n❌ فشل في إعداد مستخدم الأدمن")
        return False
    
    # عرض رسالة النجاح
    show_success_message()
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 النظام جاهز للاستخدام!")
    else:
        print("\n💥 فشل في الإعداد - راجع الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
