#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحضير Ta9affi للنشر مع Dokploy
"""

import os
import shutil
import subprocess
from datetime import datetime

def create_deployment_package():
    """إنشاء حزمة النشر"""
    
    print("📦 إنشاء حزمة النشر لـ Dokploy...")
    
    # الملفات الأساسية المطلوبة
    essential_files = [
        'app.py',
        'models_new.py',
        'subscription_manager.py',
        'rate_limiter.py',
        'rate_limit_monitor.py',
        'rate_limit_settings.py',
        'admin_rate_limit_manager.py',
        'config_production.py',
        'requirements_production.txt',
        'gunicorn.conf.py',
        'Dockerfile',
        'docker-compose.prod.yml',
        '.dockerignore',
        '.env.example',
        '.env.dokploy',
        'ta9affi.db',
        'DOKPLOY_QUICK_START.md',
        'DOKPLOY_DEPLOYMENT_GUIDE.md'
    ]
    
    # المجلدات المطلوبة
    essential_dirs = [
        'templates',
        'static'
    ]
    
    # التحقق من وجود الملفات
    missing_files = []
    for file_name in essential_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
    
    for dir_name in essential_dirs:
        if not os.path.exists(dir_name):
            missing_files.append(f"{dir_name}/")
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file_name in missing_files:
            print(f"   - {file_name}")
        return False
    
    print("✅ جميع الملفات الأساسية موجودة")
    return True

def create_tar_package():
    """إنشاء ملف tar للرفع"""
    
    print("\n📦 إنشاء حزمة tar...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    package_name = f"ta9affi-dokploy-{timestamp}.tar.gz"
    
    # الملفات والمجلدات المراد ضغطها
    include_items = [
        'app.py',
        'models_new.py',
        'subscription_manager.py',
        'rate_limiter.py',
        'rate_limit_monitor.py',
        'rate_limit_settings.py',
        'admin_rate_limit_manager.py',
        'config_production.py',
        'requirements_production.txt',
        'gunicorn.conf.py',
        'Dockerfile',
        'docker-compose.prod.yml',
        '.dockerignore',
        '.env.example',
        '.env.dokploy',
        'ta9affi.db',
        'templates/',
        'static/',
        'DOKPLOY_QUICK_START.md',
        'DOKPLOY_DEPLOYMENT_GUIDE.md'
    ]
    
    # إنشاء الأمر
    cmd = ['tar', '-czf', package_name] + include_items
    
    try:
        subprocess.run(cmd, check=True)
        
        # حساب حجم الملف
        size = os.path.getsize(package_name)
        size_mb = size / (1024 * 1024)
        
        print(f"✅ تم إنشاء الحزمة: {package_name}")
        print(f"📊 الحجم: {size_mb:.1f} MB")
        
        return package_name
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إنشاء الحزمة: {e}")
        return None

def show_deployment_instructions(package_name):
    """عرض تعليمات النشر"""
    
    print(f"\n🚀 تعليمات النشر مع Dokploy:")
    print("=" * 60)
    
    print(f"\n1. 📁 رفع الحزمة للسيرفر:")
    print(f"   scp {package_name} root@your-server-ip:/opt/")
    
    print(f"\n2. 🖥️ على السيرفر:")
    print(f"   ssh root@your-server-ip")
    print(f"   cd /opt")
    print(f"   tar -xzf {package_name}")
    print(f"   mv ta9affi ta9affi-app")
    print(f"   chown -R dokploy:dokploy ta9affi-app")
    
    print(f"\n3. 🌐 في Dokploy (https://your-server-ip:3000):")
    print(f"   - إنشاء مشروع جديد: ta9affi")
    print(f"   - إضافة تطبيق: Docker Compose")
    print(f"   - المسار: /opt/ta9affi-app")
    print(f"   - ملف التكوين: docker-compose.prod.yml")
    
    print(f"\n4. ⚙️ متغيرات البيئة:")
    print(f"   - انسخ من .env.dokploy")
    print(f"   - أو اتبع DOKPLOY_QUICK_START.md")
    
    print(f"\n5. 🌐 إعداد النطاق:")
    print(f"   - أضف ta9affi.com")
    print(f"   - فعل SSL (Let's Encrypt)")
    
    print(f"\n6. 🚀 النشر:")
    print(f"   - اضغط Build")
    print(f"   - اضغط Deploy")
    print(f"   - اختبر: https://ta9affi.com")

def show_quick_checklist():
    """عرض قائمة مراجعة سريعة"""
    
    print(f"\n✅ قائمة المراجعة السريعة:")
    print("=" * 40)
    
    checklist = [
        "السيرفر جاهز مع Ubuntu 20.04+",
        "Dokploy مثبت ويعمل",
        "النطاق موجه للسيرفر",
        "حزمة Ta9affi جاهزة",
        "متغيرات البيئة محضرة",
        "دليل النشر متاح"
    ]
    
    for i, item in enumerate(checklist, 1):
        print(f"   {i}. [ ] {item}")
    
    print(f"\n📚 الأدلة المتاحة:")
    print(f"   - DOKPLOY_QUICK_START.md (30 دقيقة)")
    print(f"   - DOKPLOY_DEPLOYMENT_GUIDE.md (مفصل)")
    print(f"   - .env.dokploy (متغيرات البيئة)")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 تحضير Ta9affi للنشر مع Dokploy")
    print("=" * 50)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # التحقق من الملفات
    if not create_deployment_package():
        print("\n❌ لا يمكن المتابعة - ملفات مفقودة")
        return
    
    # إنشاء حزمة النشر
    package_name = create_tar_package()
    
    if not package_name:
        print("\n❌ فشل في إنشاء حزمة النشر")
        return
    
    # عرض التعليمات
    show_deployment_instructions(package_name)
    show_quick_checklist()
    
    print(f"\n🎉 Ta9affi جاهز للنشر مع Dokploy!")
    print(f"📦 الحزمة: {package_name}")
    print(f"⏱️ وقت النشر المتوقع: 30 دقيقة")
    
    print(f"\n🔗 الروابط المفيدة:")
    print(f"   - Dokploy: https://dokploy.com")
    print(f"   - التوثيق: https://docs.dokploy.com")

if __name__ == "__main__":
    main()
