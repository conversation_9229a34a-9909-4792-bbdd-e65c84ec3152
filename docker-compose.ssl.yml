version: '3.8'

services:
  ta9affi-ssl:
    build:
      context: .
      dockerfile: Dockerfile.ssl
    container_name: ta9affi-ssl
    ports:
      - "80:80"
      - "443:443"
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SSL_MODE=dual  # أو true للـ SSL فقط
      - SSL_CERT_PATH=/app/ssl_certs/ta9affi.crt
      - SSL_KEY_PATH=/app/ssl_certs/ta9affi.key
      - CHARGILY_PUBLIC_KEY=${CHARGILY_PUBLIC_KEY}
      - CHARGILY_SECRET_KEY=${CHARGILY_SECRET_KEY}
      - BASE_URL=https://ta9affi.com
      - CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook
    volumes:
      - ./ssl_certs:/app/ssl_certs:ro
      - ./data:/app/data
      - /etc/letsencrypt:/etc/letsencrypt:ro  # للشهادات الحقيقية
    restart: unless-stopped
    networks:
      - ta9affi-network

  # Redis للـ Rate Limiting
  redis:
    image: redis:7-alpine
    container_name: ta9affi-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - ta9affi-network

networks:
  ta9affi-network:
    driver: bridge

volumes:
  redis_data:
