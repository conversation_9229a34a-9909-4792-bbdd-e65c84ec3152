#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام حماية الملفات والتحميلات
يوفر حماية شاملة لعمليات رفع وتحميل الملفات
"""

import os
import hashlib
import magic
import logging
from pathlib import Path
from functools import wraps
from flask import request, abort, current_app, send_file
from werkzeug.utils import secure_filename
import mimetypes

class FileSecurityManager:
    """مدير أمان الملفات"""
    
    def __init__(self, app=None):
        self.app = app
        
        # أنواع الملفات المسموحة
        self.allowed_extensions = {
            'images': {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'},
            'documents': {'pdf', 'doc', 'docx', 'txt', 'rtf'},
            'spreadsheets': {'xls', 'xlsx', 'csv'},
            'presentations': {'ppt', 'pptx'},
            'archives': {'zip', 'rar', '7z'},
            'audio': {'mp3', 'wav', 'ogg', 'aac'},
            'video': {'mp4', 'avi', 'mkv', 'mov', 'wmv'}
        }
        
        # أنواع MIME المسموحة
        self.allowed_mimes = {
            # صور
            'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
            # مستندات
            'application/pdf', 'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain', 'application/rtf',
            # جداول بيانات
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/csv',
            # عروض تقديمية
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            # أرشيف
            'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
            # صوت
            'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/aac',
            # فيديو
            'video/mp4', 'video/x-msvideo', 'video/x-matroska', 'video/quicktime'
        }
        
        # الحد الأقصى لحجم الملف (بالبايت)
        self.max_file_sizes = {
            'images': 5 * 1024 * 1024,      # 5MB
            'documents': 10 * 1024 * 1024,  # 10MB
            'spreadsheets': 10 * 1024 * 1024,  # 10MB
            'presentations': 20 * 1024 * 1024,  # 20MB
            'archives': 50 * 1024 * 1024,   # 50MB
            'audio': 20 * 1024 * 1024,      # 20MB
            'video': 100 * 1024 * 1024      # 100MB
        }
        
        # أنماط أسماء الملفات الخطيرة
        self.dangerous_patterns = [
            r'\.php$', r'\.asp$', r'\.aspx$', r'\.jsp$', r'\.js$',
            r'\.exe$', r'\.bat$', r'\.cmd$', r'\.com$', r'\.scr$',
            r'\.vbs$', r'\.jar$', r'\.class$', r'\.sh$', r'\.py$',
            r'\.pl$', r'\.rb$', r'\.go$', r'\.c$', r'\.cpp$'
        ]
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير أمان الملفات"""
        self.app = app
        
        # تحديث الإعدادات من التطبيق
        self.upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        self.max_content_length = app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)
        
        # إنشاء مجلد التحميل إذا لم يكن موجوداً
        os.makedirs(self.upload_folder, exist_ok=True)
        
        logging.info("✅ تم تهيئة مدير أمان الملفات")
    
    def get_file_category(self, filename):
        """تحديد فئة الملف"""
        extension = self.get_file_extension(filename)
        
        for category, extensions in self.allowed_extensions.items():
            if extension in extensions:
                return category
        
        return None
    
    def get_file_extension(self, filename):
        """الحصول على امتداد الملف"""
        return filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    def is_allowed_file(self, filename):
        """فحص ما إذا كان الملف مسموح"""
        if not filename:
            return False
        
        # فحص وجود امتداد
        if '.' not in filename:
            return False
        
        extension = self.get_file_extension(filename)
        
        # فحص الامتداد
        for extensions in self.allowed_extensions.values():
            if extension in extensions:
                return True
        
        return False
    
    def is_dangerous_filename(self, filename):
        """فحص ما إذا كان اسم الملف خطير"""
        import re
        
        for pattern in self.dangerous_patterns:
            if re.search(pattern, filename, re.IGNORECASE):
                return True
        
        return False
    
    def validate_file_content(self, file_path):
        """التحقق من محتوى الملف"""
        try:
            # فحص نوع MIME الحقيقي
            mime_type = magic.from_file(file_path, mime=True)
            
            if mime_type not in self.allowed_mimes:
                return False, f"نوع الملف غير مسموح: {mime_type}"
            
            # فحص إضافي للصور
            if mime_type.startswith('image/'):
                return self.validate_image_file(file_path)
            
            # فحص إضافي للمستندات
            elif mime_type == 'application/pdf':
                return self.validate_pdf_file(file_path)
            
            return True, "الملف آمن"
            
        except Exception as e:
            logging.error(f"Error validating file content: {str(e)}")
            return False, "خطأ في فحص الملف"
    
    def validate_image_file(self, file_path):
        """التحقق من ملف الصورة"""
        try:
            from PIL import Image
            
            # محاولة فتح الصورة
            with Image.open(file_path) as img:
                # فحص الأبعاد
                width, height = img.size
                
                # حد أقصى للأبعاد
                max_dimension = 10000
                if width > max_dimension or height > max_dimension:
                    return False, "أبعاد الصورة كبيرة جداً"
                
                # فحص تنسيق الصورة
                if img.format.lower() not in ['jpeg', 'png', 'gif', 'bmp', 'webp']:
                    return False, "تنسيق الصورة غير مدعوم"
                
                return True, "الصورة آمنة"
                
        except Exception as e:
            return False, f"ملف الصورة تالف: {str(e)}"
    
    def validate_pdf_file(self, file_path):
        """التحقق من ملف PDF"""
        try:
            # قراءة بداية الملف للتحقق من header
            with open(file_path, 'rb') as f:
                header = f.read(8)
                
                # التحقق من PDF header
                if not header.startswith(b'%PDF-'):
                    return False, "ملف PDF غير صالح"
                
                # فحص حجم الملف
                f.seek(0, 2)  # الذهاب لنهاية الملف
                size = f.tell()
                
                max_pdf_size = self.max_file_sizes.get('documents', 10 * 1024 * 1024)
                if size > max_pdf_size:
                    return False, "حجم ملف PDF كبير جداً"
                
                return True, "ملف PDF آمن"
                
        except Exception as e:
            return False, f"خطأ في فحص ملف PDF: {str(e)}"
    
    def sanitize_filename(self, filename):
        """تنظيف اسم الملف"""
        # استخدام secure_filename من werkzeug
        filename = secure_filename(filename)
        
        # إزالة الأحرف الخطيرة إضافياً
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        for char in dangerous_chars:
            filename = filename.replace(char, '_')
        
        # تحديد الطول
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:250] + ext
        
        return filename
    
    def generate_safe_filename(self, original_filename):
        """إنشاء اسم ملف آمن وفريد"""
        # تنظيف الاسم الأصلي
        clean_name = self.sanitize_filename(original_filename)
        
        # إضافة timestamp و hash للتفرد
        import time
        timestamp = str(int(time.time()))
        
        name, ext = os.path.splitext(clean_name)
        hash_part = hashlib.md5(f"{name}{timestamp}".encode()).hexdigest()[:8]
        
        return f"{name}_{timestamp}_{hash_part}{ext}"
    
    def save_uploaded_file(self, file, subfolder='general'):
        """حفظ الملف المرفوع بأمان"""
        if not file or not file.filename:
            return None, "لم يتم اختيار ملف"
        
        # فحص اسم الملف
        if self.is_dangerous_filename(file.filename):
            return None, "اسم الملف غير آمن"
        
        # فحص نوع الملف
        if not self.is_allowed_file(file.filename):
            return None, "نوع الملف غير مسموح"
        
        # تحديد فئة الملف
        category = self.get_file_category(file.filename)
        if not category:
            return None, "فئة الملف غير معروفة"
        
        # فحص حجم الملف
        file.seek(0, 2)  # الذهاب لنهاية الملف
        size = file.tell()
        file.seek(0)  # العودة للبداية
        
        max_size = self.max_file_sizes.get(category, 5 * 1024 * 1024)
        if size > max_size:
            return None, f"حجم الملف كبير جداً. الحد الأقصى: {max_size // (1024*1024)}MB"
        
        # إنشاء اسم ملف آمن
        safe_filename = self.generate_safe_filename(file.filename)
        
        # إنشاء مسار الحفظ
        save_folder = os.path.join(self.upload_folder, subfolder, category)
        os.makedirs(save_folder, exist_ok=True)
        
        file_path = os.path.join(save_folder, safe_filename)
        
        try:
            # حفظ الملف
            file.save(file_path)
            
            # التحقق من محتوى الملف
            is_valid, message = self.validate_file_content(file_path)
            
            if not is_valid:
                # حذف الملف إذا لم يكن آمن
                os.remove(file_path)
                return None, message
            
            # تسجيل العملية
            logging.getLogger('security').info(
                f"FILE_UPLOADED - {safe_filename} - Size: {size} - Category: {category}"
            )
            
            return file_path, "تم رفع الملف بنجاح"
            
        except Exception as e:
            # حذف الملف في حالة الخطأ
            if os.path.exists(file_path):
                os.remove(file_path)
            
            logging.error(f"Error saving uploaded file: {str(e)}")
            return None, "خطأ في حفظ الملف"
    
    def secure_file_download(self, file_path, filename=None):
        """تحميل آمن للملفات"""
        # التحقق من وجود الملف
        if not os.path.exists(file_path):
            abort(404)
        
        # التحقق من أن الملف داخل مجلد التحميل
        upload_path = os.path.abspath(self.upload_folder)
        requested_path = os.path.abspath(file_path)
        
        if not requested_path.startswith(upload_path):
            abort(403)
        
        # تحديد اسم الملف للتحميل
        download_name = filename or os.path.basename(file_path)
        download_name = self.sanitize_filename(download_name)
        
        # تسجيل العملية
        logging.getLogger('security').info(
            f"FILE_DOWNLOADED - {download_name} - IP: {request.remote_addr}"
        )
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=download_name
        )
    
    def delete_file_secure(self, file_path):
        """حذف آمن للملفات"""
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                return False, "الملف غير موجود"
            
            # التحقق من أن الملف داخل مجلد التحميل
            upload_path = os.path.abspath(self.upload_folder)
            requested_path = os.path.abspath(file_path)
            
            if not requested_path.startswith(upload_path):
                return False, "غير مسموح بحذف هذا الملف"
            
            # حذف الملف
            os.remove(file_path)
            
            # تسجيل العملية
            logging.getLogger('security').info(
                f"FILE_DELETED - {os.path.basename(file_path)}"
            )
            
            return True, "تم حذف الملف بنجاح"
            
        except Exception as e:
            logging.error(f"Error deleting file: {str(e)}")
            return False, "خطأ في حذف الملف"
    
    def scan_uploaded_files(self):
        """فحص الملفات المرفوعة للتأكد من أمانها"""
        issues = []
        
        try:
            for root, dirs, files in os.walk(self.upload_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # فحص اسم الملف
                    if self.is_dangerous_filename(file):
                        issues.append(f"اسم ملف خطير: {file_path}")
                    
                    # فحص محتوى الملف
                    is_valid, message = self.validate_file_content(file_path)
                    if not is_valid:
                        issues.append(f"محتوى ملف غير آمن: {file_path} - {message}")
            
            return issues
            
        except Exception as e:
            logging.error(f"Error scanning uploaded files: {str(e)}")
            return [f"خطأ في فحص الملفات: {str(e)}"]

# إنشاء مثيل عام لمدير أمان الملفات
file_security = FileSecurityManager()

# ديكوريتر للحماية عند رفع الملفات
def secure_file_upload(allowed_categories=None):
    """ديكوريتر للحماية عند رفع الملفات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # فحص وجود ملفات
            if not request.files:
                return jsonify({'error': 'لم يتم إرسال ملفات'}), 400
            
            # فحص كل ملف
            for file_key in request.files:
                file = request.files[file_key]
                
                if file and file.filename:
                    # فحص نوع الملف
                    if not file_security.is_allowed_file(file.filename):
                        return jsonify({'error': f'نوع الملف غير مسموح: {file.filename}'}), 400
                    
                    # فحص الفئة إذا تم تحديدها
                    if allowed_categories:
                        category = file_security.get_file_category(file.filename)
                        if category not in allowed_categories:
                            return jsonify({'error': f'فئة الملف غير مسموحة: {category}'}), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
