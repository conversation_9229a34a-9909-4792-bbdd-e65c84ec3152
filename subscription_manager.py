"""
نظام إدارة الاشتراكات - Ta9affi
إعادة إنشاء كاملة مع إصلاح مشاكل SSL وChargily
"""

# إصلاح مشكلة SSL مع gevent للـ Chargily API
import os
if os.environ.get('FLASK_ENV') == 'production':
    try:
        import gevent.monkey
        # patch SSL فقط للـ requests
        gevent.monkey.patch_socket()
        gevent.monkey.patch_ssl()
        print("✅ تم تطبيق gevent SSL patch في subscription_manager")
    except ImportError:
        print("⚠️ gevent غير متاح في subscription_manager")

import requests
import json
import os
import time
import uuid
import ssl
import urllib3
from datetime import datetime, timedelta
from flask import current_app
from models_new import db, User, Subscription, SubscriptionPlan, Payment, SubscriptionNotification, SubscriptionExtensionLog, Role

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ChargilyClient:
    """عميل Chargily محسن مع معالجة SSL قوية"""
    
    def __init__(self, public_key, secret_key):
        self.public_key = public_key
        self.secret_key = secret_key
        self.base_url = "https://pay.chargily.dz/api/v2"
        
        # إعداد session مع SSL آمن
        self.session = requests.Session()
        self.session.verify = False  # تعطيل SSL verification
        
        # Headers أساسية
        self.session.headers.update({
            'Authorization': f'Bearer {secret_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Ta9affi/1.0'
        })
        
        print("✅ [ChargilyClient] تم تهيئة العميل بنجاح")
    
    def _make_request(self, method, endpoint, data=None):
        """تنفيذ طلب HTTP مع معالجة أخطاء شاملة"""
        
        # URLs للتجربة (HTTPS ثم HTTP)
        urls = [
            f"https://pay.chargily.dz/api/v2/{endpoint}",
            f"http://pay.chargily.dz/api/v2/{endpoint}"
        ]
        
        for url_index, url in enumerate(urls):
            protocol = "HTTPS" if url.startswith('https') else "HTTP"
            print(f"🔄 [Chargily] {protocol} {method.upper()} {endpoint}")
            
            try:
                # تنفيذ الطلب
                if method.upper() == 'POST':
                    response = self.session.post(url, json=data, timeout=20)
                elif method.upper() == 'GET':
                    response = self.session.get(url, timeout=20)
                else:
                    continue
                
                print(f"📊 [Chargily] Response: {response.status_code}")
                
                # فحص الاستجابة
                if response.status_code in [200, 201]:
                    try:
                        result = response.json()
                        print(f"✅ [Chargily] نجح {protocol} {method.upper()}")
                        return result
                    except json.JSONDecodeError:
                        print(f"❌ [Chargily] خطأ في تحليل JSON")
                        continue
                else:
                    print(f"❌ [Chargily] HTTP {response.status_code}: {response.text[:200]}")
                    continue
                    
            except (requests.exceptions.SSLError, ssl.SSLError) as e:
                print(f"❌ [Chargily] SSL Error: {str(e)[:100]}...")
                if protocol == "HTTPS":
                    print("🔄 [Chargily] التبديل إلى HTTP...")
                    continue
                else:
                    break
                    
            except Exception as e:
                print(f"❌ [Chargily] خطأ: {str(e)[:100]}...")
                continue
        
        # إذا فشلت جميع المحاولات
        raise Exception("فشل في الاتصال بـ Chargily API")
    
    def create_product(self, name, description=""):
        """إنشاء منتج في Chargily"""
        data = {
            "name": name,
            "description": description
        }
        return self._make_request('POST', 'products', data)
    
    def create_price(self, amount, currency, product_id):
        """إنشاء سعر في Chargily"""
        data = {
            "amount": int(amount),
            "currency": currency.lower(),
            "product_id": product_id
        }
        return self._make_request('POST', 'prices', data)
    
    def create_checkout(self, items, success_url, failure_url, metadata=None):
        """إنشاء checkout في Chargily"""
        data = {
            "items": items,
            "success_url": success_url,
            "failure_url": failure_url
        }
        
        if metadata:
            data["metadata"] = metadata
            
        return self._make_request('POST', 'checkouts', data)

class SubscriptionManager:
    """مدير الاشتراكات الرئيسي"""
    
    def __init__(self):
        """تهيئة مدير الاشتراكات"""
        try:
            print("🔧 [SubscriptionManager] بدء التهيئة...")
            
            # تحديد البيئة
            self.environment = os.environ.get('FLASK_ENV', 'production')
            print(f"🌍 [SubscriptionManager] البيئة: {self.environment}")
            
            # إعداد URLs حسب البيئة
            if self.environment == 'development':
                self.base_url = "http://127.0.0.1:5000"
                self.webhook_url = "http://127.0.0.1:5000/chargily-webhook"
            else:
                self.base_url = "https://ta9affi.com"
                self.webhook_url = "https://ta9affi.com/chargily-webhook"
            
            print(f"🔗 [SubscriptionManager] Base URL: {self.base_url}")
            print(f"🔗 [SubscriptionManager] Webhook URL: {self.webhook_url}")
            
            # مفاتيح Chargily
            self.chargily_public_key = os.environ.get('CHARGILY_PUBLIC_KEY', 'live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk')
            self.chargily_secret_key = os.environ.get('CHARGILY_SECRET_KEY', 'live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU')
            
            # تهيئة عميل Chargily
            self.chargily = ChargilyClient(
                self.chargily_public_key,
                self.chargily_secret_key
            )
            
            print("✅ [SubscriptionManager] تم التهيئة بنجاح")
            
        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في التهيئة: {str(e)}")
            raise e
    
    def get_payment_urls(self):
        """الحصول على URLs الدفع"""
        success_url = f"{self.base_url}/payment/success"
        failure_url = f"{self.base_url}/payment/failure"
        return success_url, failure_url
    
    def create_payment_checkout(self, user_id, plan_id, success_url, failure_url):
        """إنشاء عملية دفع عبر Chargily"""
        
        print(f"🔄 [SubscriptionManager] بدء إنشاء checkout")
        print(f"   - User ID: {user_id}")
        print(f"   - Plan ID: {plan_id}")
        
        try:
            # الحصول على بيانات المستخدم والباقة
            user = User.query.get(user_id)
            plan = SubscriptionPlan.query.get(plan_id)
            
            if not user:
                print(f"❌ [SubscriptionManager] المستخدم غير موجود: {user_id}")
                return None
                
            if not plan:
                print(f"❌ [SubscriptionManager] الباقة غير موجودة: {plan_id}")
                return None
            
            print(f"✅ [SubscriptionManager] المستخدم: {user.username}")
            print(f"✅ [SubscriptionManager] الباقة: {plan.name} - {plan.price} دج")
            
            # إنشاء منتج في Chargily
            print("🔄 [SubscriptionManager] إنشاء منتج...")
            product_name = f"اشتراك {plan.name} - {user.username}"
            
            product_response = self.chargily.create_product(
                name=product_name,
                description=plan.description or f"اشتراك في {plan.name}"
            )
            
            if not product_response or 'id' not in product_response:
                raise Exception("فشل في إنشاء المنتج")
            
            product_id = product_response['id']
            print(f"✅ [SubscriptionManager] تم إنشاء المنتج: {product_id}")
            
            # إنشاء سعر في Chargily
            print("🔄 [SubscriptionManager] إنشاء سعر...")
            
            price_response = self.chargily.create_price(
                amount=int(plan.price),
                currency="dzd",
                product_id=product_id
            )
            
            if not price_response or 'id' not in price_response:
                raise Exception("فشل في إنشاء السعر")
            
            price_id = price_response['id']
            print(f"✅ [SubscriptionManager] تم إنشاء السعر: {price_id}")
            
            # إنشاء checkout
            print("🔄 [SubscriptionManager] إنشاء checkout...")
            
            checkout_response = self.chargily.create_checkout(
                items=[{"price": price_id, "quantity": 1}],
                success_url=success_url,
                failure_url=failure_url,
                metadata={
                    "user_id": str(user_id),
                    "plan_id": str(plan_id),
                    "username": user.username,
                    "plan_name": plan.name
                }
            )
            
            if not checkout_response or 'id' not in checkout_response:
                raise Exception("فشل في إنشاء checkout")
            
            checkout_id = checkout_response['id']
            checkout_url = checkout_response.get('url', checkout_response.get('checkout_url', ''))
            
            print(f"✅ [SubscriptionManager] تم إنشاء checkout: {checkout_id}")
            print(f"🔗 [SubscriptionManager] URL: {checkout_url}")
            
            # حفظ معلومات الدفع في قاعدة البيانات
            payment = Payment(
                user_id=user_id,
                plan_id=plan_id,
                subscription_id=None,
                amount=plan.price,
                currency='DZD',
                status='pending',
                chargily_checkout_id=checkout_id,
                chargily_response=json.dumps(checkout_response)
            )
            
            db.session.add(payment)
            db.session.commit()
            
            print(f"✅ [SubscriptionManager] تم حفظ الدفع: Payment ID {payment.id}")
            
            return {
                'checkout_url': checkout_url,
                'checkout_id': checkout_id,
                'payment_id': payment.id
            }
            
        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في create_payment_checkout: {str(e)}")
            import traceback
            print(f"   - Traceback: {traceback.format_exc()}")
            return None

    def process_payment_webhook(self, webhook_data, request_id=None):
        """معالجة webhook من Chargily مع تفعيل الاشتراك"""

        if not request_id:
            request_id = f"webhook_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        try:
            print(f"[{request_id}] 🔄 بدء معالجة webhook...")
            print(f"[{request_id}] 📥 البيانات الواردة: {json.dumps(webhook_data, indent=2, ensure_ascii=False)}")

            # استخراج البيانات الأساسية
            checkout_id = webhook_data.get('checkout_id')
            status = webhook_data.get('status', '').lower()

            if not checkout_id:
                print(f"[{request_id}] ❌ checkout_id مفقود في البيانات")
                return False, "checkout_id مفقود"

            print(f"[{request_id}] 📋 Checkout ID: {checkout_id}")
            print(f"[{request_id}] 📊 Status: {status}")

            # البحث عن الدفع
            payment = Payment.query.filter_by(chargily_checkout_id=checkout_id).first()

            if not payment:
                print(f"[{request_id}] ❌ لم يتم العثور على دفع بـ checkout_id: {checkout_id}")
                # البحث في جميع المدفوعات للتشخيص
                all_payments = Payment.query.all()
                print(f"[{request_id}] 🔍 إجمالي المدفوعات في قاعدة البيانات: {len(all_payments)}")
                for p in all_payments[-5:]:  # آخر 5 مدفوعات
                    print(f"[{request_id}]    - Payment ID: {p.id}, Checkout: {p.chargily_checkout_id}, Status: {p.status}")
                return False, "الدفع غير موجود"

            print(f"[{request_id}] ✅ تم العثور على الدفع - Payment ID: {payment.id}")
            print(f"[{request_id}] 👤 المستخدم: {payment.user_id}")
            print(f"[{request_id}] 📦 الباقة: {payment.plan_id}")

            # تحديث حالة الدفع
            old_status = payment.status
            payment.status = status
            payment.updated_at = datetime.now()
            payment.webhook_data = json.dumps(webhook_data)

            print(f"[{request_id}] 🔄 تحديث حالة الدفع من '{old_status}' إلى '{status}'")

            # معالجة الدفع الناجح - التفعيل التلقائي
            if status in ['paid', 'completed', 'success']:
                print(f"[{request_id}] 💰 دفع ناجح ({status}) - بدء التفعيل التلقائي...")

                payment.paid_at = datetime.now()

                # الحصول على بيانات المستخدم والباقة
                user = User.query.get(payment.user_id)
                plan = SubscriptionPlan.query.get(payment.plan_id)

                if not user:
                    print(f"[{request_id}] ❌ المستخدم غير موجود: {payment.user_id}")
                    db.session.commit()  # حفظ تحديث الدفع على الأقل
                    return False, "المستخدم غير موجود"

                if not plan:
                    print(f"[{request_id}] ❌ الباقة غير موجودة: {payment.plan_id}")
                    db.session.commit()  # حفظ تحديث الدفع على الأقل
                    return False, "الباقة غير موجودة"

                print(f"[{request_id}] ✅ المستخدم: {user.username} (ID: {user.id})")
                print(f"[{request_id}] ✅ الباقة: {plan.name} - {plan.duration_months} شهر")

                # فحص الاشتراك الحالي
                existing_subscription = Subscription.query.filter_by(
                    user_id=user.id,
                    is_active=True
                ).first()

                if existing_subscription:
                    print(f"[{request_id}] ⚠️ يوجد اشتراك نشط - سيتم تمديده")
                    print(f"[{request_id}] 📅 تاريخ الانتهاء الحالي: {existing_subscription.end_date}")

                    # تمديد الاشتراك الحالي
                    existing_subscription.end_date += timedelta(days=plan.duration_months * 30)
                    end_date = existing_subscription.end_date
                    subscription = existing_subscription

                    print(f"[{request_id}] 📅 تاريخ الانتهاء الجديد: {end_date}")
                else:
                    print(f"[{request_id}] 🆕 إنشاء اشتراك جديد")

                    # إنشاء اشتراك جديد
                    start_date = datetime.now()
                    end_date = start_date + timedelta(days=plan.duration_months * 30)

                    subscription = Subscription(
                        user_id=user.id,
                        plan_id=plan.id,
                        start_date=start_date,
                        end_date=end_date,
                        is_active=True
                    )

                    db.session.add(subscription)
                    print(f"[{request_id}] 📅 من {start_date.date()} إلى {end_date.date()}")

                # تحديث حالة المستخدم
                old_user_status = user.subscription_status

                user.subscription_status = 'active'
                # ملاحظة: subscription_end_date يتم تتبعه في Subscription model وليس User model

                # إضافة الاشتراك إلى session أولاً للحصول على ID
                db.session.add(subscription)
                db.session.flush()  # للحصول على subscription.id

                # ربط الدفع بالاشتراك
                payment.subscription_id = subscription.id

                print(f"[{request_id}] ✅ تحديث المستخدم {user.username}:")
                print(f"[{request_id}]    - الحالة: {old_user_status} → active")
                print(f"[{request_id}]    - تاريخ انتهاء الاشتراك: {end_date}")

                # حفظ التغييرات مع معالجة الأخطاء
                try:
                    db.session.commit()
                    print(f"[{request_id}] ✅ تم حفظ جميع التحديثات بنجاح")

                    # تأكيد التحديث
                    updated_user = User.query.get(user.id)
                    updated_subscription = Subscription.query.get(subscription.id)
                    print(f"[{request_id}] 🔍 تأكيد: المستخدم {updated_user.username}")
                    print(f"[{request_id}]    - subscription_status: {updated_user.subscription_status}")
                    print(f"[{request_id}]    - subscription_end_date: {updated_subscription.end_date}")

                except Exception as commit_error:
                    print(f"[{request_id}] ❌ خطأ في حفظ البيانات: {str(commit_error)}")
                    db.session.rollback()
                    return False, f"خطأ في حفظ البيانات: {str(commit_error)}"

                return True, {
                    'status': 'success',
                    'payment_id': payment.id,
                    'payment_status': payment.status,
                    'subscription_id': subscription.id if hasattr(subscription, 'id') else None,
                    'user_id': user.id,
                    'username': user.username,
                    'plan_name': plan.name,
                    'end_date': end_date.isoformat(),
                    'subscription_status': user.subscription_status,
                    'message': 'تم تفعيل الاشتراك تلقائياً بنجاح'
                }

            else:
                print(f"[{request_id}] ⚠️ حالة الدفع غير ناجحة: {status}")
                db.session.commit()

                return True, {
                    'status': 'processed',
                    'payment_id': payment.id,
                    'payment_status': payment.status,
                    'message': f'تم تحديث حالة الدفع إلى {status}'
                }

        except Exception as e:
            print(f"[{request_id}] ❌ خطأ في معالجة webhook: {str(e)}")
            import traceback
            print(f"[{request_id}] 📋 Traceback: {traceback.format_exc()}")
            db.session.rollback()
            return False, f"خطأ في معالجة webhook: {str(e)}"

    def get_user_subscription(self, user_id):
        """الحصول على اشتراك المستخدم النشط"""
        return Subscription.query.filter_by(
            user_id=user_id,
            is_active=True
        ).first()

    def check_subscription_status(self, user_id):
        """فحص حالة اشتراك المستخدم"""
        subscription = self.get_user_subscription(user_id)

        if not subscription:
            return {
                'status': 'inactive',
                'message': 'لا يوجد اشتراك نشط',
                'days_remaining': 0
            }

        # فحص انتهاء الاشتراك
        now = datetime.now()
        if subscription.end_date < now:
            subscription.is_active = False
            db.session.commit()

            return {
                'status': 'expired',
                'message': 'انتهى الاشتراك',
                'days_remaining': 0,
                'expired_date': subscription.end_date
            }

        # حساب الأيام المتبقية
        days_remaining = (subscription.end_date - now).days

        return {
            'status': 'active',
            'message': 'اشتراك نشط',
            'days_remaining': days_remaining,
            'end_date': subscription.end_date,
            'plan_name': subscription.plan.name if subscription.plan else 'غير محدد'
        }

    def get_available_plans(self, user=None):
        """الحصول على الباقات المتاحة"""
        try:
            plans = SubscriptionPlan.query.filter_by(is_active=True).order_by(SubscriptionPlan.price).all()
            print(f"✅ [SubscriptionManager] تم العثور على {len(plans)} باقة متاحة")
            return plans
        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في get_available_plans: {str(e)}")
            return []

    def create_subscription(self, user_id, plan_id, payment_id=None):
        """إنشاء اشتراك جديد"""
        try:
            user = User.query.get(user_id)
            plan = SubscriptionPlan.query.get(plan_id)

            if not user or not plan:
                return None

            # حساب تواريخ الاشتراك
            start_date = datetime.now()
            end_date = start_date + timedelta(days=plan.duration_months * 30)

            # إنشاء الاشتراك
            subscription = Subscription(
                user_id=user_id,
                plan_id=plan_id,
                start_date=start_date,
                end_date=end_date,
                is_active=True,
                payment_id=payment_id
            )

            db.session.add(subscription)

            # تحديث حالة المستخدم
            user.subscription_status = 'active'
            user.subscription_end_date = end_date

            db.session.commit()

            print(f"✅ [SubscriptionManager] تم إنشاء اشتراك للمستخدم {user.username}")
            return subscription

        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في create_subscription: {str(e)}")
            db.session.rollback()
            return None

    def extend_subscription(self, user_id, days):
        """تمديد اشتراك المستخدم"""
        try:
            subscription = self.get_user_subscription(user_id)

            if not subscription:
                return False

            # تمديد تاريخ الانتهاء
            subscription.end_date += timedelta(days=days)

            # تحديث حالة المستخدم
            user = User.query.get(user_id)
            if user:
                if subscription.end_date > datetime.now():
                    user.subscription_status = 'active'

            db.session.commit()

            print(f"✅ [SubscriptionManager] تم تمديد اشتراك المستخدم {user_id} بـ {days} يوم")
            return True

        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في extend_subscription: {str(e)}")
            db.session.rollback()
            return False

    def cancel_subscription(self, user_id):
        """إلغاء اشتراك المستخدم"""
        try:
            subscription = self.get_user_subscription(user_id)

            if not subscription:
                return False

            # إلغاء الاشتراك
            subscription.is_active = False

            # تحديث حالة المستخدم
            user = User.query.get(user_id)
            if user:
                user.subscription_status = 'cancelled'

            db.session.commit()

            print(f"✅ [SubscriptionManager] تم إلغاء اشتراك المستخدم {user_id}")
            return True

        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في cancel_subscription: {str(e)}")
            db.session.rollback()
            return False

    def get_subscription_stats(self):
        """إحصائيات الاشتراكات"""
        try:
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(is_active=True).count()
            expired_subscriptions = Subscription.query.filter(
                Subscription.end_date < datetime.now(),
                Subscription.is_active == False
            ).count()

            return {
                'total': total_subscriptions,
                'active': active_subscriptions,
                'expired': expired_subscriptions,
                'revenue': self.calculate_total_revenue()
            }

        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في get_subscription_stats: {str(e)}")
            return {'total': 0, 'active': 0, 'expired': 0, 'revenue': 0}

    def calculate_total_revenue(self):
        """حساب إجمالي الإيرادات"""
        try:
            total = db.session.query(db.func.sum(Payment.amount)).filter_by(status='paid').scalar()
            return float(total) if total else 0.0
        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في calculate_total_revenue: {str(e)}")
            return 0.0

    def check_expiring_subscriptions(self, days_ahead=7):
        """فحص الاشتراكات التي تقارب على الانتهاء"""
        try:
            expiring_date = datetime.now() + timedelta(days=days_ahead)
            expiring_subscriptions = Subscription.query.filter(
                Subscription.is_active == True,
                Subscription.end_date <= expiring_date,
                Subscription.end_date > datetime.now()
            ).all()

            print(f"⚠️ [SubscriptionManager] {len(expiring_subscriptions)} اشتراك ينتهي خلال {days_ahead} أيام")
            return expiring_subscriptions

        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في check_expiring_subscriptions: {str(e)}")
            return []

    def send_expiry_notifications(self):
        """إرسال إشعارات انتهاء الاشتراك"""
        try:
            expiring_subscriptions = self.check_expiring_subscriptions()
            notifications_sent = 0

            for subscription in expiring_subscriptions:
                # فحص إذا تم إرسال إشعار مسبقاً
                existing_notification = SubscriptionNotification.query.filter_by(
                    subscription_id=subscription.id,
                    notification_type='expiry_warning'
                ).first()

                if not existing_notification:
                    # إنشاء إشعار جديد
                    notification = SubscriptionNotification(
                        user_id=subscription.user_id,
                        subscription_id=subscription.id,
                        notification_type='expiry_warning',
                        message=f'اشتراكك في {subscription.plan.name} ينتهي في {(subscription.end_date - datetime.now()).days} أيام',
                        is_read=False
                    )

                    db.session.add(notification)
                    notifications_sent += 1

            db.session.commit()
            print(f"✅ [SubscriptionManager] تم إرسال {notifications_sent} إشعار انتهاء")
            return notifications_sent

        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في send_expiry_notifications: {str(e)}")
            db.session.rollback()
            return 0

    def test_webhook_processing(self, checkout_id, status='paid'):
        """اختبار معالجة webhook يدوياً"""
        print(f"🧪 [SubscriptionManager] اختبار معالجة webhook...")
        print(f"   - Checkout ID: {checkout_id}")
        print(f"   - Status: {status}")

        # إنشاء بيانات webhook تجريبية
        test_webhook_data = {
            'checkout_id': checkout_id,
            'status': status,
            'test': True,
            'timestamp': datetime.now().isoformat()
        }

        # معالجة webhook
        success, result = self.process_payment_webhook(test_webhook_data, f"test_{datetime.now().strftime('%H%M%S')}")

        if success:
            print(f"✅ [SubscriptionManager] اختبار webhook نجح")
            print(f"   - النتيجة: {result}")
        else:
            print(f"❌ [SubscriptionManager] اختبار webhook فشل")
            print(f"   - الخطأ: {result}")

        return success, result

    def extend_subscription_days(self, user_id, days_to_add, admin_user_id, reason=None):
        """تمديد اشتراك المستخدم بعدد أيام محدد (للأدمن ومدير المستخدمين)"""
        try:
            print(f"🔄 [SubscriptionManager] بدء تمديد الاشتراك:")
            print(f"   - User ID: {user_id}")
            print(f"   - Days to add: {days_to_add}")
            print(f"   - Admin ID: {admin_user_id}")
            print(f"   - Reason: {reason}")

            # التحقق من صحة البيانات
            if not user_id or not days_to_add or days_to_add <= 0:
                return {
                    'success': False,
                    'error': 'بيانات غير صحيحة'
                }

            # الحصول على المستخدم
            user = User.query.get(user_id)
            if not user:
                return {
                    'success': False,
                    'error': 'المستخدم غير موجود'
                }

            # الحصول على المستخدم الأدمن
            admin_user = User.query.get(admin_user_id)
            if not admin_user:
                return {
                    'success': False,
                    'error': 'المستخدم الأدمن غير موجود'
                }

            print(f"✅ [SubscriptionManager] المستخدم: {user.username}")
            print(f"✅ [SubscriptionManager] الأدمن: {admin_user.username}")

            # البحث عن اشتراك نشط
            current_subscription = Subscription.query.filter_by(
                user_id=user_id,
                is_active=True
            ).first()

            if current_subscription:
                # تمديد الاشتراك الموجود
                old_end_date_original = current_subscription.end_date
                current_subscription.end_date += timedelta(days=days_to_add)
                new_end_date = current_subscription.end_date

                print(f"📅 [SubscriptionManager] تمديد اشتراك موجود:")
                print(f"   - من: {old_end_date_original}")
                print(f"   - إلى: {new_end_date}")

                subscription_id = current_subscription.id

            else:
                # إنشاء اشتراك جديد إذا لم يوجد
                start_date = datetime.now()
                end_date = start_date + timedelta(days=days_to_add)

                # استخدام الباقة الافتراضية (الشهرية)
                default_plan = SubscriptionPlan.query.filter_by(duration_months=1).first()
                if not default_plan:
                    return {
                        'success': False,
                        'error': 'لا توجد باقة افتراضية'
                    }

                new_subscription = Subscription(
                    user_id=user_id,
                    plan_id=default_plan.id,
                    start_date=start_date,
                    end_date=end_date,
                    is_active=True
                )

                db.session.add(new_subscription)
                db.session.flush()  # للحصول على ID

                print(f"🆕 [SubscriptionManager] إنشاء اشتراك جديد:")
                print(f"   - من: {start_date}")
                print(f"   - إلى: {end_date}")

                subscription_id = new_subscription.id
                new_end_date = end_date

            # تحديث حالة المستخدم
            user.subscription_status = 'active'

            # تحديد نوع التمديد
            if current_subscription:
                extension_type = 'paid_subscription'  # تمديد اشتراك مدفوع موجود
                old_end_date = old_end_date_original  # التاريخ الأصلي قبل التمديد
            else:
                extension_type = 'new_subscription'  # إنشاء اشتراك جديد
                old_end_date = None

            # تسجيل العملية في سجل التمديد
            extension_log = SubscriptionExtensionLog(
                user_id=user_id,
                admin_user_id=admin_user_id,
                subscription_id=subscription_id,
                days_added=days_to_add,
                extension_type=extension_type,
                reason=reason or 'تمديد من لوحة التحكم',
                old_end_date=old_end_date,
                new_end_date=new_end_date,
                created_at=datetime.now()
            )

            db.session.add(extension_log)

            # حفظ التغييرات
            db.session.commit()

            print(f"✅ [SubscriptionManager] تم تمديد الاشتراك بنجاح")
            print(f"   - تاريخ الانتهاء الجديد: {new_end_date}")

            return {
                'success': True,
                'message': f'تم إضافة {days_to_add} يوم بنجاح',
                'new_end_date': new_end_date.isoformat(),
                'subscription_id': subscription_id,
                'days_added': days_to_add
            }

        except Exception as e:
            print(f"❌ [SubscriptionManager] خطأ في extend_subscription_days: {str(e)}")
            db.session.rollback()
            return {
                'success': False,
                'error': f'خطأ في تمديد الاشتراك: {str(e)}'
            }

    def initialize_user_free_trial(self, user):
        """تهيئة الفترة التجريبية المجانية للمستخدم الجديد"""
        try:
            print(f"🔄 [SubscriptionManager] تهيئة الفترة التجريبية للمستخدم: {user.username}")

            # التحقق من أن المستخدم يحتاج فترة تجريبية
            if user.role not in [Role.TEACHER, Role.INSPECTOR]:
                print(f"ℹ️ المستخدم {user.username} لا يحتاج فترة تجريبية (دور: {user.role})")
                return True

            # التحقق من عدم وجود فترة تجريبية مسبقاً
            if user.free_trial_end:
                print(f"⚠️ المستخدم {user.username} لديه فترة تجريبية مسبقاً: {user.free_trial_end}")
                return True

            # إعداد الفترة التجريبية (30 يوم)
            trial_duration_days = 30
            trial_end_date = datetime.utcnow() + timedelta(days=trial_duration_days)

            # تحديث المستخدم
            user.free_trial_end = trial_end_date
            user.subscription_status = 'trial'

            # حفظ التغييرات
            db.session.commit()

            print(f"✅ تم تهيئة الفترة التجريبية للمستخدم {user.username}")
            print(f"   - مدة الفترة التجريبية: {trial_duration_days} يوم")
            print(f"   - تاريخ الانتهاء: {trial_end_date}")
            print(f"   - حالة الاشتراك: {user.subscription_status}")

            return True

        except Exception as e:
            print(f"❌ خطأ في تهيئة الفترة التجريبية للمستخدم {user.username}: {str(e)}")
            db.session.rollback()
            return False

# إنشاء instance عالمي
subscription_manager = SubscriptionManager()
