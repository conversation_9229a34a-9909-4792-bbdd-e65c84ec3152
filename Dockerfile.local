# Dockerfile للتطوير المحلي - Ta9affi
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=development \
    DEBIAN_FRONTEND=noninteractive

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    postgresql-client \
    curl \
    wget \
    git \
    libmagic1 \
    libmagic-dev \
    libjpeg-dev \
    libpng-dev \
    libwebp-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libopenjp2-7-dev \
    libtiff5-dev \
    libffi-dev \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملفات المتطلبات
COPY requirements.txt ./

# تثبيت المتطلبات Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# إضافة مكتبات التطوير
RUN pip install --no-cache-dir \
    flask-debugtoolbar \
    pytest \
    pytest-flask \
    pytest-cov \
    black \
    flake8 \
    isort

# نسخ الكود المصدري
COPY . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p uploads logs backups static/optimized

# تعيين الصلاحيات
RUN chmod +x *.sh 2>/dev/null || true && \
    chmod +x *.py

# فحص الصحة
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# تعريض المنفذ
EXPOSE 5000

# الأمر الافتراضي للتطوير
CMD ["python", "app_postgresql.py"]
