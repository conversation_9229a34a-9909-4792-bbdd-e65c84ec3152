# 🚀 نشر Ta9affi مع Dokploy - الدليل السريع

## 📋 ما تحتاجه:
- 🖥️ **سيرفر:** Ubuntu 20.04+ مع 4GB RAM
- 🌐 **نطاق:** مثل ta9affi.com موجه للسيرفر
- ⏱️ **الوقت:** 30 دقيقة

---

## 🔧 الخطوة 1: إعداد السيرفر (10 دقائق)

### اتصل بالسيرفر:
```bash
ssh root@your-server-ip
```

### ثبت Dokploy:
```bash
# تحديث النظام
apt update && apt upgrade -y

# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# تثبيت Dokploy
curl -sSL https://dokploy.com/install.sh | sh

# تشغيل Dokploy
systemctl start dokploy
systemctl enable dokploy
```

### تحقق من التثبيت:
```bash
# يجب أن ترى "active (running)"
systemctl status dokploy

# يجب أن ترى المنفذ 3000
netstat -tlnp | grep :3000
```

---

## 📁 الخطوة 2: رفع ملفات Ta9affi (5 دقائق)

### على جهازك المحلي:
```bash
# ضغط الملفات (من مجلد Ta9affi)
tar -czf ta9affi.tar.gz \
  --exclude='*.log' \
  --exclude='__pycache__' \
  --exclude='*.pyc' \
  .

# رفع للسيرفر
scp ta9affi.tar.gz root@your-server-ip:/opt/
```

### على السيرفر:
```bash
# استخراج الملفات
cd /opt
tar -xzf ta9affi.tar.gz
mv ta9affi ta9affi-app
chown -R dokploy:dokploy ta9affi-app
```

---

## 🌐 الخطوة 3: إعداد Dokploy (10 دقائق)

### 1. افتح Dokploy:
```
https://your-server-ip:3000
```

### 2. إنشاء حساب المدير:
- **اسم المستخدم:** admin
- **كلمة المرور:** Ta9affi2024!
- **البريد:** <EMAIL>

### 3. إنشاء مشروع جديد:
- اضغط **"New Project"**
- **الاسم:** ta9affi
- **الوصف:** نظام إدارة التقدم التعليمي
- اضغط **"Create"**

---

## 🚀 الخطوة 4: إضافة التطبيق (5 دقائق)

### 1. إضافة تطبيق:
- اضغط **"Add Application"**
- **النوع:** Docker Compose
- **الاسم:** ta9affi-app

### 2. إعداد المصدر:
- **النوع:** Local Files
- **المسار:** /opt/ta9affi-app
- **ملف Docker Compose:** docker-compose.prod.yml

### 3. متغيرات البيئة:
اضغط **"Environment"** وأضف:

```env
FLASK_ENV=production
SECRET_KEY=ta9affi-production-secret-key-2024-very-secure
POSTGRES_DB=ta9affi_production
POSTGRES_USER=ta9affi_user
POSTGRES_PASSWORD=ta9affi_secure_password_2024
DATABASE_URL=********************************************************************/ta9affi_production
REDIS_URL=redis://redis:6379/0
CHARGILY_PUBLIC_KEY=live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk
CHARGILY_SECRET_KEY=live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU
CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook
```

---

## 🌐 الخطوة 5: إعداد النطاق والSSL (5 دقائق)

### 1. إضافة النطاق:
- اذهب لقسم **"Domains"**
- اضغط **"Add Domain"**
- **النطاق:** ta9affi.com
- **المنفذ:** 80
- فعل **"Enable SSL"**
- **نوع SSL:** Let's Encrypt

### 2. إعداد DNS:
في إعدادات النطاق عند مزود الخدمة:
```
A Record: @ → your-server-ip
A Record: www → your-server-ip
```

---

## 🗄️ الخطوة 6: إعداد قواعد البيانات (مدمجة)

قواعد البيانات ستعمل تلقائياً مع docker-compose.prod.yml:
- ✅ **PostgreSQL 15** للبيانات الرئيسية
- ✅ **Redis 7** للتخزين المؤقت

---

## 🚀 الخطوة 7: النشر النهائي (5 دقائق)

### 1. بناء التطبيق:
- اضغط **"Build"**
- انتظر اكتمال البناء (2-3 دقائق)

### 2. النشر:
- اضغط **"Deploy"**
- انتظر اكتمال النشر (1-2 دقيقة)

### 3. التحقق:
```bash
# فحص الحاويات
docker ps

# يجب أن ترى:
# ta9affi-app
# ta9affi-postgres
# ta9affi-redis
# ta9affi-nginx
```

---

## ✅ اختبار النشر

### 1. اختبار الموقع:
```bash
# اختبار الصحة
curl https://ta9affi.com/health

# يجب أن ترى: {"status": "healthy"}
```

### 2. اختبار الصفحات:
- **الصفحة الرئيسية:** https://ta9affi.com
- **تسجيل الدخول:** https://ta9affi.com/login
- **التسجيل:** https://ta9affi.com/register

### 3. اختبار قاعدة البيانات:
```bash
# الاتصال بـ PostgreSQL
docker exec -it ta9affi-postgres psql -U ta9affi_user -d ta9affi_production

# عرض الجداول
\dt

# يجب أن ترى جداول Ta9affi
```

---

## 🔍 المراقبة والصيانة

### 1. مراقبة الأداء:
- استخدم لوحة Dokploy للمراقبة
- تحقق من استخدام الموارد
- راقب السجلات

### 2. النسخ الاحتياطية:
```bash
# نسخ احتياطي يومي لقاعدة البيانات
docker exec ta9affi-postgres pg_dump -U ta9affi_user ta9affi_production > /backup/ta9affi-$(date +%Y%m%d).sql
```

### 3. التحديثات:
- ارفع الملفات الجديدة لـ /opt/ta9affi-app
- اضغط **"Redeploy"** في Dokploy

---

## 🛠️ استكشاف الأخطاء السريع

### مشكلة: التطبيق لا يعمل
```bash
# فحص السجلات
docker logs ta9affi-app

# إعادة تشغيل
docker restart ta9affi-app
```

### مشكلة: قاعدة البيانات لا تعمل
```bash
# فحص PostgreSQL
docker logs ta9affi-postgres

# إعادة تشغيل
docker restart ta9affi-postgres
```

### مشكلة: SSL لا يعمل
- تأكد من توجيه DNS بشكل صحيح
- انتظر 5-10 دقائق لانتشار DNS
- أعد إنشاء شهادة SSL في Dokploy

---

## 🎉 تهانينا!

**Ta9affi الآن يعمل على: https://ta9affi.com**

### الميزات المفعلة:
- ✅ **HTTPS** آمن مع Let's Encrypt
- ✅ **PostgreSQL** عالي الأداء
- ✅ **Redis** للتخزين المؤقت
- ✅ **Rate Limiting** ذكي
- ✅ **Auto-scaling** مع Docker
- ✅ **Monitoring** مع Dokploy

### الخطوات التالية:
1. 📊 **اختبر جميع الوظائف**
2. 👥 **أضف المستخدمين الأوائل**
3. 💳 **اختبر نظام الدفع**
4. 📈 **راقب الأداء**
5. 🔄 **أعد النسخ الاحتياطية**

**Ta9affi جاهز لخدمة آلاف المستخدمين! 🚀**
