#!/usr/bin/env python3
"""
سكريبت تشغيل Ta9affi في البيئة المحلية مع إعدادات صحيحة
"""

import os
import sys
from dotenv import load_dotenv

def setup_local_environment():
    """إعداد البيئة المحلية"""
    
    print("🔧 إعداد البيئة المحلية...")
    
    # تحميل متغيرات البيئة من ملف .env.development
    if os.path.exists('.env.development'):
        load_dotenv('.env.development')
        print("✅ تم تحميل إعدادات التطوير من .env.development")
    else:
        print("⚠️ ملف .env.development غير موجود، سيتم استخدام الإعدادات الافتراضية")
    
    # تعيين متغيرات البيئة المطلوبة
    os.environ['FLASK_ENV'] = 'development'
    os.environ['DEBUG'] = 'True'
    
    # التأكد من إعدادات URLs المحلية
    if not os.environ.get('BASE_URL'):
        os.environ['BASE_URL'] = 'http://127.0.0.1:5000'
    
    if not os.environ.get('CHARGILY_WEBHOOK_URL'):
        os.environ['CHARGILY_WEBHOOK_URL'] = 'http://127.0.0.1:5000/chargily-webhook'
    
    print(f"🌍 البيئة: {os.environ.get('FLASK_ENV')}")
    print(f"🔗 Base URL: {os.environ.get('BASE_URL')}")
    print(f"🔗 Webhook URL: {os.environ.get('CHARGILY_WEBHOOK_URL')}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل Ta9affi في البيئة المحلية")
    print("=" * 50)
    
    # إعداد البيئة
    setup_local_environment()
    
    # عرض معلومات التشغيل
    port = os.environ.get('PORT', '5000')
    host = '127.0.0.1'
    
    print(f"📍 الخادم: {host}")
    print(f"🔌 المنفذ: {port}")
    print(f"🌐 الرابط: http://{host}:{port}")
    print(f"💳 صفحة الاشتراكات: http://{host}:{port}/subscription/plans")
    print(f"❤️ Health Check: http://{host}:{port}/health")
    print("=" * 50)
    
    # استيراد وتشغيل التطبيق
    try:
        from app import app
        print("✅ تم تحميل التطبيق بنجاح")
        
        # تشغيل التطبيق
        app.run(
            host=host,
            port=int(port),
            debug=True,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
