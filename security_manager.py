#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأمان لتطبيق Ta9affi
يوفر حماية شاملة ضد الهجمات الشائعة
"""

import os
import re
import hashlib
import secrets
import time
import logging
from datetime import datetime, timedelta
from functools import wraps
from flask import request, session, current_app, abort, jsonify, g
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman
from werkzeug.security import check_password_hash, generate_password_hash
from redis_manager import redis_manager
import ipaddress

class SecurityManager:
    """مدير الأمان الرئيسي"""
    
    def __init__(self, app=None):
        self.app = app
        self.limiter = None
        self.talisman = None
        self.blocked_ips = set()
        self.suspicious_patterns = []
        self.failed_attempts = {}
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير الأمان"""
        self.app = app
        
        # إعداد Rate Limiting
        self.setup_rate_limiting()
        
        # إعداد حماية Headers
        self.setup_security_headers()
        
        # إعداد أنماط الهجمات المشبوهة
        self.setup_attack_patterns()
        
        # تسجيل معالجات الأحداث
        app.before_request(self.before_request_security)
        app.after_request(self.after_request_security)
        
        logging.info("✅ تم تهيئة مدير الأمان")
    
    def setup_rate_limiting(self):
        """إعداد تحديد معدل الطلبات"""
        self.limiter = Limiter(
            app=self.app,
            key_func=get_remote_address,
            storage_uri=self.app.config.get('RATELIMIT_STORAGE_URL', 'memory://'),
            default_limits=["1000 per hour", "100 per minute"]
        )
        
        # حدود خاصة للمسارات الحساسة
        @self.limiter.limit("5 per minute")
        def login_limit():
            pass
        
        @self.limiter.limit("3 per minute")
        def password_reset_limit():
            pass
        
        @self.limiter.limit("10 per minute")
        def api_limit():
            pass
    
    def setup_security_headers(self):
        """إعداد حماية Headers الأمنية"""
        csp = {
            'default-src': "'self'",
            'script-src': [
                "'self'",
                "'unsafe-inline'",
                "'unsafe-eval'",
                "https://cdn.jsdelivr.net",
                "https://cdnjs.cloudflare.com",
                "https://code.jquery.com"
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'",
                "https://cdn.jsdelivr.net",
                "https://cdnjs.cloudflare.com",
                "https://fonts.googleapis.com"
            ],
            'img-src': [
                "'self'",
                "data:",
                "https:",
                "http:"
            ],
            'font-src': [
                "'self'",
                "https://cdn.jsdelivr.net",
                "https://cdnjs.cloudflare.com",
                "https://fonts.gstatic.com"
            ],
            'connect-src': "'self'",
            'frame-ancestors': "'none'",
            'base-uri': "'self'",
            'form-action': "'self'"
        }
        
        self.talisman = Talisman(
            self.app,
            force_https=False,  # سيتم تفعيله في الإنتاج
            strict_transport_security=True,
            strict_transport_security_max_age=31536000,
            content_security_policy=csp,
            content_security_policy_nonce_in=['script-src', 'style-src'],
            feature_policy={
                'geolocation': "'none'",
                'camera': "'none'",
                'microphone': "'none'",
                'payment': "'none'"
            }
        )
    
    def setup_attack_patterns(self):
        """إعداد أنماط الهجمات المشبوهة"""
        self.suspicious_patterns = [
            # SQL Injection patterns
            re.compile(r"(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)", re.IGNORECASE),
            re.compile(r"(\b(or|and)\s+\d+\s*=\s*\d+)", re.IGNORECASE),
            re.compile(r"('|\"|;|--|\*|\/\*|\*\/)", re.IGNORECASE),
            
            # XSS patterns
            re.compile(r"<script[^>]*>.*?</script>", re.IGNORECASE | re.DOTALL),
            re.compile(r"javascript:", re.IGNORECASE),
            re.compile(r"on\w+\s*=", re.IGNORECASE),
            re.compile(r"<iframe[^>]*>", re.IGNORECASE),
            
            # Path traversal
            re.compile(r"\.\./", re.IGNORECASE),
            re.compile(r"\.\.\\", re.IGNORECASE),
            
            # Command injection
            re.compile(r"(\||&|;|\$\(|\`)", re.IGNORECASE),
            re.compile(r"\b(cat|ls|pwd|whoami|id|uname|wget|curl)\b", re.IGNORECASE),
            
            # File inclusion
            re.compile(r"\b(file://|ftp://|php://|data://)\b", re.IGNORECASE),
        ]
    
    def before_request_security(self):
        """فحص الأمان قبل معالجة الطلب"""
        # فحص IP المحظور
        if self.is_ip_blocked(request.remote_addr):
            abort(403)
        
        # فحص أنماط الهجمات
        if self.detect_attack_patterns():
            self.log_security_event("ATTACK_PATTERN_DETECTED", request.remote_addr)
            self.increment_failed_attempts(request.remote_addr)
            abort(400)
        
        # فحص حجم الطلب
        if request.content_length and request.content_length > 50 * 1024 * 1024:  # 50MB
            abort(413)
        
        # فحص User-Agent المشبوه
        if self.is_suspicious_user_agent():
            self.log_security_event("SUSPICIOUS_USER_AGENT", request.remote_addr)
            self.increment_failed_attempts(request.remote_addr)
        
        # إضافة معرف الطلب للأمان
        g.security_request_id = secrets.token_hex(16)
    
    def after_request_security(self, response):
        """معالجة الأمان بعد الطلب"""
        # إضافة headers أمنية إضافية
        response.headers['X-Request-ID'] = getattr(g, 'security_request_id', 'unknown')
        response.headers['X-Powered-By'] = 'Ta9affi Security System'
        
        # إزالة headers حساسة
        response.headers.pop('Server', None)
        
        return response
    
    def detect_attack_patterns(self):
        """كشف أنماط الهجمات في الطلب"""
        # فحص URL
        if self.check_patterns(request.url):
            return True
        
        # فحص المعاملات
        for key, value in request.args.items():
            if self.check_patterns(f"{key}={value}"):
                return True
        
        # فحص البيانات المرسلة
        if request.is_json:
            try:
                data = request.get_json()
                if data and self.check_patterns(str(data)):
                    return True
            except:
                pass
        
        # فحص Form data
        if request.form:
            for key, value in request.form.items():
                if self.check_patterns(f"{key}={value}"):
                    return True
        
        return False
    
    def check_patterns(self, text):
        """فحص النص ضد أنماط الهجمات"""
        if not text:
            return False
        
        for pattern in self.suspicious_patterns:
            if pattern.search(text):
                return True
        
        return False
    
    def is_suspicious_user_agent(self):
        """فحص User-Agent المشبوه"""
        user_agent = request.headers.get('User-Agent', '').lower()
        
        suspicious_agents = [
            'sqlmap', 'nikto', 'nmap', 'masscan', 'zap', 'burp',
            'python-requests', 'curl', 'wget', 'scanner', 'bot',
            'crawler', 'spider', 'scraper'
        ]
        
        for agent in suspicious_agents:
            if agent in user_agent:
                return True
        
        # فحص User-Agent فارغ أو قصير جداً
        if len(user_agent) < 10:
            return True
        
        return False
    
    def is_ip_blocked(self, ip):
        """فحص ما إذا كان IP محظور"""
        if ip in self.blocked_ips:
            return True
        
        # فحص في Redis
        if redis_manager.is_available():
            try:
                blocked = redis_manager.cache_get(f"blocked_ip:{ip}")
                return blocked is not None
            except:
                pass
        
        return False
    
    def block_ip(self, ip, duration=3600, reason="Security violation"):
        """حظر IP لفترة محددة"""
        self.blocked_ips.add(ip)
        
        # حفظ في Redis
        if redis_manager.is_available():
            try:
                redis_manager.cache_set(f"blocked_ip:{ip}", {
                    'reason': reason,
                    'blocked_at': datetime.utcnow().isoformat(),
                    'duration': duration
                }, duration)
            except:
                pass
        
        self.log_security_event("IP_BLOCKED", ip, {'reason': reason, 'duration': duration})
    
    def increment_failed_attempts(self, ip):
        """زيادة عدد المحاولات الفاشلة"""
        current_time = time.time()
        
        if ip not in self.failed_attempts:
            self.failed_attempts[ip] = []
        
        # إضافة المحاولة الحالية
        self.failed_attempts[ip].append(current_time)
        
        # إزالة المحاولات القديمة (أكثر من ساعة)
        self.failed_attempts[ip] = [
            attempt for attempt in self.failed_attempts[ip]
            if current_time - attempt < 3600
        ]
        
        # حظر IP إذا تجاوز الحد المسموح
        if len(self.failed_attempts[ip]) >= 10:  # 10 محاولات في الساعة
            self.block_ip(ip, duration=7200, reason="Too many failed attempts")
    
    def log_security_event(self, event_type, ip, details=None):
        """تسجيل حدث أمني"""
        security_logger = logging.getLogger('security')
        
        event_data = {
            'type': event_type,
            'ip': ip,
            'timestamp': datetime.utcnow().isoformat(),
            'user_agent': request.headers.get('User-Agent', ''),
            'url': request.url,
            'method': request.method,
            'details': details or {}
        }
        
        security_logger.warning(f"SECURITY_EVENT: {event_data}")
        
        # حفظ في Redis للتحليل
        if redis_manager.is_available():
            try:
                event_key = f"security_event:{int(time.time())}"
                redis_manager.redis_client.setex(event_key, 86400, str(event_data))
            except:
                pass
    
    def validate_csrf_token(self, token):
        """التحقق من رمز CSRF"""
        if not token:
            return False
        
        # الحصول على الرمز المحفوظ في الجلسة
        session_token = session.get('csrf_token')
        if not session_token:
            return False
        
        # مقارنة آمنة للرموز
        return secrets.compare_digest(token, session_token)
    
    def generate_csrf_token(self):
        """إنشاء رمز CSRF جديد"""
        token = secrets.token_hex(32)
        session['csrf_token'] = token
        return token
    
    def sanitize_input(self, text, max_length=1000):
        """تنظيف المدخلات من المحتوى الضار"""
        if not text:
            return text
        
        # تحديد الطول
        text = text[:max_length]
        
        # إزالة HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # إزالة JavaScript
        text = re.sub(r'javascript:', '', text, flags=re.IGNORECASE)
        
        # إزالة SQL keywords خطيرة
        dangerous_sql = ['drop', 'delete', 'truncate', 'alter', 'create']
        for keyword in dangerous_sql:
            text = re.sub(rf'\b{keyword}\b', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def hash_password_secure(self, password):
        """تشفير كلمة المرور بطريقة آمنة"""
        # إضافة salt عشوائي
        salt = secrets.token_hex(32)
        
        # استخدام PBKDF2 مع SHA-256
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 100,000 iterations
        )
        
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password_secure(self, password, stored_hash):
        """التحقق من كلمة المرور"""
        try:
            salt, password_hash = stored_hash.split(':')
            
            # إعادة حساب hash
            computed_hash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt.encode('utf-8'),
                100000
            )
            
            return secrets.compare_digest(password_hash, computed_hash.hex())
        except:
            return False
    
    def get_security_report(self, hours=24):
        """الحصول على تقرير الأمان"""
        if not redis_manager.is_available():
            return {}
        
        try:
            # البحث عن الأحداث الأمنية
            end_time = int(time.time())
            start_time = end_time - (hours * 3600)
            
            events = []
            for timestamp in range(start_time, end_time, 300):  # كل 5 دقائق
                key_pattern = f"security_event:{timestamp}"
                keys = redis_manager.redis_client.keys(key_pattern)
                
                for key in keys:
                    try:
                        event_data = redis_manager.redis_client.get(key)
                        if event_data:
                            events.append(eval(event_data))  # تحويل string إلى dict
                    except:
                        continue
            
            # تحليل الأحداث
            report = {
                'total_events': len(events),
                'blocked_ips': len(self.blocked_ips),
                'attack_attempts': len([e for e in events if e.get('type') == 'ATTACK_PATTERN_DETECTED']),
                'suspicious_agents': len([e for e in events if e.get('type') == 'SUSPICIOUS_USER_AGENT']),
                'events_by_type': {},
                'top_attacking_ips': {}
            }
            
            # تجميع الأحداث حسب النوع
            for event in events:
                event_type = event.get('type', 'unknown')
                report['events_by_type'][event_type] = report['events_by_type'].get(event_type, 0) + 1
                
                # تجميع IPs المهاجمة
                ip = event.get('ip', 'unknown')
                report['top_attacking_ips'][ip] = report['top_attacking_ips'].get(ip, 0) + 1
            
            return report
            
        except Exception as e:
            logging.error(f"Error generating security report: {str(e)}")
            return {}

# إنشاء مثيل عام لمدير الأمان
security_manager = SecurityManager()

# ديكوريتر للحماية من CSRF
def csrf_protect(f):
    """ديكوريتر للحماية من CSRF"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == "POST":
            token = request.form.get('csrf_token') or request.headers.get('X-CSRF-Token')
            if not security_manager.validate_csrf_token(token):
                abort(403)
        return f(*args, **kwargs)
    return decorated_function

# ديكوريتر لتنظيف المدخلات
def sanitize_inputs(f):
    """ديكوريتر لتنظيف المدخلات"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # تنظيف form data
        if request.form:
            cleaned_form = {}
            for key, value in request.form.items():
                cleaned_form[key] = security_manager.sanitize_input(value)
            request.form = cleaned_form
        
        return f(*args, **kwargs)
    return decorated_function

# إعداد logger للأمان
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('logs/security.log')
security_handler.setFormatter(logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s'
))
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.WARNING)
