# Dockerfile مع دعم SSL
FROM python:3.11-slim

# تثبيت dependencies النظام
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    openssl \
    && rm -rf /var/lib/apt/lists/*

# إعداد مجلد العمل
WORKDIR /app

# نسخ requirements
COPY requirements.txt .

# تثبيت Python packages
RUN pip install --no-cache-dir -r requirements.txt

# تثبيت PyOpenSSL
RUN pip install --no-cache-dir pyopenssl cryptography

# نسخ ملفات التطبيق
COPY . .

# إنشاء مجلد للشهادات
RUN mkdir -p /app/ssl_certs

# إعداد متغيرات البيئة
ENV FLASK_APP=app.py
ENV FLASK_ENV=production
ENV PYTHONPATH=/app

# فتح البورتات
EXPOSE 80 443 5000

# إنشاء script للتشغيل
RUN echo '#!/bin/bash\n\
if [ "$SSL_MODE" = "true" ]; then\n\
    echo "🔐 تشغيل مع SSL..."\n\
    python app_with_ssl.py\n\
elif [ "$SSL_MODE" = "dual" ]; then\n\
    echo "🔐 تشغيل في الوضع المزدوج..."\n\
    python app_with_ssl.py dual\n\
else\n\
    echo "🌐 تشغيل عادي..."\n\
    python app.py\n\
fi' > /app/start.sh && chmod +x /app/start.sh

# الأمر الافتراضي
CMD ["/app/start.sh"]
