"""
نماذج قاعدة البيانات الجديدة
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, timedelta

# تهيئة قاعدة البيانات
db = SQLAlchemy()

# أدوار المستخدمين
class Role:
    ADMIN = 'admin'
    INSPECTOR = 'inspector'
    TEACHER = 'teacher'
    USER_MANAGER = 'user_manager'  # دور جديد لإدارة المستخدمين بصلاحيات محدودة

# نموذج إعدادات الأدوار المتاحة للتسجيل
class RoleSettings(db.Model):
    """نموذج لإدارة الأدوار المتاحة للتسجيل"""
    id = db.Column(db.Integer, primary_key=True)
    role_name = db.Column(db.String(20), unique=True, nullable=False)  # اسم الدور
    is_enabled = db.Column(db.<PERSON>an, default=True)  # هل الدور متاح للتسجيل
    display_name = db.Column(db.String(50), nullable=False)  # الاسم المعروض
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# قائمة الولايات الجزائرية (التقسيم الإداري الجديد - 58 ولاية)
ALGERIAN_WILAYAS = [
    ('01', 'أدرار'),
    ('02', 'الشلف'),
    ('03', 'الأغواط'),
    ('04', 'أم البواقي'),
    ('05', 'باتنة'),
    ('06', 'بجاية'),
    ('07', 'بسكرة'),
    ('08', 'بشار'),
    ('09', 'البليدة'),
    ('10', 'البويرة'),
    ('11', 'تمنراست'),
    ('12', 'تبسة'),
    ('13', 'تلمسان'),
    ('14', 'تيارت'),
    ('15', 'تيزي وزو'),
    ('16', 'الجزائر'),
    ('17', 'الجلفة'),
    ('18', 'جيجل'),
    ('19', 'سطيف'),
    ('20', 'سعيدة'),
    ('21', 'سكيكدة'),
    ('22', 'سيدي بلعباس'),
    ('23', 'عنابة'),
    ('24', 'قالمة'),
    ('25', 'قسنطينة'),
    ('26', 'المدية'),
    ('27', 'مستغانم'),
    ('28', 'المسيلة'),
    ('29', 'معسكر'),
    ('30', 'ورقلة'),
    ('31', 'وهران'),
    ('32', 'البيض'),
    ('33', 'إليزي'),
    ('34', 'برج بوعريريج'),
    ('35', 'بومرداس'),
    ('36', 'الطارف'),
    ('37', 'تندوف'),
    ('38', 'تيسمسيلت'),
    ('39', 'الوادي'),
    ('40', 'خنشلة'),
    ('41', 'سوق أهراس'),
    ('42', 'تيبازة'),
    ('43', 'ميلة'),
    ('44', 'عين الدفلى'),
    ('45', 'النعامة'),
    ('46', 'عين تموشنت'),
    ('47', 'غرداية'),
    ('48', 'غليزان'),
    ('49', 'تيميمون'),
    ('50', 'برج باجي مختار'),
    ('51', 'أولاد جلال'),
    ('52', 'بني عباس'),
    ('53', 'عين صالح'),
    ('54', 'عين قزام'),
    ('55', 'تقرت'),
    ('56', 'جانت'),
    ('57', 'المغير'),
    ('58', 'المنيعة')
]

# نموذج المستخدم
class User(db.Model, UserMixin):
    """نموذج المستخدم مع دعم تسجيل الدخول"""

    @property
    def is_authenticated(self):
        return True

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value):
        self._is_active = value
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    role = db.Column(db.String(20), nullable=False)
    _is_active = db.Column('is_active', db.Boolean, default=True)  # لتفعيل أو تعطيل الحساب

    # الحقول الجديدة
    phone_number = db.Column(db.String(15), nullable=False)  # رقم الهاتف (إجباري)
    wilaya_code = db.Column(db.String(2), nullable=True)  # رمز الولاية (اختياري)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)  # آخر تسجيل دخول

    # حقول الاشتراك
    free_trial_end = db.Column(db.DateTime, nullable=True)  # تاريخ انتهاء الفترة التجريبية المجانية
    subscription_status = db.Column(db.String(20), default='trial')  # trial, active, expired, suspended

    # العلاقات
    schedules = db.relationship('Schedule', backref='user', lazy=True)
    progress_entries = db.relationship('ProgressEntry', backref='user', lazy=True)

    @property
    def wilaya_name(self):
        """الحصول على اسم الولاية من الرمز"""
        if not self.wilaya_code:
            return None
        wilaya_dict = dict(ALGERIAN_WILAYAS)
        return wilaya_dict.get(self.wilaya_code, 'غير محدد')

    @property
    def formatted_phone(self):
        """تنسيق رقم الهاتف للعرض"""
        if not self.phone_number:
            return None
        # تنسيق الرقم الجزائري (مثال: 0555123456 -> 0555 12 34 56)
        phone = self.phone_number.replace(' ', '').replace('-', '')
        if len(phone) == 10 and phone.startswith('0'):
            return f"{phone[:4]} {phone[4:6]} {phone[6:8]} {phone[8:]}"
        return phone

    @property
    def masked_phone(self):
        """إخفاء جزئي لرقم الهاتف للحماية"""
        if not self.phone_number:
            return None
        phone = self.phone_number.replace(' ', '').replace('-', '')
        if len(phone) == 10 and phone.startswith('0'):
            # إظهار أول 4 أرقام وآخر رقمين فقط (مثال: 0555****56)
            return f"{phone[:4]}****{phone[-2:]}"
        return phone[:3] + '*' * (len(phone) - 5) + phone[-2:] if len(phone) > 5 else phone

    @property
    def masked_email(self):
        """إخفاء جزئي للبريد الإلكتروني للحماية"""
        if not self.email:
            return None
        if '@' in self.email:
            local, domain = self.email.split('@', 1)
            if len(local) <= 2:
                masked_local = local
            elif len(local) <= 4:
                masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
            else:
                masked_local = local[:2] + '*' * (len(local) - 4) + local[-2:]
            return f"{masked_local}@{domain}"
        return self.email

    @property
    def masked_username(self):
        """إخفاء جزئي لاسم المستخدم للحماية"""
        if not self.username:
            return None
        if len(self.username) <= 3:
            return self.username
        elif len(self.username) <= 6:
            return self.username[:2] + '*' * (len(self.username) - 3) + self.username[-1:]
        else:
            return self.username[:3] + '*' * (len(self.username) - 6) + self.username[-3:]

    @property
    def is_abandoned(self):
        """التحقق من كون الحساب مهجور (لم يسجل دخول لأكثر من 6 أشهر)"""
        if not self.last_login:
            # إذا لم يسجل دخول أبداً، نعتبر تاريخ الإنشاء
            return (datetime.utcnow() - self.created_at).days > 180
        return (datetime.utcnow() - self.last_login).days > 180

    @property
    def is_online(self):
        """التحقق من كون المستخدم متصل حالياً"""
        active_session = UserSession.query.filter_by(
            user_id=self.id,
            is_active=True
        ).filter(
            UserSession.last_activity > datetime.utcnow() - timedelta(minutes=5)
        ).first()
        return active_session is not None

    @property
    def days_since_last_login(self):
        """عدد الأيام منذ آخر تسجيل دخول"""
        if not self.last_login:
            return (datetime.utcnow() - self.created_at).days
        return (datetime.utcnow() - self.last_login).days

    @classmethod
    def get_abandoned_accounts(cls, days=180):
        """الحصول على الحسابات المهجورة"""
        from datetime import timedelta
        cutoff_date = datetime.utcnow() - timedelta(days=days)

        # الحسابات التي لم تسجل دخول أبداً ومضى على إنشائها أكثر من المدة المحددة
        never_logged_in = cls.query.filter(
            cls.last_login.is_(None),
            cls.created_at < cutoff_date,
            cls.role != Role.ADMIN  # استثناء حسابات الأدمن
        )

        # الحسابات التي سجلت دخول لكن آخر دخول كان قبل المدة المحددة
        inactive_accounts = cls.query.filter(
            cls.last_login < cutoff_date,
            cls.role != Role.ADMIN  # استثناء حسابات الأدمن
        )

        # دمج النتائج
        return never_logged_in.union(inactive_accounts).all()

    # خصائص الاشتراك
    @property
    def current_subscription(self):
        """الحصول على الاشتراك الحالي النشط"""
        return Subscription.query.filter_by(
            user_id=self.id,
            is_active=True
        ).filter(
            Subscription.end_date > datetime.utcnow()
        ).first()

    @property
    def has_active_subscription(self):
        """التحقق من وجود اشتراك نشط"""
        if self.role not in [Role.TEACHER, Role.INSPECTOR]:
            return True  # الأدمن ومدير المستخدمين لا يحتاجون اشتراك

        # التحقق من الفترة التجريبية المجانية
        if self.free_trial_end and datetime.utcnow() <= self.free_trial_end:
            return True

        # التحقق من الاشتراك المدفوع
        return self.current_subscription is not None

    @property
    def subscription_days_remaining(self):
        """عدد الأيام المتبقية في الاشتراك"""
        if self.role not in [Role.TEACHER, Role.INSPECTOR]:
            return float('inf')  # لا حدود للأدمن ومدير المستخدمين

        # التحقق من الاشتراك المدفوع أولاً (أولوية عالية)
        current_sub = self.current_subscription
        if current_sub:
            return current_sub.days_remaining

        # إذا لم يوجد اشتراك مدفوع، فحص الفترة التجريبية
        if self.free_trial_end and datetime.utcnow() <= self.free_trial_end:
            return (self.free_trial_end - datetime.utcnow()).days

        return 0

    @property
    def is_subscription_expiring_soon(self):
        """التحقق من قرب انتهاء الاشتراك (أقل من 7 أيام)"""
        days_remaining = self.subscription_days_remaining
        return 0 < days_remaining <= 7

    @property
    def subscription_type(self):
        """نوع الاشتراك الحالي - أولوية للاشتراك المدفوع"""
        if self.role not in [Role.TEACHER, Role.INSPECTOR]:
            return 'unlimited'

        # فحص الاشتراك المدفوع أولاً (أولوية عالية)
        current_sub = self.current_subscription
        if current_sub:
            return 'paid'

        # إذا لم يوجد اشتراك مدفوع، فحص الفترة التجريبية
        if self.free_trial_end and datetime.utcnow() <= self.free_trial_end:
            return 'free_trial'

        return 'expired'

    def can_access_premium_features(self):
        """التحقق من إمكانية الوصول للميزات المدفوعة"""
        return self.has_active_subscription

    def initialize_free_trial(self):
        """تفعيل الفترة التجريبية المجانية للمستخدمين الجدد"""
        if self.role in [Role.TEACHER, Role.INSPECTOR] and not self.free_trial_end:
            from datetime import timedelta
            self.free_trial_end = datetime.utcnow() + timedelta(days=30)  # شهر مجاني
            self.subscription_status = 'trial'

    # للمفتشين: الأساتذة الذين يشرفون عليهم
    supervised_teachers = db.relationship('User',
                                         secondary='inspector_teacher',
                                         primaryjoin="and_(User.id==inspector_teacher.c.inspector_id, User.role=='inspector')",
                                         secondaryjoin="and_(User.id==inspector_teacher.c.teacher_id, User.role=='teacher')",
                                         backref=db.backref('inspectors', lazy='dynamic'),
                                         lazy='dynamic')

# علاقة المفتش-الأستاذ
inspector_teacher = db.Table('inspector_teacher',
    db.Column('inspector_id', db.Integer, db.ForeignKey('user.id'), primary_key=True),
    db.Column('teacher_id', db.Integer, db.ForeignKey('user.id'), primary_key=True)
)

# نموذج المستوى التعليمي
class EducationalLevel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=True)  # لتفعيل أو تعطيل المستوى
    database_prefix = db.Column(db.String(20), nullable=True)  # بادئة لقاعدة البيانات الخاصة بالمستوى

    # العلاقات
    subjects = db.relationship('Subject', backref='educational_level', lazy=True)
    databases = db.relationship('LevelDatabase', backref='level', lazy=True, cascade='all, delete-orphan')

# نموذج المادة الدراسية
class Subject(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)

    # العلاقات
    domains = db.relationship('Domain', backref='subject', lazy=True)

# نموذج الميدان/النشاط
class Domain(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)

    # العلاقات
    knowledge_materials = db.relationship('KnowledgeMaterial', backref='domain', lazy=True)

# نموذج الموارد المعرفية
class KnowledgeMaterial(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    domain_id = db.Column(db.Integer, db.ForeignKey('domain.id'), nullable=False)

    # العلاقات
    competencies = db.relationship('Competency', backref='knowledge_material', lazy=True)

# نموذج الكفاءة المستهدفة
class Competency(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.Text, nullable=False)
    knowledge_material_id = db.Column(db.Integer, db.ForeignKey('knowledge_material.id'), nullable=False)

    # العلاقات
    progress_entries = db.relationship('ProgressEntry', backref='competency', lazy=True)

# نموذج جدول الأستاذ
class Schedule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)  # 0=الاثنين، 6=الأحد
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)

    # العلاقات
    subject = db.relationship('Subject')
    level = db.relationship('EducationalLevel')

# نموذج تسجيل التقدم
class ProgressEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    competency_id = db.Column(db.Integer, db.ForeignKey('competency.id'), nullable=True)

    # إضافة العلاقات مع المستوى والمادة والميدان/النشاط والموارد المعرفية
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'))
    subject_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))
    domain_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))
    material_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'))

    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False)  # 'completed', 'in_progress', 'planned'
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات مع الجداول الأخرى
    level = db.relationship('EducationalLevel', foreign_keys=[level_id], backref='progress_entries')
    subject = db.relationship('LevelDataEntry', foreign_keys=[subject_id], backref='subject_progress_entries')
    domain = db.relationship('LevelDataEntry', foreign_keys=[domain_id], backref='domain_progress_entries')
    material = db.relationship('LevelDataEntry', foreign_keys=[material_id], backref='material_progress_entries')

# نموذج قاعدة البيانات الخاصة بكل مستوى
class LevelDatabase(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)  # اسم قاعدة البيانات
    file_path = db.Column(db.String(255), nullable=False)  # مسار ملف قاعدة البيانات
    is_active = db.Column(db.Boolean, default=True)  # حالة قاعدة البيانات
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات مع الجداول الأخرى
    data_entries = db.relationship('LevelDataEntry', backref='database', lazy=True, cascade='all, delete-orphan')

# نموذج بيانات كل مستوى
class LevelDataEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    database_id = db.Column(db.Integer, db.ForeignKey('level_database.id'), nullable=False, index=True)
    entry_type = db.Column(db.String(50), nullable=False, index=True)  # نوع البيانات (مادة، ميدان/نشاط، موارد معرفية، كفاءة)
    parent_id = db.Column(db.Integer, nullable=True, index=True)  # معرف العنصر الأب (للتسلسل الهرمي)
    name = db.Column(db.String(255), nullable=False)  # اسم العنصر
    description = db.Column(db.Text, nullable=True)  # وصف العنصر
    order_num = db.Column(db.Integer, default=0)  # ترتيب العنصر
    is_active = db.Column(db.Boolean, default=True, index=True)  # حالة العنصر
    extra_data = db.Column(db.Text, nullable=True)  # بيانات إضافية (JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # إضافة فهرس مركب لتحسين الأداء
    __table_args__ = (
        db.Index('idx_database_type_active', 'database_id', 'entry_type', 'is_active'),
        db.Index('idx_database_parent', 'database_id', 'parent_id'),
    )


# نموذج الإشعارات بين الإدارة والمفتشين
class AdminInspectorNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_admin_inspector_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_admin_inspector_notifications')


# نموذج الإشعارات بين المفتشين والأساتذة
class InspectorTeacherNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_inspector_teacher_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_inspector_teacher_notifications')

# نموذج الإشعارات العامة (للجميع أو لمجموعة محددة)
class GeneralNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    target_type = db.Column(db.String(20), nullable=False)  # 'all', 'role', 'specific'
    target_role = db.Column(db.String(20), nullable=True)  # للإرسال لدور معين
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_general_notifications')

# نموذج قراءة الإشعارات العامة
class GeneralNotificationRead(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    notification_id = db.Column(db.Integer, db.ForeignKey('general_notification.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    read_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    notification = db.relationship('GeneralNotification', backref='reads')
    user = db.relationship('User', backref='general_notification_reads')

# نموذج باقات الاشتراك
class SubscriptionPlan(db.Model):
    """نموذج باقات الاشتراك"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # اسم الباقة
    description = db.Column(db.Text, nullable=True)  # وصف الباقة
    price = db.Column(db.Float, nullable=False)  # السعر بالدينار الجزائري
    duration_months = db.Column(db.Integer, nullable=False)  # مدة الاشتراك بالأشهر
    is_active = db.Column(db.Boolean, default=True)  # هل الباقة متاحة
    is_institutional = db.Column(db.Boolean, default=False)  # باقة مؤسسية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    subscriptions = db.relationship('Subscription', backref='plan', lazy=True)

# نموذج الاشتراكات
class Subscription(db.Model):
    """نموذج اشتراكات المستخدمين"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    plan_id = db.Column(db.Integer, db.ForeignKey('subscription_plan.id'), nullable=False)
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    is_free_trial = db.Column(db.Boolean, default=False)  # اشتراك تجريبي مجاني
    auto_renew = db.Column(db.Boolean, default=False)  # تجديد تلقائي
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref='subscriptions')
    payments = db.relationship('Payment', backref='subscription', lazy=True)

    @property
    def is_expired(self):
        """التحقق من انتهاء الاشتراك"""
        return datetime.utcnow() > self.end_date

    @property
    def days_remaining(self):
        """عدد الأيام المتبقية في الاشتراك"""
        if self.is_expired:
            return 0
        return (self.end_date - datetime.utcnow()).days

    @property
    def is_expiring_soon(self):
        """التحقق من قرب انتهاء الاشتراك (أقل من 7 أيام)"""
        return self.days_remaining <= 7 and self.days_remaining > 0

# نموذج المدفوعات
class Payment(db.Model):
    """نموذج مدفوعات الاشتراكات"""
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    plan_id = db.Column(db.Integer, db.ForeignKey('subscription_plan.id'), nullable=True)  # خطة الاشتراك
    amount = db.Column(db.Float, nullable=False)  # المبلغ
    currency = db.Column(db.String(3), default='DZD')  # العملة
    status = db.Column(db.String(20), default='pending')  # حالة الدفع: pending, completed, failed, cancelled
    payment_method = db.Column(db.String(50), nullable=True)  # طريقة الدفع

    # معلومات Chargily
    chargily_checkout_id = db.Column(db.String(100), nullable=True)  # معرف الدفع في Chargily
    chargily_payment_id = db.Column(db.String(100), nullable=True)  # معرف المعاملة في Chargily
    chargily_response = db.Column(db.Text, nullable=True)  # استجابة Chargily كاملة (JSON)
    webhook_data = db.Column(db.Text, nullable=True)  # بيانات webhook من Chargily (JSON)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    paid_at = db.Column(db.DateTime, nullable=True)  # تاريخ إتمام الدفع

    # العلاقات
    user = db.relationship('User', backref='payments')

# نموذج إشعارات الاشتراك
class SubscriptionNotification(db.Model):
    """نموذج إشعارات انتهاء الاشتراك"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    notification_type = db.Column(db.String(20), nullable=False)  # expiring_soon, expired, renewed
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref='subscription_notifications')
    subscription = db.relationship('Subscription', backref='notifications')

# نموذج سجل تمديد الاشتراكات
class SubscriptionExtensionLog(db.Model):
    """نموذج تسجيل عمليات تمديد الاشتراكات من قبل الإدارة"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # المستخدم المستفيد
    admin_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # الأدمن أو مدير المستخدمين
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)  # الاشتراك إذا كان موجود

    # تفاصيل العملية
    days_added = db.Column(db.Integer, nullable=False)  # عدد الأيام المضافة
    extension_type = db.Column(db.String(50), nullable=False)  # نوع التمديد: paid_subscription, free_trial, reactivated_trial, new_trial
    reason = db.Column(db.Text, nullable=True)  # سبب الإضافة

    # التواريخ
    old_end_date = db.Column(db.DateTime, nullable=True)  # التاريخ القديم
    new_end_date = db.Column(db.DateTime, nullable=False)  # التاريخ الجديد
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', foreign_keys=[user_id], backref='subscription_extensions_received')
    admin_user = db.relationship('User', foreign_keys=[admin_user_id], backref='subscription_extensions_performed')
    subscription = db.relationship('Subscription', backref='extension_logs')

# نموذج جلسات المستخدمين لتتبع المتصلين حالياً
class UserSession(db.Model):
    """نموذج تتبع جلسات المستخدمين النشطة"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    session_id = db.Column(db.String(255), nullable=False, unique=True)  # معرف الجلسة
    ip_address = db.Column(db.String(45), nullable=True)  # عنوان IP
    user_agent = db.Column(db.Text, nullable=True)  # معلومات المتصفح
    login_time = db.Column(db.DateTime, default=datetime.utcnow)  # وقت تسجيل الدخول
    last_activity = db.Column(db.DateTime, default=datetime.utcnow)  # آخر نشاط
    is_active = db.Column(db.Boolean, default=True)  # حالة الجلسة
    logout_time = db.Column(db.DateTime, nullable=True)  # وقت تسجيل الخروج

    # العلاقات
    user = db.relationship('User', backref='sessions')

    @classmethod
    def get_online_users_count(cls):
        """الحصول على عدد المستخدمين المتصلين حالياً"""
        # المستخدمون الذين لديهم جلسة نشطة وآخر نشاط خلال آخر 5 دقائق
        cutoff_time = datetime.utcnow() - timedelta(minutes=5)
        return cls.query.filter(
            cls.is_active == True,
            cls.last_activity > cutoff_time
        ).count()

    @classmethod
    def cleanup_old_sessions(cls):
        """تنظيف الجلسات القديمة (أكثر من 24 ساعة)"""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        old_sessions = cls.query.filter(
            cls.last_activity < cutoff_time
        ).all()

        for session in old_sessions:
            session.is_active = False
            session.logout_time = datetime.utcnow()

        db.session.commit()
        return len(old_sessions)

    @classmethod
    def get_online_users(cls):
        """الحصول على قائمة المستخدمين المتصلين حالياً"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=5)
        return cls.query.filter(
            cls.is_active == True,
            cls.last_activity > cutoff_time
        ).join(User).all()

    @classmethod
    def cleanup_old_sessions(cls):
        """تنظيف الجلسات القديمة (أكثر من 24 ساعة)"""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        old_sessions = cls.query.filter(
            cls.last_activity < cutoff_time
        ).all()

        for session in old_sessions:
            session.is_active = False
            session.logout_time = datetime.utcnow()

        db.session.commit()
        return len(old_sessions)

# نموذج الأخبار والتحديثات
class NewsUpdate(db.Model):
    """نموذج الأخبار والتحديثات للشريط الإخباري"""
    __tablename__ = 'news_updates'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)  # عنوان الخبر
    content = db.Column(db.Text, nullable=False)  # محتوى الخبر
    is_active = db.Column(db.Boolean, default=True)  # نشط أم لا
    priority = db.Column(db.Integer, default=1)  # الأولوية (1-10)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    creator = db.relationship('User', backref='created_news')

    def __repr__(self):
        return f'<NewsUpdate {self.title[:30]}...>'

    @classmethod
    def get_active_news(cls, limit=10):
        """الحصول على الأخبار النشطة مرتبة حسب الأولوية والتاريخ"""
        return cls.query.filter_by(is_active=True)\
                       .order_by(cls.priority.desc(), cls.created_at.desc())\
                       .limit(limit).all()
