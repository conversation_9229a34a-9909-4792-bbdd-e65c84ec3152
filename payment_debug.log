
🔄 بدء عملية الدفع - User: splintergangs (ID: 22), Plan: 2
🔄 [SubscriptionManager] بدء create_payment_checkout
   - User ID: 22
   - Plan ID: 2
   - Success URL: https://example.com/payment/success
   - Failure URL: https://example.com/payment/failure
✅ تم إنشاء checkout بنجاح!
   - Checkout URL: http://pay.chargily.dz/checkouts/01k2azrqkf09f1k7b49t1qszss/pay
   - Checkout ID: 01k2azrqkf09f1k7b49t1qszss
   - Payment ID: 42

🔄 بدء عملية الدفع - User: splintergangs (ID: 22), Plan: 2
🔄 [SubscriptionManager] بدء create_payment_checkout
   - User ID: 22
   - Plan ID: 2
   - Success URL: https://example.com/payment/success
   - Failure URL: https://example.com/payment/failure
✅ تم إنشاء checkout بنجاح!
   - Checkout URL: http://pay.chargily.dz/checkouts/01k2b0csrcree5y7gj9asdhcwa/pay
   - Checkout ID: 01k2b0csrcree5y7gj9asdhcwa
   - Payment ID: 43

🔄 بدء عملية الدفع - User: splintergangs (ID: 22), Plan: 2
🔄 [SubscriptionManager] بدء create_payment_checkout
   - User ID: 22
   - Plan ID: 2
   - Success URL: https://example.com/payment/success
   - Failure URL: https://example.com/payment/failure
✅ تم إنشاء checkout بنجاح!
   - Checkout URL: http://pay.chargily.dz/checkouts/01k2fpkqnqefcmypjm8d21mcr8/pay
   - Checkout ID: 01k2fpkqnqefcmypjm8d21mcr8
   - Payment ID: 44

🔄 بدء عملية الدفع - User: splintergangs (ID: 22), Plan: 2
🔄 [SubscriptionManager] بدء create_payment_checkout
   - User ID: 22
   - Plan ID: 2
   - Success URL: http://ta9affi.com/payment/success
   - Failure URL: http://ta9affi.com/payment/failure
✅ تم إنشاء checkout بنجاح!
   - Checkout URL: http://pay.chargily.dz/checkouts/01k2sermp1k32ya1w092qwytsc/pay
   - Checkout ID: 01k2sermp1k32ya1w092qwytsc
   - Payment ID: 45

🔄 بدء عملية الدفع - User: splintergangs (ID: 22), Plan: 2
🔄 [SubscriptionManager] بدء create_payment_checkout
   - User ID: 22
   - Plan ID: 2
   - Success URL: http://127.0.0.1:5000/payment/success
   - Failure URL: http://127.0.0.1:5000/payment/failure
❌ [SubscriptionManager] خطأ في create_payment_checkout:
   - Error: 'id'
   - Type: <class 'KeyError'>
   - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ta9affi1.16.1\subscription_manager.py", line 569, in create_payment_checkout
    print(f"✅ [SubscriptionManager] تم إنشاء checkout: {checkout_response['id']}")
                                                         ~~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'id'

❌ فشل في إنشاء checkout - checkout_data is None

🔄 بدء عملية الدفع - User: splintergangs (ID: 22), Plan: 3
🔄 [SubscriptionManager] بدء create_payment_checkout
   - User ID: 22
   - Plan ID: 3
   - Success URL: http://127.0.0.1:5000/payment/success
   - Failure URL: http://127.0.0.1:5000/payment/failure
❌ [SubscriptionManager] خطأ في create_payment_checkout:
   - Error: 'id'
   - Type: <class 'KeyError'>
   - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ta9affi1.16.1\subscription_manager.py", line 569, in create_payment_checkout
    print(f"✅ [SubscriptionManager] تم إنشاء checkout: {checkout_response['id']}")
                                                         ~~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'id'

❌ فشل في إنشاء checkout - checkout_data is None

🔄 بدء عملية الدفع - User: tahar (ID: 11), Plan: 1
✅ تم إنشاء checkout بنجاح!
   - Checkout URL: http://pay.chargily.dz/checkouts/01k37e971ec7vhkaswt4nyh6p2/pay
   - Checkout ID: 01k37e971ec7vhkaswt4nyh6p2
   - Payment ID: 46

🔄 بدء عملية الدفع - User: tahar (ID: 11), Plan: 3
✅ تم إنشاء checkout بنجاح!
   - Checkout URL: http://pay.chargily.dz/checkouts/01k3hm0zwewhw3gxea28fjs27k/pay
   - Checkout ID: 01k3hm0zwewhw3gxea28fjs27k
   - Payment ID: 46

🔄 بدء عملية الدفع - User: teacher (ID: 3), Plan: 3
✅ تم إنشاء checkout بنجاح!
   - Checkout URL: http://pay.chargily.dz/checkouts/01k650q2pkk68qtkr6nwjczb8y/pay
   - Checkout ID: 01k650q2pkk68qtkr6nwjczb8y
   - Payment ID: 47
