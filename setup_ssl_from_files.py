#!/usr/bin/env python3
"""
إعداد SSL من الملفات المحملة من cheapsslweb.com
"""

import os
import shutil
import ssl
from datetime import datetime

def setup_ssl_files():
    """إعداد ملفات SSL من المجلد المحمل"""
    
    print("🔐 إعداد SSL من الملفات المحملة")
    print("=" * 50)
    
    # إنشاء مجلد SSL منظم
    ssl_dir = "ssl_ready"
    os.makedirs(ssl_dir, exist_ok=True)
    
    # 1. نسخ المفتاح الخاص (من ssl_setup_purchased.py)
    private_key_content = """*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    
    # حفظ المفتاح الخاص
    key_path = os.path.join(ssl_dir, "ta9affi.key")
    with open(key_path, "w") as f:
        f.write(private_key_content)
    os.chmod(key_path, 0o600)
    print(f"✅ تم حفظ المفتاح الخاص: {key_path}")
    
    # 2. نسخ الشهادة الأساسية
    cert_source = "SSL_Cert/Plain Text Files/ta9affi_com.txt"
    cert_path = os.path.join(ssl_dir, "ta9affi.crt")
    
    if os.path.exists(cert_source):
        shutil.copy2(cert_source, cert_path)
        print(f"✅ تم نسخ الشهادة الأساسية: {cert_path}")
    else:
        print(f"❌ الشهادة الأساسية غير موجودة: {cert_source}")
        return False
    
    # 3. نسخ الشهادة الوسطية
    intermediate_source = "SSL_Cert/Plain Text Files/CerteraDVSSLCA.txt"
    intermediate_path = os.path.join(ssl_dir, "intermediate.crt")
    
    if os.path.exists(intermediate_source):
        shutil.copy2(intermediate_source, intermediate_path)
        print(f"✅ تم نسخ الشهادة الوسطية: {intermediate_path}")
    else:
        print(f"❌ الشهادة الوسطية غير موجودة: {intermediate_source}")
        return False
    
    # 4. إنشاء fullchain certificate
    fullchain_path = os.path.join(ssl_dir, "fullchain.pem")
    
    with open(fullchain_path, "w") as fullchain:
        # الشهادة الأساسية أولاً
        with open(cert_path, "r") as cert:
            fullchain.write(cert.read())
        
        # الشهادة الوسطية
        with open(intermediate_path, "r") as intermediate:
            fullchain.write("\n" + intermediate.read())
    
    print(f"✅ تم إنشاء fullchain: {fullchain_path}")
    
    # 5. اختبار الإعداد
    try:
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(fullchain_path, key_path)
        print("✅ اختبار SSL نجح!")
        
        # عرض معلومات الشهادة
        try:
            from OpenSSL import crypto
            
            with open(cert_path, "r") as f:
                cert_data = f.read()
            
            cert = crypto.load_certificate(crypto.FILETYPE_PEM, cert_data)
            
            print(f"\n📜 معلومات الشهادة:")
            print(f"   - الموضوع: {cert.get_subject().CN}")
            print(f"   - المُصدر: {cert.get_issuer().CN}")
            print(f"   - صالحة من: {cert.get_notBefore().decode()}")
            print(f"   - صالحة حتى: {cert.get_notAfter().decode()}")
            
        except ImportError:
            print("⚠️ PyOpenSSL غير مثبت - لا يمكن عرض تفاصيل الشهادة")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار SSL: {e}")
        return False

def create_env_file():
    """إنشاء ملف متغيرات البيئة"""
    
    env_content = f"""# SSL Configuration for Ta9affi
SSL_CERT_PATH=ssl_ready/fullchain.pem
SSL_KEY_PATH=ssl_ready/ta9affi.key
SSL_MODE=true

# Base URLs
BASE_URL=https://ta9affi.com
CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook

# Flask Configuration
FLASK_ENV=production
PRODUCTION_MODE=true
"""
    
    with open(".env.ssl", "w") as f:
        f.write(env_content)
    
    print("✅ تم إنشاء ملف .env.ssl")

def create_run_script():
    """إنشاء سكريبت تشغيل مع SSL"""
    
    # للـ Windows
    windows_script = """@echo off
echo 🔐 تشغيل Ta9affi مع SSL
echo ========================

REM تعيين متغيرات البيئة
set SSL_CERT_PATH=ssl_ready/fullchain.pem
set SSL_KEY_PATH=ssl_ready/ta9affi.key
set SSL_MODE=true
set BASE_URL=https://ta9affi.com
set CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook

echo ✅ تم تعيين متغيرات البيئة
echo 🚀 تشغيل التطبيق...

python app_with_ssl.py

pause
"""
    
    with open("run_with_ssl.bat", "w", encoding='utf-8') as f:
        f.write(windows_script)
    
    # للـ Linux/Mac
    linux_script = """#!/bin/bash
echo "🔐 تشغيل Ta9affi مع SSL"
echo "========================"

# تعيين متغيرات البيئة
export SSL_CERT_PATH=ssl_ready/fullchain.pem
export SSL_KEY_PATH=ssl_ready/ta9affi.key
export SSL_MODE=true
export BASE_URL=https://ta9affi.com
export CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook

echo "✅ تم تعيين متغيرات البيئة"
echo "🚀 تشغيل التطبيق..."

python app_with_ssl.py
"""
    
    with open("run_with_ssl.sh", "w", encoding='utf-8') as f:
        f.write(linux_script)
    
    # تعيين صلاحيات التنفيذ
    try:
        os.chmod("run_with_ssl.sh", 0o755)
    except:
        pass
    
    print("✅ تم إنشاء سكريبتات التشغيل:")
    print("   - run_with_ssl.bat (Windows)")
    print("   - run_with_ssl.sh (Linux/Mac)")

def main():
    """الدالة الرئيسية"""
    
    print("🔐 إعداد SSL شامل لـ Ta9affi")
    print("=" * 40)
    
    # إعداد ملفات SSL
    if setup_ssl_files():
        print("\n✅ تم إعداد ملفات SSL بنجاح!")
        
        # إنشاء ملفات مساعدة
        create_env_file()
        create_run_script()
        
        print("\n📋 الملفات الجاهزة:")
        print("   - ssl_ready/ta9affi.key (المفتاح الخاص)")
        print("   - ssl_ready/ta9affi.crt (الشهادة الأساسية)")
        print("   - ssl_ready/intermediate.crt (الشهادة الوسطية)")
        print("   - ssl_ready/fullchain.pem (الشهادة الكاملة)")
        print("   - .env.ssl (متغيرات البيئة)")
        print("   - run_with_ssl.bat/sh (سكريبتات التشغيل)")
        
        print("\n🚀 للتشغيل:")
        print("   Windows: run_with_ssl.bat")
        print("   Linux/Mac: ./run_with_ssl.sh")
        print("   أو يدوياً: python app_with_ssl.py")
        
        print("\n🌐 الموقع سيكون متاح على:")
        print("   https://ta9affi.com")
        print("   https://www.ta9affi.com")
        
    else:
        print("\n❌ فشل في إعداد SSL")

if __name__ == '__main__':
    main()
