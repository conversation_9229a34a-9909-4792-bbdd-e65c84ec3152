-----------------------------
***Where's My Private Key?*** 
-----------------------------

Your private key will be required to complete your installation. Your private key, as the name suggests, is private to the system that originally created your CSR and will not be provided to you by our support agents or the Certificate Authority. 

You should never share your private key with anyone, and we will never ask you for your private key...EVER! 

----------------------------
How to Find Your Private Key
----------------------------

IF you created your CSR on your server, then your private key is already on your machine and you should not need to import it. 

IF you created your CSR using an online tool, then you should have saved your private key on your local machine and can go back and retrieve it to complete your installation. 

----------------------------------
If You Can't Find Your Private Key
----------------------------------

Private keys are only created when a new CSR is generated. The CSR and the Private Key are a pair. 

If you are not able to find your original Private Key, then you will need to create a new CSR and re-issue (or re-key) your SSL certificate. 

***Please Note*** To successfully re-issue your SSL, some validation steps may need to be completed again. 