#!/usr/bin/env python3
"""
إعداد بيئة اختبار Chargily
"""

import os

def setup_test_environment():
    """إعداد متغيرات البيئة للاختبار"""
    
    # إعدادات أساسية
    os.environ['FLASK_ENV'] = 'development'
    os.environ['PRODUCTION_MODE'] = 'false'
    os.environ['DEBUG'] = 'true'
    
    # إعدادات Chargily للاختبار
    os.environ['CHARGILY_PUBLIC_KEY'] = 'test_pk_your_test_public_key_here'
    os.environ['CHARGILY_SECRET_KEY'] = 'test_sk_your_test_secret_key_here'
    
    # إعدادات URLs
    os.environ['BASE_URL'] = 'http://127.0.0.1:5000'
    os.environ['CHARGILY_WEBHOOK_URL'] = 'http://127.0.0.1:5000/chargily-webhook'
    
    print("✅ تم إعداد بيئة الاختبار")
    print(f"🔗 Base URL: {os.environ['BASE_URL']}")
    print(f"🔗 Webhook URL: {os.environ['CHARGILY_WEBHOOK_URL']}")

def setup_production_environment():
    """إعداد متغيرات البيئة للإنتاج"""
    
    # إعدادات أساسية
    os.environ['FLASK_ENV'] = 'production'
    os.environ['PRODUCTION_MODE'] = 'true'
    os.environ['DEBUG'] = 'false'
    
    # إعدادات Chargily للإنتاج
    os.environ['CHARGILY_PUBLIC_KEY'] = 'live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk'
    os.environ['CHARGILY_SECRET_KEY'] = 'live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU'
    
    # إعدادات URLs
    os.environ['BASE_URL'] = 'https://ta9affi.com'
    os.environ['CHARGILY_WEBHOOK_URL'] = 'https://ta9affi.com/chargily-webhook'
    
    print("✅ تم إعداد بيئة الإنتاج")
    print(f"🔗 Base URL: {os.environ['BASE_URL']}")
    print(f"🔗 Webhook URL: {os.environ['CHARGILY_WEBHOOK_URL']}")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'production':
        setup_production_environment()
    else:
        setup_test_environment()
