#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة الجلسات - Ta9affi
"""

import os
import requests
import time

def test_login_session():
    """اختبار تسجيل الدخول والجلسة"""
    
    print("🧪 اختبار تسجيل الدخول والجلسة...")
    print("=" * 50)
    
    base_url = "http://ta9affi.com"
    
    # إنشاء جلسة للاحتفاظ بـ cookies
    session = requests.Session()
    
    try:
        # 1. الحصول على صفحة تسجيل الدخول
        print("🔄 الحصول على صفحة تسجيل الدخول...")
        login_page = session.get(f"{base_url}/login")
        
        if login_page.status_code == 200:
            print("✅ تم الوصول لصفحة تسجيل الدخول")
        else:
            print(f"❌ فشل في الوصول لصفحة تسجيل الدخول: {login_page.status_code}")
            return False
        
        # 2. محاولة تسجيل الدخول
        print("🔄 محاولة تسجيل الدخول...")
        
        login_data = {
            'username': 'saku17',  # المستخدم من الـ logs
            'password': 'admin123'  # كلمة مرور افتراضية
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        print(f"📊 استجابة تسجيل الدخول: {login_response.status_code}")
        print(f"🔗 Headers: {dict(login_response.headers)}")
        
        if login_response.status_code == 302:
            redirect_url = login_response.headers.get('Location', '')
            print(f"🔄 إعادة توجيه إلى: {redirect_url}")
            
            if '/dashboard' in redirect_url:
                print("✅ تسجيل دخول ناجح - توجيه إلى dashboard")
            elif '/login' in redirect_url:
                print("❌ فشل تسجيل الدخول - إعادة توجيه إلى login")
                return False
        
        # 3. اختبار الوصول للوحة التحكم
        print("🔄 اختبار الوصول للوحة التحكم...")
        
        dashboard_response = session.get(f"{base_url}/dashboard", allow_redirects=False)
        
        print(f"📊 استجابة لوحة التحكم: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("✅ تم الوصول للوحة التحكم بنجاح!")
            return True
        elif dashboard_response.status_code == 302:
            redirect_url = dashboard_response.headers.get('Location', '')
            print(f"❌ إعادة توجيه من لوحة التحكم إلى: {redirect_url}")
            
            if '/login' in redirect_url:
                print("❌ مشكلة في الجلسة - المستخدم لم يعد مسجل دخوله")
                return False
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_different_credentials():
    """اختبار بيانات دخول مختلفة"""
    
    print("\n🔄 اختبار بيانات دخول مختلفة...")
    
    credentials = [
        ('admin', 'admin123'),
        ('teacher', 'teacher123'),
        ('saku17', 'admin123'),
        ('saku17', 'password'),
        ('saku17', '123456')
    ]
    
    base_url = "http://ta9affi.com"
    
    for username, password in credentials:
        print(f"\n🧪 اختبار: {username} / {password}")
        
        session = requests.Session()
        
        try:
            login_data = {
                'username': username,
                'password': password
            }
            
            response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
            
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                if '/dashboard' in redirect_url:
                    print(f"✅ نجح تسجيل الدخول: {username}")
                    
                    # اختبار الجلسة
                    dashboard_response = session.get(f"{base_url}/dashboard", allow_redirects=False)
                    if dashboard_response.status_code == 200:
                        print(f"✅ الجلسة تعمل بشكل صحيح")
                        return username, password
                    else:
                        print(f"❌ مشكلة في الجلسة")
                else:
                    print(f"❌ فشل تسجيل الدخول")
            else:
                print(f"❌ استجابة غير متوقعة: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ: {str(e)}")
    
    return None, None

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار إصلاح مشكلة الجلسات - Ta9affi")
    print("=" * 60)
    
    # اختبار تسجيل الدخول الأساسي
    if test_login_session():
        print("\n🎉 الجلسة تعمل بشكل صحيح!")
    else:
        print("\n❌ مشكلة في الجلسة - اختبار بيانات مختلفة...")
        
        # اختبار بيانات دخول مختلفة
        working_username, working_password = test_different_credentials()
        
        if working_username:
            print(f"\n✅ بيانات دخول صحيحة: {working_username} / {working_password}")
        else:
            print(f"\n❌ لم يتم العثور على بيانات دخول صحيحة")
    
    print(f"\n📋 ملاحظات:")
    print(f"1. تأكد من إعادة النشر في dokploy")
    print(f"2. راجع logs الخادم للمزيد من التفاصيل")
    print(f"3. جرب تشغيل fix_login_quick.py إذا لزم الأمر")

if __name__ == '__main__':
    main()
