#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إدارة خدمة Ta9affi
توفر أوامر لإدارة التطبيق في بيئة الإنتاج
"""

import os
import sys
import signal
import subprocess
import time
import argparse
import json
from pathlib import Path

class Ta9affiService:
    """مدير خدمة Ta9affi"""
    
    def __init__(self, app_dir=None):
        self.app_dir = Path(app_dir) if app_dir else Path.cwd()
        self.venv_dir = self.app_dir / "venv"
        self.logs_dir = self.app_dir / "logs"
        self.pid_file = self.logs_dir / "gunicorn.pid"
        self.config_file = self.app_dir / "gunicorn_config.py"
        
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        self.logs_dir.mkdir(exist_ok=True)
    
    def log(self, message, level="INFO"):
        """طباعة رسالة مع الوقت"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        colors = {
            "INFO": "\033[0;34m",
            "SUCCESS": "\033[0;32m", 
            "WARNING": "\033[1;33m",
            "ERROR": "\033[0;31m"
        }
        color = colors.get(level, "\033[0m")
        reset = "\033[0m"
        print(f"{color}[{timestamp}] [{level}]{reset} {message}")
    
    def run_command(self, command, cwd=None, env=None):
        """تشغيل أمر shell"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd or self.app_dir,
                env=env,
                capture_output=True,
                text=True
            )
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def is_running(self):
        """التحقق من تشغيل Gunicorn"""
        if not self.pid_file.exists():
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # التحقق من وجود العملية
            os.kill(pid, 0)
            return True
        except (OSError, ValueError):
            # حذف ملف PID إذا كانت العملية غير موجودة
            if self.pid_file.exists():
                self.pid_file.unlink()
            return False
    
    def start(self):
        """بدء التطبيق"""
        if self.is_running():
            self.log("التطبيق يعمل بالفعل", "WARNING")
            return True
        
        self.log("بدء تشغيل Ta9affi...")
        
        # التحقق من وجود البيئة الافتراضية
        if not self.venv_dir.exists():
            self.log("البيئة الافتراضية غير موجودة", "ERROR")
            return False
        
        # التحقق من وجود ملف الإعداد
        if not self.config_file.exists():
            self.log("ملف إعداد Gunicorn غير موجود", "ERROR")
            return False
        
        # إعداد متغيرات البيئة
        env = os.environ.copy()
        env.update({
            'FLASK_ENV': 'production',
            'PYTHONPATH': str(self.app_dir)
        })
        
        # أمر بدء Gunicorn
        activate_script = self.venv_dir / "bin" / "activate"
        command = f"""
        source {activate_script} && \
        cd {self.app_dir} && \
        gunicorn --config {self.config_file} app_postgresql:app --daemon
        """
        
        success, stdout, stderr = self.run_command(command, env=env)
        
        if success:
            # انتظار قليل للتأكد من بدء التشغيل
            time.sleep(3)
            
            if self.is_running():
                with open(self.pid_file, 'r') as f:
                    pid = f.read().strip()
                self.log(f"تم بدء التطبيق بنجاح (PID: {pid})", "SUCCESS")
                return True
            else:
                self.log("فشل في بدء التطبيق", "ERROR")
                if stderr:
                    self.log(f"خطأ: {stderr}", "ERROR")
                return False
        else:
            self.log(f"فشل في تشغيل الأمر: {stderr}", "ERROR")
            return False
    
    def stop(self):
        """إيقاف التطبيق"""
        if not self.is_running():
            self.log("التطبيق متوقف بالفعل", "WARNING")
            return True
        
        self.log("إيقاف التطبيق...")
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # إرسال إشارة TERM
            os.kill(pid, signal.SIGTERM)
            
            # انتظار الإغلاق الطبيعي
            for _ in range(10):
                if not self.is_running():
                    self.log("تم إيقاف التطبيق بنجاح", "SUCCESS")
                    return True
                time.sleep(1)
            
            # إذا لم يتوقف، استخدم KILL
            self.log("إجبار إيقاف التطبيق...", "WARNING")
            os.kill(pid, signal.SIGKILL)
            time.sleep(2)
            
            if not self.is_running():
                self.log("تم إجبار إيقاف التطبيق", "SUCCESS")
                return True
            else:
                self.log("فشل في إيقاف التطبيق", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"خطأ في إيقاف التطبيق: {str(e)}", "ERROR")
            return False
    
    def restart(self):
        """إعادة تشغيل التطبيق"""
        self.log("إعادة تشغيل التطبيق...")
        
        if self.is_running():
            if not self.stop():
                return False
        
        return self.start()
    
    def reload(self):
        """إعادة تحميل التطبيق (بدون إيقاف)"""
        if not self.is_running():
            self.log("التطبيق غير مُشغل", "ERROR")
            return False
        
        self.log("إعادة تحميل التطبيق...")
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # إرسال إشارة HUP لإعادة التحميل
            os.kill(pid, signal.SIGHUP)
            
            time.sleep(2)
            
            if self.is_running():
                self.log("تم إعادة تحميل التطبيق بنجاح", "SUCCESS")
                return True
            else:
                self.log("فشل في إعادة التحميل", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"خطأ في إعادة التحميل: {str(e)}", "ERROR")
            return False
    
    def status(self):
        """عرض حالة التطبيق"""
        self.log("حالة التطبيق:")
        print("=" * 50)
        
        # حالة Gunicorn
        if self.is_running():
            with open(self.pid_file, 'r') as f:
                pid = f.read().strip()
            print(f"✅ Gunicorn: يعمل (PID: {pid})")
            
            # معلومات إضافية عن العملية
            try:
                success, stdout, stderr = self.run_command(f"ps -p {pid} -o pid,ppid,cmd,etime,pcpu,pmem")
                if success and stdout:
                    lines = stdout.strip().split('\n')
                    if len(lines) > 1:
                        print(f"   {lines[1]}")
            except:
                pass
        else:
            print("❌ Gunicorn: متوقف")
        
        # حالة Nginx
        success, stdout, stderr = self.run_command("systemctl is-active nginx")
        if success and stdout.strip() == "active":
            print("✅ Nginx: يعمل")
        else:
            print("❌ Nginx: متوقف أو غير مثبت")
        
        # حالة PostgreSQL
        success, stdout, stderr = self.run_command("systemctl is-active postgresql")
        if success and stdout.strip() == "active":
            print("✅ PostgreSQL: يعمل")
        else:
            print("⚠️ PostgreSQL: غير معروف")
        
        # حالة Redis
        success, stdout, stderr = self.run_command("redis-cli ping")
        if success and "PONG" in stdout:
            print("✅ Redis: يعمل")
        else:
            print("⚠️ Redis: غير متصل")
        
        print("=" * 50)
    
    def logs(self, lines=50, follow=False):
        """عرض سجلات التطبيق"""
        log_files = [
            self.logs_dir / "gunicorn_error.log",
            self.logs_dir / "gunicorn_access.log"
        ]
        
        for log_file in log_files:
            if log_file.exists():
                self.log(f"سجل: {log_file.name}")
                print("-" * 30)
                
                if follow:
                    command = f"tail -f -n {lines} {log_file}"
                else:
                    command = f"tail -n {lines} {log_file}"
                
                os.system(command)
                print()
    
    def health_check(self):
        """فحص صحة التطبيق"""
        self.log("فحص صحة التطبيق...")
        
        # فحص Gunicorn
        success, stdout, stderr = self.run_command("curl -s http://127.0.0.1:8000/health")
        if success:
            self.log("✅ Gunicorn يستجيب", "SUCCESS")
        else:
            self.log("❌ Gunicorn لا يستجيب", "ERROR")
        
        # فحص Nginx
        success, stdout, stderr = self.run_command("curl -s http://localhost/")
        if success:
            self.log("✅ Nginx يستجيب", "SUCCESS")
        else:
            self.log("❌ Nginx لا يستجيب", "ERROR")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="أداة إدارة خدمة Ta9affi")
    parser.add_argument("--app-dir", help="مجلد التطبيق", default=None)
    
    subparsers = parser.add_subparsers(dest="command", help="الأوامر المتاحة")
    
    # أوامر الإدارة
    subparsers.add_parser("start", help="بدء التطبيق")
    subparsers.add_parser("stop", help="إيقاف التطبيق")
    subparsers.add_parser("restart", help="إعادة تشغيل التطبيق")
    subparsers.add_parser("reload", help="إعادة تحميل التطبيق")
    subparsers.add_parser("status", help="عرض حالة التطبيق")
    subparsers.add_parser("health", help="فحص صحة التطبيق")
    
    # أوامر السجلات
    logs_parser = subparsers.add_parser("logs", help="عرض السجلات")
    logs_parser.add_argument("-n", "--lines", type=int, default=50, help="عدد الأسطر")
    logs_parser.add_argument("-f", "--follow", action="store_true", help="متابعة السجلات")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # إنشاء مثيل الخدمة
    service = Ta9affiService(args.app_dir)
    
    # تنفيذ الأمر
    if args.command == "start":
        service.start()
    elif args.command == "stop":
        service.stop()
    elif args.command == "restart":
        service.restart()
    elif args.command == "reload":
        service.reload()
    elif args.command == "status":
        service.status()
    elif args.command == "health":
        service.health_check()
    elif args.command == "logs":
        service.logs(args.lines, args.follow)

if __name__ == "__main__":
    main()
