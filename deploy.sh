#!/bin/bash
# سكريبت النشر للبيئة الإنتاجية - Ta9affi

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# دوال المساعدة
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# متغيرات الإعداد
PROJECT_NAME="ta9affi"
DOCKER_COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env.production"
BACKUP_DIR="backups/deployment"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# التحقق من المتطلبات
check_requirements() {
    log_header "🔍 التحقق من المتطلبات"
    
    # التحقق من Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker غير مثبت"
        exit 1
    fi
    
    # التحقق من Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose غير مثبت"
        exit 1
    fi
    
    # التحقق من ملف البيئة
    if [ ! -f "$ENV_FILE" ]; then
        log_error "ملف البيئة $ENV_FILE غير موجود"
        log_info "يرجى إنشاء ملف البيئة من القالب: cp .env.example $ENV_FILE"
        exit 1
    fi
    
    # التحقق من ملف Docker Compose
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        log_error "ملف Docker Compose $DOCKER_COMPOSE_FILE غير موجود"
        exit 1
    fi
    
    log_success "جميع المتطلبات متوفرة"
}

# إنشاء نسخة احتياطية
create_backup() {
    log_header "💾 إنشاء نسخة احتياطية"
    
    mkdir -p "$BACKUP_DIR"
    
    # نسخ احتياطي لقاعدة البيانات
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log_info "إنشاء نسخة احتياطية لقاعدة البيانات..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump -U ta9affi_user ta9affi > "$BACKUP_DIR/database_backup_$TIMESTAMP.sql"
        gzip "$BACKUP_DIR/database_backup_$TIMESTAMP.sql"
        log_success "تم إنشاء نسخة احتياطية لقاعدة البيانات"
    fi
    
    # نسخ احتياطي للملفات المرفوعة
    if [ -d "uploads" ]; then
        log_info "إنشاء نسخة احتياطية للملفات المرفوعة..."
        tar -czf "$BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz" uploads/
        log_success "تم إنشاء نسخة احتياطية للملفات المرفوعة"
    fi
    
    # نسخ احتياطي للإعدادات
    log_info "إنشاء نسخة احتياطية للإعدادات..."
    tar -czf "$BACKUP_DIR/config_backup_$TIMESTAMP.tar.gz" \
        "$ENV_FILE" \
        "$DOCKER_COMPOSE_FILE" \
        nginx/ \
        monitoring/ \
        2>/dev/null || true
    
    log_success "تم إنشاء النسخ الاحتياطية في $BACKUP_DIR"
}

# بناء الصور
build_images() {
    log_header "🔨 بناء صور Docker"
    
    log_info "بناء صورة التطبيق الرئيسي..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build ta9affi-app
    
    log_info "بناء صورة النسخ الاحتياطي..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build backup-service
    
    log_success "تم بناء جميع الصور"
}

# تحديث قاعدة البيانات
update_database() {
    log_header "🗄️ تحديث قاعدة البيانات"
    
    # انتظار جاهزية قاعدة البيانات
    log_info "انتظار جاهزية قاعدة البيانات..."
    timeout 60 bash -c 'until docker-compose -f "$DOCKER_COMPOSE_FILE" exec postgres pg_isready -U ta9affi_user; do sleep 2; done'
    
    # تشغيل migrations
    log_info "تشغيل migrations..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec ta9affi-app flask db upgrade
    
    log_success "تم تحديث قاعدة البيانات"
}

# تحسين الملفات الثابتة
optimize_static_files() {
    log_header "⚡ تحسين الملفات الثابتة"
    
    log_info "تحسين ملفات CSS و JavaScript..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec ta9affi-app python -c "
from frontend_optimizer import optimize_frontend_assets
from flask import Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'temp-key'
app.static_folder = 'static'
with app.app_context():
    optimize_frontend_assets(app)
"
    
    log_success "تم تحسين الملفات الثابتة"
}

# فحص الصحة
health_check() {
    log_header "🏥 فحص صحة الخدمات"
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "محاولة فحص الصحة ($attempt/$max_attempts)..."
        
        # فحص التطبيق الرئيسي
        if curl -f -s http://localhost/health > /dev/null; then
            log_success "التطبيق الرئيسي يعمل بشكل صحيح"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "فشل في فحص صحة التطبيق"
            return 1
        fi
        
        sleep 10
        ((attempt++))
    done
    
    # فحص قاعدة البيانات
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec postgres pg_isready -U ta9affi_user > /dev/null; then
        log_success "قاعدة البيانات تعمل بشكل صحيح"
    else
        log_error "مشكلة في قاعدة البيانات"
        return 1
    fi
    
    # فحص Redis
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis يعمل بشكل صحيح"
    else
        log_error "مشكلة في Redis"
        return 1
    fi
    
    log_success "جميع الخدمات تعمل بشكل صحيح"
}

# تشغيل اختبارات الدخان
smoke_tests() {
    log_header "🧪 تشغيل اختبارات الدخان"
    
    # اختبار الصفحة الرئيسية
    if curl -f -s http://localhost/ > /dev/null; then
        log_success "الصفحة الرئيسية تعمل"
    else
        log_error "مشكلة في الصفحة الرئيسية"
        return 1
    fi
    
    # اختبار API
    if curl -f -s http://localhost/api/health > /dev/null; then
        log_success "API يعمل"
    else
        log_error "مشكلة في API"
        return 1
    fi
    
    # اختبار الملفات الثابتة
    if curl -f -s http://localhost/static/css/style.css > /dev/null; then
        log_success "الملفات الثابتة تعمل"
    else
        log_warning "قد تكون هناك مشكلة في الملفات الثابتة"
    fi
    
    log_success "اختبارات الدخان نجحت"
}

# إعداد المراقبة
setup_monitoring() {
    log_header "📊 إعداد المراقبة"
    
    # انتظار جاهزية Prometheus
    log_info "انتظار جاهزية Prometheus..."
    timeout 60 bash -c 'until curl -f -s http://localhost:9090/-/ready > /dev/null; do sleep 2; done'
    
    # انتظار جاهزية Grafana
    log_info "انتظار جاهزية Grafana..."
    timeout 60 bash -c 'until curl -f -s http://localhost:3000/api/health > /dev/null; do sleep 2; done'
    
    log_success "تم إعداد المراقبة"
}

# تنظيف الموارد القديمة
cleanup() {
    log_header "🧹 تنظيف الموارد القديمة"
    
    # إزالة الصور غير المستخدمة
    log_info "إزالة صور Docker غير المستخدمة..."
    docker image prune -f
    
    # إزالة الحاويات المتوقفة
    log_info "إزالة الحاويات المتوقفة..."
    docker container prune -f
    
    # إزالة الشبكات غير المستخدمة
    log_info "إزالة الشبكات غير المستخدمة..."
    docker network prune -f
    
    # تنظيف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 7 أيام)
    log_info "تنظيف النسخ الاحتياطية القديمة..."
    find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete 2>/dev/null || true
    
    log_success "تم تنظيف الموارد القديمة"
}

# عرض معلومات النشر
show_deployment_info() {
    log_header "📋 معلومات النشر"
    
    echo "=================================="
    echo "🚀 تم نشر Ta9affi بنجاح!"
    echo "=================================="
    echo "📅 تاريخ النشر: $(date)"
    echo "🌐 الرابط الرئيسي: http://localhost"
    echo "🔒 الرابط الآمن: https://localhost"
    echo "📊 Grafana: http://localhost:3000"
    echo "📈 Prometheus: http://localhost:9090"
    echo "🔍 Kibana: http://localhost:5601"
    echo "=================================="
    echo ""
    echo "📝 أوامر مفيدة:"
    echo "   عرض السجلات: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
    echo "   إعادة التشغيل: docker-compose -f $DOCKER_COMPOSE_FILE restart"
    echo "   الإيقاف: docker-compose -f $DOCKER_COMPOSE_FILE down"
    echo "   فحص الحالة: docker-compose -f $DOCKER_COMPOSE_FILE ps"
    echo "=================================="
}

# التراجع عن النشر
rollback() {
    log_header "🔄 التراجع عن النشر"
    
    log_warning "بدء عملية التراجع..."
    
    # إيقاف الخدمات الحالية
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # استعادة النسخة الاحتياطية لقاعدة البيانات
    local latest_db_backup=$(ls -t "$BACKUP_DIR"/database_backup_*.sql.gz 2>/dev/null | head -1)
    if [ -n "$latest_db_backup" ]; then
        log_info "استعادة قاعدة البيانات من $latest_db_backup"
        gunzip -c "$latest_db_backup" | docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql -U ta9affi_user ta9affi
    fi
    
    # استعادة الملفات المرفوعة
    local latest_uploads_backup=$(ls -t "$BACKUP_DIR"/uploads_backup_*.tar.gz 2>/dev/null | head -1)
    if [ -n "$latest_uploads_backup" ]; then
        log_info "استعادة الملفات المرفوعة من $latest_uploads_backup"
        rm -rf uploads/
        tar -xzf "$latest_uploads_backup"
    fi
    
    log_success "تم التراجع عن النشر"
}

# معالجة المعاملات
case "${1:-deploy}" in
    "deploy")
        log_header "🚀 بدء عملية النشر لـ Ta9affi"
        check_requirements
        create_backup
        
        # إيقاف الخدمات القديمة
        docker-compose -f "$DOCKER_COMPOSE_FILE" down 2>/dev/null || true
        
        # بناء وتشغيل الخدمات
        build_images
        docker-compose -f "$DOCKER_COMPOSE_FILE" --env-file "$ENV_FILE" up -d
        
        # تحديث قاعدة البيانات
        update_database
        
        # تحسين الملفات الثابتة
        optimize_static_files
        
        # فحص الصحة
        health_check
        
        # اختبارات الدخان
        smoke_tests
        
        # إعداد المراقبة
        setup_monitoring
        
        # تنظيف الموارد القديمة
        cleanup
        
        # عرض معلومات النشر
        show_deployment_info
        ;;
        
    "rollback")
        rollback
        ;;
        
    "health")
        health_check
        ;;
        
    "backup")
        create_backup
        ;;
        
    "cleanup")
        cleanup
        ;;
        
    "logs")
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f "${2:-}"
        ;;
        
    "status")
        docker-compose -f "$DOCKER_COMPOSE_FILE" ps
        ;;
        
    "restart")
        docker-compose -f "$DOCKER_COMPOSE_FILE" restart "${2:-}"
        ;;
        
    "stop")
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        ;;
        
    "help")
        echo "استخدام: $0 [COMMAND]"
        echo ""
        echo "الأوامر:"
        echo "  deploy    - نشر التطبيق (افتراضي)"
        echo "  rollback  - التراجع عن النشر"
        echo "  health    - فحص صحة الخدمات"
        echo "  backup    - إنشاء نسخة احتياطية"
        echo "  cleanup   - تنظيف الموارد القديمة"
        echo "  logs      - عرض السجلات"
        echo "  status    - عرض حالة الخدمات"
        echo "  restart   - إعادة تشغيل الخدمات"
        echo "  stop      - إيقاف الخدمات"
        echo "  help      - عرض هذه المساعدة"
        ;;
        
    *)
        log_error "أمر غير معروف: $1"
        echo "استخدم '$0 help' لعرض الأوامر المتاحة"
        exit 1
        ;;
esac
