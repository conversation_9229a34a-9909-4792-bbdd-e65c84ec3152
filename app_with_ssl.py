#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ta9affi مع SSL مدمج في Flask
"""

import os
import ssl
from flask import Flask, request
from datetime import datetime

# استيراد التطبيق الأساسي
from app import *

def create_ssl_context():
    """إنشاء SSL context لـ Flask"""
    
    try:
        # مسارات الشهادات
        cert_file = os.environ.get('SSL_CERT_PATH', '/etc/letsencrypt/live/ta9affi.com/fullchain.pem')
        key_file = os.environ.get('SSL_KEY_PATH', '/etc/letsencrypt/live/ta9affi.com/privkey.pem')
        
        # التحقق من وجود الملفات
        if not os.path.exists(cert_file):
            print(f"❌ ملف الشهادة غير موجود: {cert_file}")
            return None
            
        if not os.path.exists(key_file):
            print(f"❌ ملف المفتاح غير موجود: {key_file}")
            return None
        
        # إنشاء SSL context
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(cert_file, key_file)
        
        # إعدادات أمان إضافية
        context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
        context.options |= ssl.OP_NO_SSLv2
        context.options |= ssl.OP_NO_SSLv3
        context.options |= ssl.OP_NO_TLSv1
        context.options |= ssl.OP_NO_TLSv1_1
        
        print(f"✅ تم إنشاء SSL context بنجاح")
        print(f"📜 الشهادة: {cert_file}")
        print(f"🔑 المفتاح: {key_file}")
        
        return context
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء SSL context: {str(e)}")
        return None

def setup_ssl_app():
    """إعداد التطبيق مع SSL"""
    
    # إضافة middleware لإعادة توجيه HTTP إلى HTTPS
    @app.before_request
    def force_https():
        if not request.is_secure and app.env != 'development':
            return redirect(request.url.replace('http://', 'https://'))
    
    # إضافة headers أمان
    @app.after_request
    def add_security_headers(response):
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response
    
    return app

def run_with_ssl():
    """تشغيل التطبيق مع SSL"""
    
    print("🚀 تشغيل Ta9affi مع SSL مدمج")
    print("=" * 50)
    
    # إعداد التطبيق
    ssl_app = setup_ssl_app()
    
    # إنشاء SSL context
    ssl_context = create_ssl_context()
    
    if ssl_context:
        print("🔐 تشغيل مع SSL...")
        
        # تشغيل على البورت 443 (HTTPS)
        ssl_app.run(
            host='0.0.0.0',
            port=443,
            ssl_context=ssl_context,
            debug=False,
            threaded=True
        )
    else:
        print("⚠️ فشل SSL، تشغيل بدون SSL...")
        
        # تشغيل عادي على البورت 80
        ssl_app.run(
            host='0.0.0.0',
            port=80,
            debug=False,
            threaded=True
        )

def run_dual_mode():
    """تشغيل مع HTTP و HTTPS معاً"""
    
    import threading
    from werkzeug.serving import make_server
    
    print("🚀 تشغيل Ta9affi في وضع مزدوج (HTTP + HTTPS)")
    print("=" * 60)
    
    ssl_app = setup_ssl_app()
    
    # خادم HTTP (البورت 80)
    http_server = make_server('0.0.0.0', 80, ssl_app)
    http_thread = threading.Thread(target=http_server.serve_forever)
    http_thread.daemon = True
    http_thread.start()
    print("✅ خادم HTTP يعمل على البورت 80")
    
    # خادم HTTPS (البورت 443)
    ssl_context = create_ssl_context()
    if ssl_context:
        https_server = make_server('0.0.0.0', 443, ssl_app, ssl_context=ssl_context)
        https_thread = threading.Thread(target=https_server.serve_forever)
        https_thread.daemon = True
        https_thread.start()
        print("✅ خادم HTTPS يعمل على البورت 443")
    else:
        print("❌ فشل في تشغيل خادم HTTPS")
    
    print("\n🌐 التطبيق متاح على:")
    print("   HTTP:  http://ta9affi.com")
    print("   HTTPS: https://ta9affi.com")
    print("\nاضغط Ctrl+C للإيقاف...")
    
    try:
        # إبقاء التطبيق يعمل
        while True:
            import time
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 إيقاف الخوادم...")
        http_server.shutdown()
        if ssl_context:
            https_server.shutdown()

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'dual':
        run_dual_mode()
    else:
        run_with_ssl()
