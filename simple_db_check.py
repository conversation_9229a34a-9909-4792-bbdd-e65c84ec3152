#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص بسيطة لحجم قاعدة البيانات
"""

import sqlite3
import os

def check_sqlite_database():
    """فحص قاعدة البيانات SQLite مباشرة"""
    
    db_path = "instance/ta9affi.db"
    
    if not os.path.exists(db_path):
        print(f"❌ لم يتم العثور على قاعدة البيانات: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 60)
        print("تقرير حجم قاعدة البيانات")
        print("=" * 60)
        
        # فحص جدول LevelDataEntry
        cursor.execute("SELECT COUNT(*) FROM level_data_entry")
        total_entries = cursor.fetchone()[0]
        
        print(f"📊 إجمالي البيانات في level_data_entry: {total_entries}")
        
        # فحص حسب النوع
        entry_types = ['subject', 'domain', 'material', 'competency']
        
        for entry_type in entry_types:
            cursor.execute("SELECT COUNT(*) FROM level_data_entry WHERE entry_type = ?", (entry_type,))
            count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM level_data_entry WHERE entry_type = ? AND is_active = 1", (entry_type,))
            active_count = cursor.fetchone()[0]
            
            type_names = {
                'subject': 'المواد الدراسية',
                'domain': 'الميادين/الأنشطة', 
                'material': 'الموارد المعرفية',
                'competency': 'الكفاءات المستهدفة'
            }
            
            print(f"   {type_names[entry_type]}: {count} (نشطة: {active_count})")
            
            if entry_type == 'competency' and count > 1000:
                print(f"   ⚠️  تحذير: عدد كبير من الكفاءات قد يسبب مشاكل في الأداء")
            elif entry_type == 'material' and count > 500:
                print(f"   ⚠️  تحذير: عدد كبير من الموارد المعرفية قد يسبب مشاكل في الأداء")
        
        # فحص حسب قاعدة البيانات
        print("\n" + "-" * 40)
        print("التوزيع حسب قواعد البيانات:")
        
        cursor.execute("""
            SELECT ld.name, ld.id, COUNT(lde.id) as entry_count
            FROM level_database ld
            LEFT JOIN level_data_entry lde ON ld.id = lde.database_id
            GROUP BY ld.id, ld.name
            ORDER BY entry_count DESC
        """)
        
        databases = cursor.fetchall()
        
        for db_name, db_id, entry_count in databases:
            print(f"   📚 {db_name}: {entry_count} عنصر")
            
            if entry_count > 2000:
                print(f"      🚨 تحذير: عدد كبير جداً من البيانات!")
            elif entry_count > 1000:
                print(f"      ⚠️  تحذير: عدد كبير من البيانات")
        
        # حجم الملف
        file_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
        print(f"\n📁 حجم ملف قاعدة البيانات: {file_size:.2f} MB")
        
        if file_size > 100:
            print("🚨 تحذير: حجم قاعدة البيانات كبير جداً!")
        elif file_size > 50:
            print("⚠️  تنبيه: حجم قاعدة البيانات مرتفع")
        else:
            print("✅ حجم قاعدة البيانات مقبول")
        
        print("\n" + "=" * 60)
        print("💡 التوصيات:")
        if total_entries > 5000:
            print("   1. ✅ تم تطبيق تحسينات الأداء (عرض عينة محدودة)")
            print("   2. ✅ تم إضافة إحصائيات سريعة")
            print("   3. ✅ تم إضافة تحذيرات للمستخدم")
            print("   4. 💡 يُنصح بإضافة التصفح (Pagination) للبيانات الكثيرة")
        else:
            print("   ✅ حجم البيانات ضمن الحدود المقبولة")
        print("=" * 60)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")

if __name__ == "__main__":
    check_sqlite_database()
