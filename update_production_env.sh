#!/bin/bash
# سكريبت تحديث متغيرات البيئة للإنتاج - Ta9affi

echo "🚀 تحديث متغيرات البيئة للإنتاج - Ta9affi"
echo "=================================================="

# التحقق من وجود ملف dokploy.config.js
if [ ! -f "dokploy.config.js" ]; then
    echo "❌ ملف dokploy.config.js غير موجود"
    exit 1
fi

echo "✅ ملف dokploy.config.js موجود"

# عرض متغيرات البيئة المطلوبة للإنتاج
echo ""
echo "📋 متغيرات البيئة المطلوبة للإنتاج:"
echo "======================================"
echo "FLASK_ENV=production"
echo "PRODUCTION_MODE=true"
echo "BASE_URL=http://ta9affi.com"
echo "CHARGILY_WEBHOOK_URL=http://ta9affi.com/chargily-webhook"
echo "CHARGILY_PUBLIC_KEY=live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk"
echo "CHARGILY_SECRET_KEY=live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU"
echo "SECRET_KEY=ta9affi-production-secret-key-2024-very-secure"

echo ""
echo "🔍 التحقق من ملف dokploy.config.js الحالي:"
echo "==========================================="

# التحقق من وجود متغيرات البيئة في dokploy.config.js
if grep -q "FLASK_ENV.*production" dokploy.config.js; then
    echo "✅ FLASK_ENV=production موجود"
else
    echo "❌ FLASK_ENV=production مفقود"
fi

if grep -q "BASE_URL.*ta9affi.com" dokploy.config.js; then
    echo "✅ BASE_URL صحيح"
else
    echo "❌ BASE_URL مفقود أو خطأ"
fi

if grep -q "CHARGILY_WEBHOOK_URL.*ta9affi.com" dokploy.config.js; then
    echo "✅ CHARGILY_WEBHOOK_URL صحيح"
else
    echo "❌ CHARGILY_WEBHOOK_URL مفقود أو خطأ"
fi

echo ""
echo "📝 الخطوات التالية:"
echo "==================="
echo "1. تحديث dokploy.config.js إذا لزم الأمر"
echo "2. Commit التغييرات: git add . && git commit -m 'Fix Chargily environment URLs'"
echo "3. Push إلى GitHub: git push origin main"
echo "4. Deploy في dokploy dashboard"
echo "5. اختبار الدفع على: http://ta9affi.com/subscription/plans"

echo ""
echo "🧪 لاختبار الإعدادات محلياً:"
echo "============================"
echo "python test_chargily_config.py"

echo ""
echo "✅ انتهى التحقق من إعدادات الإنتاج"
