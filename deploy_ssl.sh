#!/bin/bash

# سكريبت نشر SSL لـ Ta9affi
echo "🔐 نشر SSL لـ Ta9affi"
echo "===================="

# التحقق من وجود ملفات SSL
if [ ! -d "ssl_certs" ]; then
    echo "❌ مجلد ssl_certs غير موجود"
    exit 1
fi

if [ ! -f "ssl_certs/fullchain.pem" ] || [ ! -f "ssl_certs/ta9affi.key" ]; then
    echo "❌ ملفات SSL غير مكتملة"
    exit 1
fi

echo "✅ ملفات SSL موجودة"

# نسخ ملفات SSL إلى الخادم
echo "📤 نسخ ملفات SSL إلى الخادم..."
scp -r ssl_certs/ <EMAIL>:/app/

# نسخ إعداد nginx
echo "📤 نسخ إعداد nginx..."
scp nginx_ssl_production.conf <EMAIL>:/tmp/

# تطبيق إعداد nginx على الخادم
echo "🔧 تطبيق إعداد nginx..."
ssh <EMAIL> << 'EOF'
    # نسخ إعداد nginx
    cp /tmp/nginx_ssl_production.conf /etc/nginx/sites-available/ta9affi
    
    # تفعيل الموقع
    ln -sf /etc/nginx/sites-available/ta9affi /etc/nginx/sites-enabled/
    
    # إزالة الإعداد الافتراضي
    rm -f /etc/nginx/sites-enabled/default
    
    # تعيين صلاحيات SSL
    chmod 600 /app/ssl_certs/ta9affi.key
    chmod 644 /app/ssl_certs/fullchain.pem
    
    # اختبار nginx
    nginx -t
    
    if [ $? -eq 0 ]; then
        echo "✅ إعداد nginx صحيح"
        systemctl reload nginx
        echo "✅ تم إعادة تحميل nginx"
    else
        echo "❌ خطأ في إعداد nginx"
        exit 1
    fi
EOF

echo "🚀 تم نشر SSL بنجاح!"
echo "🌐 الموقع متاح الآن على: https://ta9affi.com"
