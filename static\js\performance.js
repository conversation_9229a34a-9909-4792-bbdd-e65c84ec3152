/**
 * ملف JavaScript محسن للأداء - Ta9affi
 * يحتوي على تحسينات الأداء والتحميل التدريجي
 */

// تحسين الأداء العام
(function() {
    'use strict';

    // تحسين console.log في الإنتاج
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        console.log = console.warn = console.error = function() {};
    }

    // تحسين requestAnimationFrame
    window.requestAnimFrame = (function() {
        return window.requestAnimationFrame ||
               window.webkitRequestAnimationFrame ||
               window.mozRequestAnimationFrame ||
               function(callback) {
                   window.setTimeout(callback, 1000 / 60);
               };
    })();

    // تحسين cancelAnimationFrame
    window.cancelAnimFrame = (function() {
        return window.cancelAnimationFrame ||
               window.webkitCancelAnimationFrame ||
               window.mozCancelAnimationFrame ||
               function(id) {
                   clearTimeout(id);
               };
    })();

})();

// مدير التحميل التدريجي
class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.contentObserver = null;
        this.init();
    }

    init() {
        // تحميل تدريجي للصور
        this.setupImageLazyLoading();
        
        // تحميل تدريجي للمحتوى
        this.setupContentLazyLoading();
        
        // تحميل تدريجي للسكريبتات
        this.setupScriptLazyLoading();
    }

    setupImageLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        this.imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                this.imageObserver.observe(img);
            });
        } else {
            // fallback للمتصفحات القديمة
            this.loadAllImages();
        }
    }

    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            img.src = src;
            img.classList.add('loaded');
            img.removeAttribute('data-src');
        }
    }

    loadAllImages() {
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.loadImage(img);
        });
    }

    setupContentLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.contentObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        this.loadContent(element);
                        this.contentObserver.unobserve(element);
                    }
                });
            }, {
                rootMargin: '100px 0px',
                threshold: 0.01
            });

            document.querySelectorAll('[data-lazy-content]').forEach(element => {
                this.contentObserver.observe(element);
            });
        }
    }

    loadContent(element) {
        const url = element.dataset.lazyContent;
        if (url) {
            this.fetchContent(url, element);
        }
    }

    async fetchContent(url, container) {
        try {
            container.innerHTML = '<div class="loading-spinner"></div>';
            
            const response = await fetch(url);
            if (!response.ok) throw new Error('Network response was not ok');
            
            const html = await response.text();
            container.innerHTML = html;
            
            // تفعيل السكريبتات الجديدة
            this.activateScripts(container);
            
        } catch (error) {
            console.error('Error loading content:', error);
            container.innerHTML = '<div class="alert alert-danger">خطأ في تحميل المحتوى</div>';
        }
    }

    activateScripts(container) {
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => {
            const newScript = document.createElement('script');
            if (script.src) {
                newScript.src = script.src;
            } else {
                newScript.textContent = script.textContent;
            }
            document.head.appendChild(newScript);
        });
    }

    setupScriptLazyLoading() {
        document.querySelectorAll('script[data-lazy]').forEach(script => {
            const src = script.dataset.lazy;
            if (src) {
                this.loadScript(src);
            }
        });
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
}

// مدير التخزين المؤقت
class CacheManager {
    constructor() {
        this.cache = new Map();
        this.maxSize = 100;
        this.ttl = 5 * 60 * 1000; // 5 دقائق
    }

    set(key, value, ttl = this.ttl) {
        // إزالة العناصر القديمة إذا تجاوز الحد الأقصى
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            value: value,
            timestamp: Date.now(),
            ttl: ttl
        });
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;

        // فحص انتهاء الصلاحية
        if (Date.now() - item.timestamp > item.ttl) {
            this.cache.delete(key);
            return null;
        }

        return item.value;
    }

    clear() {
        this.cache.clear();
    }

    size() {
        return this.cache.size;
    }
}

// مدير الطلبات المحسن
class RequestManager {
    constructor() {
        this.pendingRequests = new Map();
        this.cache = new CacheManager();
    }

    async fetch(url, options = {}) {
        const cacheKey = this.getCacheKey(url, options);
        
        // فحص التخزين المؤقت
        const cachedResponse = this.cache.get(cacheKey);
        if (cachedResponse && options.cache !== 'no-cache') {
            return cachedResponse;
        }

        // فحص الطلبات المعلقة لتجنب التكرار
        if (this.pendingRequests.has(cacheKey)) {
            return this.pendingRequests.get(cacheKey);
        }

        // إجراء الطلب
        const requestPromise = this.makeRequest(url, options);
        this.pendingRequests.set(cacheKey, requestPromise);

        try {
            const response = await requestPromise;
            
            // حفظ في التخزين المؤقت
            if (response.ok && options.cache !== 'no-store') {
                const data = await response.clone().json();
                this.cache.set(cacheKey, data);
            }
            
            return response;
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }

    async makeRequest(url, options) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };
        
        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            mergedOptions.headers['X-CSRF-Token'] = csrfToken.getAttribute('content');
        }

        return fetch(url, mergedOptions);
    }

    getCacheKey(url, options) {
        return `${options.method || 'GET'}_${url}_${JSON.stringify(options.body || {})}`;
    }
}

// مدير الأداء
class PerformanceManager {
    constructor() {
        this.metrics = {};
        this.observers = [];
        this.init();
    }

    init() {
        this.setupPerformanceObserver();
        this.setupResourceObserver();
        this.setupNavigationObserver();
    }

    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    this.recordMetric(entry.name, entry.duration);
                });
            });
            
            observer.observe({ entryTypes: ['measure', 'navigation'] });
            this.observers.push(observer);
        }
    }

    setupResourceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.duration > 1000) { // أكثر من ثانية
                        console.warn(`Slow resource: ${entry.name} took ${entry.duration}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['resource'] });
            this.observers.push(observer);
        }
    }

    setupNavigationObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    this.metrics.navigation = {
                        domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                        loadComplete: entry.loadEventEnd - entry.loadEventStart,
                        firstPaint: entry.responseEnd - entry.requestStart
                    };
                });
            });
            
            observer.observe({ entryTypes: ['navigation'] });
            this.observers.push(observer);
        }
    }

    recordMetric(name, value) {
        if (!this.metrics[name]) {
            this.metrics[name] = [];
        }
        this.metrics[name].push(value);
    }

    getMetrics() {
        return this.metrics;
    }

    measure(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        this.recordMetric(name, end - start);
        return result;
    }

    async measureAsync(name, fn) {
        const start = performance.now();
        const result = await fn();
        const end = performance.now();
        this.recordMetric(name, end - start);
        return result;
    }
}

// مدير الأحداث المحسن
class EventManager {
    constructor() {
        this.listeners = new Map();
        this.throttledEvents = new Set(['scroll', 'resize', 'mousemove']);
        this.debouncedEvents = new Set(['input', 'keyup']);
    }

    on(element, event, handler, options = {}) {
        let optimizedHandler = handler;

        // تحسين الأحداث المتكررة
        if (this.throttledEvents.has(event)) {
            optimizedHandler = this.throttle(handler, options.throttle || 16);
        } else if (this.debouncedEvents.has(event)) {
            optimizedHandler = this.debounce(handler, options.debounce || 300);
        }

        element.addEventListener(event, optimizedHandler, options);

        // حفظ المرجع للإزالة لاحقاً
        const key = `${element}_${event}_${handler}`;
        this.listeners.set(key, { element, event, handler: optimizedHandler, options });
    }

    off(element, event, handler) {
        const key = `${element}_${event}_${handler}`;
        const listener = this.listeners.get(key);
        
        if (listener) {
            element.removeEventListener(event, listener.handler, listener.options);
            this.listeners.delete(key);
        }
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
}

// تهيئة المدراء
const lazyLoader = new LazyLoader();
const requestManager = new RequestManager();
const performanceManager = new PerformanceManager();
const eventManager = new EventManager();

// تصدير للاستخدام العام
window.Ta9affi = {
    LazyLoader: lazyLoader,
    RequestManager: requestManager,
    PerformanceManager: performanceManager,
    EventManager: eventManager,
    
    // دوال مساعدة
    loadContent: (url, containerId) => lazyLoader.fetchContent(url, document.getElementById(containerId)),
    measure: (name, fn) => performanceManager.measure(name, fn),
    measureAsync: (name, fn) => performanceManager.measureAsync(name, fn),
    getMetrics: () => performanceManager.getMetrics()
};

// تسجيل Service Worker
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/static/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// تحسين أداء الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تحسين النماذج
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', (e) => {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="loading-spinner"></span> جاري الإرسال...';
            }
        });
    });

    // تحسين الروابط الخارجية
    document.querySelectorAll('a[href^="http"]').forEach(link => {
        if (!link.href.includes(window.location.hostname)) {
            link.setAttribute('rel', 'noopener noreferrer');
            link.setAttribute('target', '_blank');
        }
    });

    // تحسين الجداول الكبيرة
    document.querySelectorAll('table').forEach(table => {
        if (table.rows.length > 100) {
            table.classList.add('large-table');
        }
    });
});

// تحسين معالجة الأخطاء
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
    // يمكن إرسال الأخطاء لخدمة المراقبة
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise Rejection:', e.reason);
    // يمكن إرسال الأخطاء لخدمة المراقبة
});
