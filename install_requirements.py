#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت متطلبات Ta9affi تلقائياً
يقوم بفحص وتثبيت جميع المكتبات المطلوبة
"""

import subprocess
import sys
import os
import platform

def run_command(command, description=""):
    """تشغيل أمر وطباعة النتيجة"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - تم بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - فشل: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    print(f"🐍 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def check_pip():
    """فحص وترقية pip"""
    print("🔄 فحص pip...")
    try:
        import pip
        print("✅ pip موجود")
        
        # ترقية pip
        run_command(f"{sys.executable} -m pip install --upgrade pip", "ترقية pip")
        return True
    except ImportError:
        print("❌ pip غير موجود")
        return False

def install_requirements():
    """تثبيت المتطلبات من requirements.txt"""
    if os.path.exists('requirements.txt'):
        return run_command(f"{sys.executable} -m pip install -r requirements.txt", 
                         "تثبيت المتطلبات من requirements.txt")
    else:
        print("⚠️ ملف requirements.txt غير موجود، سيتم تثبيت المكتبات يدوياً")
        return install_manual_requirements()

def install_manual_requirements():
    """تثبيت المكتبات يدوياً"""
    requirements = [
        "flask>=2.3.0",
        "flask-sqlalchemy>=3.0.0", 
        "flask-login>=0.6.0",
        "flask-wtf>=1.1.0",
        "werkzeug>=2.3.0",
        "pandas>=1.5.0",
        "openpyxl>=3.1.0",
        "chargily-pay",
        "schedule>=1.2.0",
        "pymysql>=1.0.0",
        "psycopg2-binary>=2.9.0",
        "redis>=4.5.0",
        "flask-session>=0.5.0",
        "flask-limiter>=3.3.0",
        "flask-talisman>=1.1.0",
        "gunicorn>=20.1.0",
        "gevent>=22.10.0",
        "flask-migrate>=4.0.0",
        "python-dotenv>=1.0.0",
        "requests>=2.28.0"
    ]
    
    success = True
    for req in requirements:
        if not run_command(f"{sys.executable} -m pip install {req}", f"تثبيت {req}"):
            success = False
    
    return success

def check_redis():
    """فحص Redis"""
    print("🔄 فحص Redis...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis يعمل بشكل صحيح")
        return True
    except ImportError:
        print("❌ مكتبة redis غير مثبتة")
        return False
    except Exception as e:
        print(f"⚠️ Redis غير متاح: {e}")
        print("💡 تأكد من تشغيل Redis server:")
        if platform.system() == "Windows":
            print("   - استخدم Docker: docker run -d -p 6379:6379 redis:7-alpine")
            print("   - أو قم بتثبيت Redis for Windows")
        else:
            print("   - Linux: sudo systemctl start redis")
            print("   - macOS: brew services start redis")
        return False

def create_env_file():
    """إنشاء ملف .env إذا لم يكن موجوداً"""
    if not os.path.exists('.env'):
        print("🔄 إنشاء ملف .env...")
        env_content = """# إعدادات Flask
FLASK_ENV=development
SECRET_KEY=dev-secret-key-change-in-production

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///instance/ta9affi.db

# إعدادات Redis
REDIS_URL=redis://localhost:6379/0

# إعدادات Chargily
CHARGILY_PUBLIC_KEY=your-public-key
CHARGILY_SECRET_KEY=your-secret-key
BASE_URL=http://127.0.0.1:5000
CHARGILY_WEBHOOK_URL=http://127.0.0.1:5000/chargily-webhook

# إعدادات السجلات
LOG_LEVEL=DEBUG
LOG_FILE=logs/ta9affi.log
"""
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(env_content)
            print("✅ تم إنشاء ملف .env")
            return True
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف .env: {e}")
            return False
    else:
        print("✅ ملف .env موجود")
        return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['instance', 'logs', 'uploads', 'static/exports']
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ مجلد {directory} جاهز")
        except Exception as e:
            print(f"❌ فشل في إنشاء مجلد {directory}: {e}")
            return False
    
    return True

def test_imports():
    """اختبار استيراد المكتبات الأساسية"""
    print("🔄 اختبار استيراد المكتبات...")
    
    test_imports = [
        ('flask', 'Flask'),
        ('flask_sqlalchemy', 'Flask-SQLAlchemy'),
        ('flask_login', 'Flask-Login'),
        ('pandas', 'Pandas'),
        ('openpyxl', 'OpenPyXL'),
        ('redis', 'Redis'),
        ('requests', 'Requests'),
        ('schedule', 'Schedule')
    ]
    
    success = True
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - غير مثبت")
            success = False
    
    return success

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تثبيت متطلبات Ta9affi")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # فحص pip
    if not check_pip():
        sys.exit(1)
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت بعض المتطلبات")
        print("💡 جرب تشغيل الأمر يدوياً: pip install -r requirements.txt")
    
    # إنشاء المجلدات
    create_directories()
    
    # إنشاء ملف .env
    create_env_file()
    
    # فحص Redis
    check_redis()
    
    # اختبار الاستيراد
    if test_imports():
        print("\n🎉 تم تثبيت جميع المتطلبات بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. تأكد من تشغيل Redis server")
        print("2. قم بتشغيل: python init_database.py")
        print("3. قم بتشغيل: python run.py")
        print("4. افتح المتصفح على: http://127.0.0.1:5000")
    else:
        print("\n⚠️ بعض المكتبات لم يتم تثبيتها بشكل صحيح")
        print("جرب تشغيل: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
