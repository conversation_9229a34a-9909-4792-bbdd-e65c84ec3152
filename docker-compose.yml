version: '3.8'

services:
  ta9affi:
    build: .
    ports:
      - "0.0.0.0:8000:8000"    # المنفذ 8000 للاتساق مع nginx
      - "0.0.0.0:8080:8000"    # منفذ إضافي للاختبار
    environment:
      - REDIS_URL=redis://redis:6379/0
      - PORT=8000              # المنفذ الجديد
      - FLASK_ENV=production
      - PRODUCTION_MODE=true
      - WORKER_CLASS=gevent    # تفعيل gevent patch
      - CHARGILY_PUBLIC_KEY=${CHARGILY_PUBLIC_KEY}
      - CHARGILY_SECRET_KEY=${CHARGILY_SECRET_KEY}
      - BASE_URL=https://ta9affi.com
      - CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook
    depends_on:
      - redis
    networks:
      - ta9affi_network
    restart: unless-stopped
    command: gunicorn --config gunicorn.conf.py app:app

  redis:
    image: redis:7-alpine
    container_name: ta9affi_redis
    restart: unless-stopped
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - ta9affi_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  ta9affi_network:
    driver: bridge
    enable_ipv6: false








