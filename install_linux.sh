#!/bin/bash
# سكريپت تثبيت متطلبات Ta9affi على Linux/macOS

set -e  # إيقاف السكريپت عند حدوث خطأ

echo "🚀 تثبيت متطلبات Ta9affi على Linux/macOS"
echo "=========================================="

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسائل ملونة
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}🔄 $1${NC}"
}

# فحص نظام التشغيل
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "🖥️ نظام التشغيل: $MACHINE"

# فحص Python
print_info "فحص Python..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_success "Python $PYTHON_VERSION موجود"
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
    print_success "Python $PYTHON_VERSION موجود"
    PYTHON_CMD="python"
else
    print_error "Python غير مثبت"
    echo "💡 قم بتثبيت Python 3.8+ أولاً"
    exit 1
fi

# فحص pip
print_info "فحص pip..."
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
elif command -v pip &> /dev/null; then
    PIP_CMD="pip"
else
    print_error "pip غير مثبت"
    exit 1
fi

print_success "pip موجود"

# تثبيت متطلبات النظام
print_info "فحص متطلبات النظام..."

if [[ "$MACHINE" == "Linux" ]]; then
    # فحص إذا كان المستخدم يملك صلاحيات sudo
    if command -v sudo &> /dev/null; then
        print_info "تثبيت متطلبات النظام على Linux..."
        
        # تحديد نوع التوزيعة
        if command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            sudo apt-get update
            sudo apt-get install -y build-essential gcc g++ libpq-dev libjpeg-dev libpng-dev libfreetype6-dev libffi-dev libssl-dev curl wget python3-dev python3-pip
        elif command -v yum &> /dev/null; then
            # CentOS/RHEL
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y postgresql-devel libjpeg-devel libpng-devel freetype-devel libffi-devel openssl-devel curl wget python3-devel python3-pip
        elif command -v dnf &> /dev/null; then
            # Fedora
            sudo dnf groupinstall -y "Development Tools"
            sudo dnf install -y postgresql-devel libjpeg-devel libpng-devel freetype-devel libffi-devel openssl-devel curl wget python3-devel python3-pip
        else
            print_warning "لم يتم التعرف على مدير الحزم، تأكد من تثبيت build tools يدوياً"
        fi
        
        print_success "تم تثبيت متطلبات النظام"
    else
        print_warning "sudo غير متاح، تأكد من تثبيت build tools يدوياً"
    fi
    
elif [[ "$MACHINE" == "Mac" ]]; then
    print_info "فحص Xcode Command Line Tools..."
    if ! xcode-select -p &> /dev/null; then
        print_info "تثبيت Xcode Command Line Tools..."
        xcode-select --install
        print_success "تم تثبيت Xcode Command Line Tools"
    else
        print_success "Xcode Command Line Tools موجود"
    fi
    
    # فحص Homebrew
    if command -v brew &> /dev/null; then
        print_success "Homebrew موجود"
        print_info "تثبيت متطلبات إضافية..."
        brew install postgresql libpq || true
    else
        print_warning "Homebrew غير مثبت، قد تحتاج لتثبيته لبعض المكتبات"
        echo "💡 لتثبيت Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    fi
fi

# ترقية pip
print_info "ترقية pip..."
$PYTHON_CMD -m pip install --upgrade pip

# إنشاء البيئة الافتراضية
print_info "إنشاء البيئة الافتراضية..."
if [ ! -d "venv" ]; then
    $PYTHON_CMD -m venv venv
    print_success "تم إنشاء البيئة الافتراضية"
else
    print_success "البيئة الافتراضية موجودة"
fi

# تفعيل البيئة الافتراضية
print_info "تفعيل البيئة الافتراضية..."
source venv/bin/activate

# تثبيت المتطلبات
print_info "تثبيت المتطلبات..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    if [ $? -eq 0 ]; then
        print_success "تم تثبيت المتطلبات بنجاح"
    else
        print_error "فشل في تثبيت بعض المتطلبات"
        print_warning "جرب تشغيل الأمر يدوياً: pip install -r requirements.txt"
    fi
else
    print_warning "ملف requirements.txt غير موجود"
    print_info "تثبيت المكتبات الأساسية..."
    pip install flask flask-sqlalchemy flask-login flask-wtf werkzeug pandas openpyxl chargily-pay schedule pymysql psycopg2-binary redis flask-session flask-limiter flask-talisman gunicorn gevent flask-migrate python-dotenv requests
fi

# إنشاء المجلدات
print_info "إنشاء المجلدات المطلوبة..."
mkdir -p instance logs uploads static/exports
print_success "تم إنشاء المجلدات"

# إنشاء ملف .env
print_info "إنشاء ملف .env..."
if [ ! -f ".env" ]; then
    cat > .env << EOF
# إعدادات Flask
FLASK_ENV=development
SECRET_KEY=dev-secret-key-change-in-production

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///instance/ta9affi.db

# إعدادات Redis
REDIS_URL=redis://localhost:6379/0

# إعدادات Chargily
CHARGILY_PUBLIC_KEY=your-public-key
CHARGILY_SECRET_KEY=your-secret-key
BASE_URL=http://127.0.0.1:5000
CHARGILY_WEBHOOK_URL=http://127.0.0.1:5000/chargily-webhook

# إعدادات السجلات
LOG_LEVEL=DEBUG
LOG_FILE=logs/ta9affi.log
EOF
    print_success "تم إنشاء ملف .env"
else
    print_success "ملف .env موجود"
fi

# فحص Redis
print_info "فحص Redis..."
if python -c "import redis; r=redis.Redis(); r.ping(); print('✅ Redis يعمل')" 2>/dev/null; then
    print_success "Redis يعمل بشكل صحيح"
else
    print_warning "Redis غير متاح"
    echo "💡 لتشغيل Redis:"
    if [[ "$MACHINE" == "Linux" ]]; then
        echo "   - Ubuntu/Debian: sudo apt-get install redis-server && sudo systemctl start redis"
        echo "   - CentOS/RHEL: sudo yum install redis && sudo systemctl start redis"
        echo "   - أو استخدم Docker: docker run -d -p 6379:6379 redis:7-alpine"
    elif [[ "$MACHINE" == "Mac" ]]; then
        echo "   - brew install redis && brew services start redis"
        echo "   - أو استخدم Docker: docker run -d -p 6379:6379 redis:7-alpine"
    fi
fi

# اختبار الاستيراد
print_info "اختبار المكتبات..."
python -c "
try:
    import flask, flask_sqlalchemy, flask_login, pandas, openpyxl, redis, requests
    print('✅ جميع المكتبات الأساسية مثبتة')
except ImportError as e:
    print(f'❌ مكتبة مفقودة: {e}')
"

echo ""
print_success "🎉 انتهى التثبيت!"
echo ""
echo "📋 الخطوات التالية:"
echo "1. تأكد من تشغيل Redis server"
echo "2. قم بتشغيل: python init_database.py"
echo "3. قم بتشغيل: python run.py"
echo "4. افتح المتصفح على: http://127.0.0.1:5000"
echo ""
echo "💡 لتفعيل البيئة الافتراضية في المرات القادمة:"
echo "   source venv/bin/activate"
echo ""
