# Dockerfile محسن لـ Ta9affi - الإنتاج عالي الأداء
FROM python:3.11-slim-bullseye

# معلومات الصورة
LABEL maintainer="Ta9affi Team"
LABEL version="1.0"
LABEL description="Ta9affi - نظام إدارة التقدم التعليمي"

# متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV FLASK_ENV=production
ENV FLASK_APP=app.py
ENV DEBIAN_FRONTEND=noninteractive

# إنشاء مستخدم غير جذر للأمان
RUN groupadd -r ta9affi && useradd -r -g ta9affi ta9affi

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    libpq-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    libffi-dev \
    libssl-dev \
    curl \
    wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# إنشاء مجلدات التطبيق
RUN mkdir -p /app /app/logs /app/uploads /app/static /app/templates \
    && chown -R ta9affi:ta9affi /app

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات المتطلبات أولاً
COPY requirements.txt /app/

# ترقية pip وتثبيت المتطلبات
RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
    && pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . /app/

# نسخ ملفات SSL
COPY ssl_certs/ /app/ssl_certs/

# إنشاء مجلدات قاعدة البيانات
RUN mkdir -p /app/instance /app/data

# نسخ قاعدة البيانات إذا كانت موجودة
RUN if [ -f /app/instance/ta9affi.db ]; then \
        cp /app/instance/ta9affi.db /app/ta9affi.db; \
        echo "✅ تم نسخ قاعدة البيانات الموجودة"; \
    else \
        echo "⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها"; \
    fi

# إنشاء سكريبت تهيئة قاعدة البيانات
RUN echo '#!/bin/bash\n\
echo "🗄️ تهيئة قاعدة البيانات..."\n\
mkdir -p /app/instance /app/logs /app/uploads\n\
if [ ! -f /app/ta9affi.db ]; then\n\
    echo "📊 إنشاء قاعدة بيانات جديدة..."\n\
    python init_database.py\n\
fi\n\
echo "✅ تم تهيئة قاعدة البيانات"\n\
' > /app/init_db.sh && chmod +x /app/init_db.sh

# تعيين الصلاحيات النهائية
RUN chown -R ta9affi:ta9affi /app \
    && chmod 664 /app/ta9affi.db 2>/dev/null || true \
    && chmod 664 /app/instance/ta9affi.db 2>/dev/null || true

# المنافذ المكشوفة
EXPOSE 8000


# فحص صحة الحاوية
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# نقطة الدخول
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "app:app"]









