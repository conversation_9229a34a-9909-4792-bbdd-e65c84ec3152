{% extends 'base.html' %}

{% block extra_css %}
<style>
    .forgot-password-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .forgot-password-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 500px;
        width: 100%;
        padding: 40px;
        text-align: center;
    }

    .forgot-password-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 30px;
        color: white;
        font-size: 2rem;
    }

    .btn-primary-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 50px;
        padding: 12px 30px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }

    .form-floating > .form-control {
        border-radius: 15px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-floating > .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .back-to-login {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .back-to-login:hover {
        color: #764ba2;
        text-decoration: underline;
    }

    .alert {
        border-radius: 15px;
        border: none;
        margin-bottom: 25px;
    }

    .alert-info {
        background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
        color: #495057;
        border-left: 4px solid #667eea;
    }

    .alert-success {
        background: linear-gradient(135deg, #28a74520 0%, #20c99720 100%);
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-danger {
        background: linear-gradient(135deg, #dc354520 0%, #c8254520 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .reset-link-container {
        border: 2px dashed #667eea;
        border-radius: 10px;
        background: #f8f9ff;
    }

    .reset-link-container input {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        background: white;
        border: 1px solid #dee2e6;
    }

    .reset-link-container .btn {
        border-radius: 20px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block title %}استرجاع كلمة المرور - Ta9affi{% endblock %}

{% block content %}
<div class="forgot-password-container">
    <div class="forgot-password-card">
        <div class="forgot-password-icon">
            <i class="fas fa-key"></i>
        </div>
        
        <h2 class="mb-4 text-dark">استرجاع كلمة المرور</h2>
        
        <p class="text-muted mb-4">
            أدخل البريد الإلكتروني المرتبط بحسابك وسنرسل لك رابط إعادة تعيين كلمة المرور
        </p>

        <!-- عرض الرسائل -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' or category == 'danger' else 'info-circle' if category == 'info' else 'check-circle' }} me-2"></i>
                        {% if category == 'info' and 'http://127.0.0.1:5000/reset-password/' in message %}
                            <!-- رابط إعادة تعيين كلمة المرور -->
                            <div class="mb-3">
                                <strong>رابط إعادة تعيين كلمة المرور:</strong>
                            </div>
                            <div class="reset-link-container p-3 bg-light rounded">
                                {% set reset_url = message.split(': ')[1] %}
                                <div class="mb-2">
                                    <input type="text" class="form-control" id="resetLink" value="{{ reset_url }}" readonly>
                                </div>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="copyResetLink()">
                                        <i class="fas fa-copy me-1"></i>
                                        نسخ الرابط
                                    </button>
                                    <a href="{{ reset_url }}" class="btn btn-sm btn-success" target="_blank">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        فتح الرابط
                                    </a>
                                </div>
                            </div>
                        {% else %}
                            {{ message }}
                        {% endif %}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" id="forgotPasswordForm">
            <div class="form-floating mb-4">
                <input type="email" class="form-control" id="email" name="email" 
                       placeholder="البريد الإلكتروني" required>
                <label for="email">
                    <i class="fas fa-envelope me-2"></i>
                    البريد الإلكتروني
                </label>
            </div>

            <div class="d-grid mb-4">
                <button type="submit" class="btn btn-primary-gradient btn-lg" id="submitBtn">
                    <span id="submitBtnText">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال رابط الاسترجاع
                    </span>
                    <span id="submitBtnLoading" class="d-none">
                        <span class="spinner-border spinner-border-sm me-2"></span>
                        جاري الإرسال...
                    </span>
                </button>
            </div>
        </form>

        <div class="text-center">
            <a href="{{ url_for('login') }}" class="back-to-login">
                <i class="fas fa-arrow-right me-2"></i>
                العودة لتسجيل الدخول
            </a>
        </div>

        <div class="mt-4">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> 
                <ul class="mb-0 mt-2 text-start">
                    <li>تأكد من إدخال البريد الإلكتروني الصحيح المرتبط بحسابك</li>
                    <li>قد يستغرق وصول البريد بضع دقائق</li>
                    <li>تحقق من مجلد الرسائل غير المرغوب فيها (Spam)</li>
                    <li>رابط الاسترجاع صالح لمدة ساعة واحدة فقط</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('forgotPasswordForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitBtnText = document.getElementById('submitBtnText');
    const submitBtnLoading = document.getElementById('submitBtnLoading');

    form.addEventListener('submit', function(e) {
        // إظهار حالة التحميل
        submitBtn.disabled = true;
        submitBtnText.classList.add('d-none');
        submitBtnLoading.classList.remove('d-none');
    });

    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert && alert.parentNode) {
                alert.classList.remove('show');
                setTimeout(function() {
                    if (alert && alert.parentNode) {
                        alert.remove();
                    }
                }, 150);
            }
        }, 5000);
    });
});

// دالة نسخ رابط إعادة التعيين
function copyResetLink() {
    const resetLinkInput = document.getElementById('resetLink');
    if (resetLinkInput) {
        resetLinkInput.select();
        resetLinkInput.setSelectionRange(0, 99999); // للهواتف المحمولة

        try {
            document.execCommand('copy');

            // تغيير نص الزر مؤقتاً
            const copyBtn = event.target.closest('button');
            const originalHTML = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check me-1"></i>تم النسخ!';
            copyBtn.classList.remove('btn-primary');
            copyBtn.classList.add('btn-success');

            setTimeout(() => {
                copyBtn.innerHTML = originalHTML;
                copyBtn.classList.remove('btn-success');
                copyBtn.classList.add('btn-primary');
            }, 2000);

        } catch (err) {
            console.error('فشل في نسخ الرابط:', err);
            alert('فشل في نسخ الرابط. يرجى نسخه يدوياً.');
        }
    }
}
</script>
{% endblock %}
