#!/usr/bin/env python3
"""
سكريبت اختبار إعدادات Chargily في البيئات المختلفة
"""

import os
import sys
from dotenv import load_dotenv

def test_environment_config(env_name, env_file=None):
    """اختبار إعدادات بيئة محددة"""
    
    print(f"\n{'='*60}")
    print(f"🧪 اختبار إعدادات البيئة: {env_name}")
    print(f"{'='*60}")
    
    # تحميل ملف البيئة إذا كان موجوداً
    if env_file and os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ تم تحميل إعدادات من: {env_file}")
    else:
        print(f"⚠️ ملف البيئة غير موجود: {env_file}")
    
    # تعيين متغيرات البيئة
    if env_name == 'development':
        os.environ['FLASK_ENV'] = 'development'
        if not os.environ.get('BASE_URL'):
            os.environ['BASE_URL'] = 'http://127.0.0.1:5000'
    elif env_name == 'production':
        os.environ['FLASK_ENV'] = 'production'
        os.environ['PRODUCTION_MODE'] = 'true'
        if not os.environ.get('BASE_URL'):
            os.environ['BASE_URL'] = 'http://ta9affi.com'
    
    # اختبار استيراد subscription_manager
    try:
        from subscription_manager import SubscriptionManager
        
        # إنشاء مثيل من SubscriptionManager
        manager = SubscriptionManager()
        
        print(f"🌍 البيئة المكتشفة: {manager.environment}")
        print(f"🔗 Base URL: {manager.base_url}")
        print(f"🔗 Webhook URL: {manager.webhook_url}")
        print(f"🔑 Public Key: {manager.chargily_public_key[:20]}...")
        print(f"🔐 Secret Key: {manager.chargily_secret_key[:20]}...")
        
        # اختبار URLs الدفع
        success_url, failure_url = manager.get_payment_urls()
        print(f"✅ Success URL: {success_url}")
        print(f"❌ Failure URL: {failure_url}")
        
        # التحقق من صحة URLs
        expected_base = 'http://127.0.0.1:5000' if env_name == 'development' else 'http://ta9affi.com'
        
        if manager.base_url == expected_base:
            print(f"✅ Base URL صحيح للبيئة {env_name}")
        else:
            print(f"❌ Base URL خطأ! متوقع: {expected_base}, فعلي: {manager.base_url}")
        
        if success_url == f"{expected_base}/payment/success":
            print(f"✅ Success URL صحيح")
        else:
            print(f"❌ Success URL خطأ!")
        
        if failure_url == f"{expected_base}/payment/failure":
            print(f"✅ Failure URL صحيح")
        else:
            print(f"❌ Failure URL خطأ!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البيئة {env_name}: {str(e)}")
        import traceback
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إعدادات Chargily في البيئات المختلفة")
    
    # حفظ متغيرات البيئة الأصلية
    original_env = dict(os.environ)
    
    success_count = 0
    total_tests = 2
    
    try:
        # اختبار البيئة المحلية
        os.environ.clear()
        os.environ.update(original_env)
        if test_environment_config('development', '.env.development'):
            success_count += 1
        
        # اختبار البيئة الإنتاجية
        os.environ.clear()
        os.environ.update(original_env)
        if test_environment_config('production', '.env.production.example'):
            success_count += 1
        
    finally:
        # استعادة متغيرات البيئة الأصلية
        os.environ.clear()
        os.environ.update(original_env)
    
    # عرض النتائج
    print(f"\n{'='*60}")
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests} نجح")
    
    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 الخطوات التالية:")
        print("1. للتطوير المحلي: python run_local_with_env.py")
        print("2. للإنتاج: تأكد من متغيرات البيئة في dokploy")
        print("3. اختبر عملية الدفع في كلا البيئتين")
    else:
        print("❌ بعض الاختبارات فشلت - راجع الأخطاء أعلاه")
        sys.exit(1)

if __name__ == '__main__':
    main()
