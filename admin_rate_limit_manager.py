#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير Rate Limiting للأدمن - واجهة إدارة متقدمة
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models_new import db, User
from rate_limit_settings import RateLimitSettings, RateLimitHistory, UserRateLimitOverride, update_rate_limiter_settings
import json
from datetime import datetime, timedelta

# إنشاء Blueprint
admin_rate_limit_bp = Blueprint('admin_rate_limit', __name__, url_prefix='/admin/rate-limit-manager')

def admin_required(f):
    """Decorator للتأكد من صلاحيات الأدمن"""
    from functools import wraps

    @wraps(f)
    def admin_decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('يجب تسجيل الدخول أولاً', 'error')
            return redirect(url_for('login'))

        # التحقق من دور الأدمن
        from models_new import Role
        if current_user.role != Role.ADMIN:
            flash('غير مصرح لك بالوصول لهذه الصفحة - صلاحيات أدمن مطلوبة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return admin_decorated_function

@admin_rate_limit_bp.route('/')
@login_required
@admin_required
def dashboard():
    """لوحة تحكم إدارة Rate Limiting"""
    
    # الحصول على الإعدادات الحالية
    current_settings = RateLimitSettings.get_current_settings()
    
    # الحصول على إحصائيات
    total_overrides = UserRateLimitOverride.query.filter_by(is_active=True).count()
    recent_changes = RateLimitHistory.query.order_by(RateLimitHistory.changed_at.desc()).limit(5).all()
    
    # الحصول على المستخدمين مع تخصيصات
    users_with_overrides = db.session.query(
        UserRateLimitOverride, User
    ).join(
        User, UserRateLimitOverride.user_id == User.id
    ).filter(
        UserRateLimitOverride.is_active == True
    ).all()
    
    return render_template('admin/rate_limit_manager.html',
                         current_settings=current_settings,
                         total_overrides=total_overrides,
                         recent_changes=recent_changes,
                         users_with_overrides=users_with_overrides)

@admin_rate_limit_bp.route('/update-global-settings', methods=['POST'])
@login_required
@admin_required
def update_global_settings():
    """تحديث الإعدادات العامة"""
    
    try:
        # الحصول على الإعدادات الحالية للسجل
        old_settings = RateLimitSettings.get_current_settings().to_dict()
        
        # الحصول على البيانات من النموذج
        add_limit = int(request.form.get('add_progress_limit', 10))
        add_window = int(request.form.get('add_progress_window_hours', 12))
        delete_limit = int(request.form.get('delete_progress_limit', 3))
        delete_window = int(request.form.get('delete_progress_window_hours', 12))
        notes = request.form.get('notes', '')
        
        # التحقق من صحة البيانات
        if add_limit < 1 or add_limit > 100:
            flash('حد إضافة التقدمات يجب أن يكون بين 1 و 100', 'error')
            return redirect(url_for('admin_rate_limit.dashboard'))
        
        if delete_limit < 1 or delete_limit > 50:
            flash('حد حذف التقدمات يجب أن يكون بين 1 و 50', 'error')
            return redirect(url_for('admin_rate_limit.dashboard'))
        
        if add_window < 1 or add_window > 168:  # أسبوع كحد أقصى
            flash('نافذة إضافة التقدمات يجب أن تكون بين 1 و 168 ساعة', 'error')
            return redirect(url_for('admin_rate_limit.dashboard'))
        
        if delete_window < 1 or delete_window > 168:
            flash('نافذة حذف التقدمات يجب أن تكون بين 1 و 168 ساعة', 'error')
            return redirect(url_for('admin_rate_limit.dashboard'))
        
        # تحديث الإعدادات
        new_settings = RateLimitSettings.update_settings(
            admin_id=current_user.id,
            add_progress_limit=add_limit,
            add_progress_window_hours=add_window,
            delete_progress_limit=delete_limit,
            delete_progress_window_hours=delete_window,
            notes=notes
        )
        
        # تسجيل التغيير في السجل
        RateLimitHistory.log_change(
            admin_id=current_user.id,
            old_settings=old_settings,
            new_settings=new_settings.to_dict(),
            reason=f"تحديث الإعدادات العامة بواسطة {current_user.username}",
            change_type='update'
        )
        
        # تحديث Rate Limiter
        update_rate_limiter_settings()
        
        flash(f'تم تحديث الإعدادات بنجاح: إضافة {add_limit}/{add_window}ساعة، حذف {delete_limit}/{delete_window}ساعة', 'success')
        
    except ValueError as e:
        flash('خطأ في البيانات المدخلة - تأكد من أن جميع القيم أرقام صحيحة', 'error')
    except Exception as e:
        flash(f'حدث خطأ أثناء تحديث الإعدادات: {str(e)}', 'error')
    
    return redirect(url_for('admin_rate_limit.dashboard'))

@admin_rate_limit_bp.route('/user-override')
@login_required
@admin_required
def user_override():
    """صفحة تخصيص حدود المستخدمين"""
    
    # الحصول على جميع المستخدمين
    users = User.query.filter_by(role='teacher').all()
    
    # الحصول على التخصيصات الحالية
    overrides = UserRateLimitOverride.query.filter_by(is_active=True).all()
    
    return render_template('admin/user_rate_limit_override.html',
                         users=users,
                         overrides=overrides)

@admin_rate_limit_bp.route('/set-user-override', methods=['POST'])
@login_required
@admin_required
def set_user_override():
    """تعيين تخصيص لمستخدم محدد"""
    
    try:
        user_id = int(request.form.get('user_id'))
        add_limit = request.form.get('add_progress_limit')
        add_window = request.form.get('add_progress_window_hours')
        delete_limit = request.form.get('delete_progress_limit')
        delete_window = request.form.get('delete_progress_window_hours')
        reason = request.form.get('reason', '')
        expires_days = request.form.get('expires_days')
        
        # التحقق من وجود المستخدم
        user = User.query.get(user_id)
        if not user:
            flash('المستخدم غير موجود', 'error')
            return redirect(url_for('admin_rate_limit.user_override'))
        
        # تحضير البيانات
        override_data = {
            'reason': reason
        }
        
        # إضافة الحدود إذا تم تحديدها
        if add_limit:
            add_limit = int(add_limit)
            if add_limit < 1 or add_limit > 100:
                flash('حد إضافة التقدمات يجب أن يكون بين 1 و 100', 'error')
                return redirect(url_for('admin_rate_limit.user_override'))
            override_data['add_progress_limit'] = add_limit
        
        if add_window:
            add_window = int(add_window)
            if add_window < 1 or add_window > 168:
                flash('نافذة إضافة التقدمات يجب أن تكون بين 1 و 168 ساعة', 'error')
                return redirect(url_for('admin_rate_limit.user_override'))
            override_data['add_progress_window_hours'] = add_window
        
        if delete_limit:
            delete_limit = int(delete_limit)
            if delete_limit < 1 or delete_limit > 50:
                flash('حد حذف التقدمات يجب أن يكون بين 1 و 50', 'error')
                return redirect(url_for('admin_rate_limit.user_override'))
            override_data['delete_progress_limit'] = delete_limit
        
        if delete_window:
            delete_window = int(delete_window)
            if delete_window < 1 or delete_window > 168:
                flash('نافذة حذف التقدمات يجب أن تكون بين 1 و 168 ساعة', 'error')
                return redirect(url_for('admin_rate_limit.user_override'))
            override_data['delete_progress_window_hours'] = delete_window
        
        # تاريخ انتهاء الصلاحية
        if expires_days:
            expires_days = int(expires_days)
            if expires_days > 0:
                override_data['expires_at'] = datetime.utcnow() + timedelta(days=expires_days)
        
        # تعيين التخصيص
        UserRateLimitOverride.set_user_override(
            admin_id=current_user.id,
            user_id=user_id,
            **override_data
        )
        
        flash(f'تم تعيين تخصيص للمستخدم {user.username} بنجاح', 'success')
        
    except ValueError as e:
        flash('خطأ في البيانات المدخلة - تأكد من أن جميع القيم أرقام صحيحة', 'error')
    except Exception as e:
        flash(f'حدث خطأ أثناء تعيين التخصيص: {str(e)}', 'error')
    
    return redirect(url_for('admin_rate_limit.user_override'))

@admin_rate_limit_bp.route('/remove-user-override/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def remove_user_override(user_id):
    """إزالة تخصيص المستخدم"""
    
    try:
        override = UserRateLimitOverride.query.filter_by(
            user_id=user_id, 
            is_active=True
        ).first()
        
        if override:
            override.is_active = False
            override.updated_by = current_user.id
            override.updated_at = datetime.utcnow()
            db.session.commit()
            
            user = User.query.get(user_id)
            flash(f'تم إزالة تخصيص المستخدم {user.username if user else user_id} بنجاح', 'success')
        else:
            flash('التخصيص غير موجود', 'error')
            
    except Exception as e:
        flash(f'حدث خطأ أثناء إزالة التخصيص: {str(e)}', 'error')
    
    return redirect(url_for('admin_rate_limit.user_override'))

@admin_rate_limit_bp.route('/history')
@login_required
@admin_required
def history():
    """سجل تغييرات Rate Limiting"""
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    history_records = RateLimitHistory.query.order_by(
        RateLimitHistory.changed_at.desc()
    ).paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    return render_template('admin/rate_limit_history.html',
                         history_records=history_records)

@admin_rate_limit_bp.route('/api/current-settings')
@login_required
@admin_required
def api_current_settings():
    """API للحصول على الإعدادات الحالية"""
    
    settings = RateLimitSettings.get_current_settings()
    return jsonify({
        'success': True,
        'settings': settings.to_dict()
    })

@admin_rate_limit_bp.route('/api/user-limits/<int:user_id>')
@login_required
@admin_required
def api_user_limits(user_id):
    """API للحصول على حدود مستخدم محدد"""
    
    try:
        from rate_limit_settings import get_user_rate_limits
        limits = get_user_rate_limits(user_id)
        
        # الحصول على التخصيص إن وجد
        override = UserRateLimitOverride.get_user_override(user_id)
        
        return jsonify({
            'success': True,
            'user_id': user_id,
            'limits': limits,
            'override': override.to_dict() if override else None
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_rate_limit_bp.route('/api/reset-all-limits', methods=['POST'])
@login_required
@admin_required
def api_reset_all_limits():
    """API لإعادة تعيين جميع حدود المستخدمين"""
    
    try:
        from rate_limiter import rate_limiter
        
        # إعادة تعيين جميع البيانات في Redis أو التخزين المحلي
        if hasattr(rate_limiter, 'redis_client') and rate_limiter.redis_client:
            # حذف جميع مفاتيح Rate Limiting من Redis
            keys = rate_limiter.redis_client.keys("rate_limit:*")
            if keys:
                rate_limiter.redis_client.delete(*keys)
        else:
            # مسح التخزين المحلي
            rate_limiter.local_storage.clear()
        
        # تسجيل العملية في السجل
        RateLimitHistory.log_change(
            admin_id=current_user.id,
            old_settings=None,
            new_settings=None,
            reason=f"إعادة تعيين جميع الحدود بواسطة {current_user.username}",
            change_type='reset_all'
        )
        
        return jsonify({
            'success': True,
            'message': 'تم إعادة تعيين جميع حدود المستخدمين بنجاح'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
