#!/bin/bash
# سكريبت لحل مشكلة 502 Bad Gateway في Ta9affi

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "🔧 بدء إصلاح مشكلة 502 Bad Gateway"
echo "=================================="

# 1. فحص الملفات المحدثة
log_info "📋 فحص الملفات المحدثة..."

if [ -f "docker-compose.prod.yml" ]; then
    log_success "✅ docker-compose.prod.yml موجود"
    
    # فحص المنفذ في الملف
    if grep -q "8000:8000" docker-compose.prod.yml; then
        log_success "✅ المنفذ 8000 محدث في docker-compose.prod.yml"
    else
        log_error "❌ المنفذ غير صحيح في docker-compose.prod.yml"
    fi
else
    log_error "❌ docker-compose.prod.yml غير موجود"
fi

if [ -f "dokploy.config.js" ]; then
    log_success "✅ dokploy.config.js موجود"
    
    # فحص المنفذ في الملف
    if grep -q "port: 8000" dokploy.config.js; then
        log_success "✅ المنفذ 8000 محدث في dokploy.config.js"
    else
        log_error "❌ المنفذ غير صحيح في dokploy.config.js"
    fi
else
    log_error "❌ dokploy.config.js غير موجود"
fi

if [ -f "nginx/ta9affi.conf" ]; then
    log_success "✅ nginx/ta9affi.conf موجود"
else
    log_warning "⚠️ nginx/ta9affi.conf غير موجود"
fi

# 2. فحص إعدادات Gunicorn
log_info "🔍 فحص إعدادات Gunicorn..."

if [ -f "gunicorn.conf.py" ]; then
    if grep -q "0.0.0.0:8000" gunicorn.conf.py; then
        log_success "✅ Gunicorn يربط على 0.0.0.0:8000"
    else
        log_error "❌ إعدادات Gunicorn غير صحيحة"
    fi
else
    log_error "❌ gunicorn.conf.py غير موجود"
fi

# 3. فحص Dockerfile
log_info "🐳 فحص Dockerfile..."

if [ -f "Dockerfile" ]; then
    if grep -q "EXPOSE 8000" Dockerfile; then
        log_success "✅ Dockerfile يعرض المنفذ 8000"
    else
        log_error "❌ Dockerfile لا يعرض المنفذ الصحيح"
    fi
    
    if grep -q "gunicorn.*8000" Dockerfile; then
        log_success "✅ Dockerfile يشغل Gunicorn على المنفذ 8000"
    else
        log_error "❌ أمر Gunicorn في Dockerfile غير صحيح"
    fi
else
    log_error "❌ Dockerfile غير موجود"
fi

# 4. إنشاء ملف تشخيص
log_info "📊 إنشاء ملف تشخيص..."

cat > diagnosis_502.md << EOF
# تشخيص مشكلة 502 Bad Gateway - Ta9affi

## المشكلة
\`\`\`
502 Bad Gateway
nginx/1.24.0 (Ubuntu)

nginx error logs:
connect() failed (111: Connection refused) while connecting to upstream, 
upstream: "http://127.0.0.1:8000/"
\`\`\`

## السبب
nginx يحاول الاتصال بـ 127.0.0.1:8000 لكن التطبيق لا يعمل على هذا المنفذ.

## الحلول المطبقة

### 1. تصحيح docker-compose.prod.yml
- ✅ تغيير المنفذ من 80:80 إلى 8000:8000
- ✅ تحديث healthcheck ليستخدم المنفذ 8000

### 2. تصحيح dokploy.config.js
- ✅ تغيير port من 80 إلى 8000

### 3. إنشاء nginx/ta9affi.conf
- ✅ إعدادات nginx محسنة للمنفذ 8000
- ✅ إعدادات SSL وأمان محسنة
- ✅ Rate limiting وتحسينات أداء

### 4. التحقق من Gunicorn
- ✅ gunicorn.conf.py يربط على 0.0.0.0:8000
- ✅ Dockerfile يعرض المنفذ 8000
- ✅ Health check endpoint موجود في app.py

## الخطوات التالية

### في dokploy:
1. إعادة deploy التطبيق
2. فحص container logs
3. التأكد من أن nginx يستخدم الإعدادات الجديدة

### أوامر التشخيص:
\`\`\`bash
# فحص containers
docker ps

# فحص logs التطبيق
docker logs ta9affi_app

# فحص المنفذ 8000
netstat -tlnp | grep 8000

# اختبار health check
curl http://127.0.0.1:8000/health
\`\`\`

## التوقعات
بعد التحديث، يجب أن:
- ✅ nginx يتصل بـ 127.0.0.1:8000 بنجاح
- ✅ Gunicorn يستجيب على المنفذ 8000
- ✅ الموقع يعمل بدون 502 error
EOF

log_success "✅ تم إنشاء ملف التشخيص: diagnosis_502.md"

# 5. إنشاء سكريبت اختبار
log_info "🧪 إنشاء سكريبت اختبار..."

cat > test_fix.sh << 'EOF'
#!/bin/bash
# اختبار إصلاح مشكلة 502

echo "🧪 اختبار إصلاح مشكلة 502 Bad Gateway"
echo "======================================="

# اختبار المنفذ 8000
echo "🔍 فحص المنفذ 8000..."
if netstat -tlnp | grep -q ":8000"; then
    echo "✅ المنفذ 8000 مفتوح"
else
    echo "❌ المنفذ 8000 غير مفتوح"
fi

# اختبار health check
echo "🏥 اختبار health check..."
if curl -f -s http://127.0.0.1:8000/health > /dev/null; then
    echo "✅ Health check يعمل"
    curl -s http://127.0.0.1:8000/health | jq .
else
    echo "❌ Health check لا يعمل"
fi

# اختبار الموقع الرئيسي
echo "🌐 اختبار الموقع الرئيسي..."
if curl -f -s http://127.0.0.1:8000/ > /dev/null; then
    echo "✅ الموقع الرئيسي يعمل"
else
    echo "❌ الموقع الرئيسي لا يعمل"
fi

echo "======================================="
echo "✅ انتهاء الاختبار"
EOF

chmod +x test_fix.sh
log_success "✅ تم إنشاء سكريبت الاختبار: test_fix.sh"

# 6. ملخص التغييرات
log_info "📋 ملخص التغييرات المطبقة:"
echo ""
echo "1. ✅ docker-compose.prod.yml - تصحيح المنفذ إلى 8000:8000"
echo "2. ✅ docker-compose.yml - تصحيح المنفذ إلى 8000:8000"
echo "3. ✅ dokploy.config.js - تصحيح port إلى 8000"
echo "4. ✅ nginx/ta9affi.conf - إعدادات nginx محسنة"
echo "5. ✅ diagnosis_502.md - ملف تشخيص شامل"
echo "6. ✅ test_fix.sh - سكريبت اختبار"
echo ""

log_success "🎉 تم تطبيق جميع الإصلاحات!"
echo ""
log_info "📤 الخطوة التالية: رفع الملفات إلى GitHub ثم إعادة deploy في dokploy"
echo ""
log_warning "⚠️ تذكير: بعد الـ deploy، قم بتشغيل test_fix.sh للتأكد من الإصلاح"
