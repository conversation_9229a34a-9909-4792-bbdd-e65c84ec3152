#!/usr/bin/env python3
"""
تفعيل اشتراك المستخدم splintergags بعد الدفع الناجح
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models_new import db, User, Payment, Subscription, SubscriptionPlan
from subscription_manager import subscription_manager
import json

def activate_subscription():
    """تفعيل اشتراك المستخدم splintergags"""
    with app.app_context():
        print("🔄 تفعيل اشتراك المستخدم splintergags")
        print("=" * 60)
        
        # الحصول على المستخدم
        user = User.query.filter_by(username='splintergags').first()
        if not user:
            print("❌ المستخدم غير موجود")
            return False
        
        print(f"👤 المستخدم: {user.username} (ID: {user.id})")
        print(f"📊 الحالة الحالية:")
        print(f"   - Subscription status: {user.subscription_status}")
        print(f"   - Has active subscription: {user.has_active_subscription}")
        print(f"   - Subscription type: {user.subscription_type}")
        
        # الحصول على آخر دفعة
        payment = Payment.query.filter_by(user_id=user.id).order_by(Payment.created_at.desc()).first()
        if not payment:
            print("❌ لا توجد مدفوعات للمستخدم")
            return False
        
        print(f"\n💳 آخر دفعة:")
        print(f"   - Payment ID: {payment.id}")
        print(f"   - Amount: {payment.amount} {payment.currency}")
        print(f"   - Status: {payment.status}")
        print(f"   - Created: {payment.created_at}")
        print(f"   - Chargily Checkout ID: {payment.chargily_checkout_id}")
        
        # تحديث حالة الدفع إلى paid
        if payment.status != 'paid':
            print(f"\n🔄 تحديث حالة الدفع إلى 'paid'...")
            payment.status = 'paid'
            payment.paid_at = datetime.utcnow()
            db.session.commit()
            print(f"✅ تم تحديث حالة الدفع")
        
        # محاكاة webhook data
        webhook_data = {
            'checkout_id': payment.chargily_checkout_id,
            'status': 'paid',
            'amount': payment.amount,
            'currency': payment.currency
        }
        
        print(f"\n🔄 معالجة webhook...")
        print(f"   - Checkout ID: {webhook_data['checkout_id']}")
        print(f"   - Status: {webhook_data['status']}")
        
        # معالجة webhook
        success = subscription_manager.process_payment_webhook(webhook_data)
        
        if success:
            print(f"✅ تم معالجة webhook بنجاح")
        else:
            print(f"❌ فشل في معالجة webhook")
            return False
        
        # التحقق من النتيجة
        user = User.query.filter_by(username='splintergags').first()  # إعادة تحميل
        subscription = user.current_subscription
        
        print(f"\n📊 الحالة بعد التفعيل:")
        print(f"   - Subscription status: {user.subscription_status}")
        print(f"   - Has active subscription: {user.has_active_subscription}")
        print(f"   - Subscription type: {user.subscription_type}")
        
        if subscription:
            print(f"\n📋 تفاصيل الاشتراك:")
            print(f"   - Subscription ID: {subscription.id}")
            print(f"   - Plan: {subscription.plan.name}")
            print(f"   - Start date: {subscription.start_date}")
            print(f"   - End date: {subscription.end_date}")
            print(f"   - Days remaining: {subscription.days_remaining}")
            print(f"   - Is active: {subscription.is_active}")
            print(f"   - Is free trial: {subscription.is_free_trial}")
            
            # تحديث Payment بـ subscription_id
            payment.subscription_id = subscription.id
            db.session.commit()
            print(f"   - Payment linked to subscription")
            
            return True
        else:
            print(f"❌ لم يتم إنشاء اشتراك")
            return False

def check_user_status():
    """فحص حالة المستخدم"""
    with app.app_context():
        print("\n🔍 فحص حالة المستخدم")
        print("=" * 60)
        
        user = User.query.filter_by(username='splintergags').first()
        if not user:
            print("❌ المستخدم غير موجود")
            return
        
        print(f"👤 المستخدم: {user.username}")
        print(f"📧 البريد الإلكتروني: {user.email}")
        print(f"👔 الدور: {user.role}")
        print(f"✅ نشط: {user.is_active}")
        print(f"📊 حالة الاشتراك: {user.subscription_status}")
        print(f"🔄 نوع الاشتراك: {user.subscription_type}")
        print(f"💼 لديه اشتراك نشط: {user.has_active_subscription}")
        
        if user.free_trial_end:
            print(f"🆓 انتهاء الفترة التجريبية: {user.free_trial_end}")
        
        # عرض جميع الاشتراكات
        subscriptions = Subscription.query.filter_by(user_id=user.id).all()
        print(f"\n📋 جميع الاشتراكات ({len(subscriptions)}):")
        
        for sub in subscriptions:
            print(f"   - ID: {sub.id}")
            print(f"     Plan: {sub.plan.name}")
            print(f"     Start: {sub.start_date}")
            print(f"     End: {sub.end_date}")
            print(f"     Active: {sub.is_active}")
            print(f"     Free trial: {sub.is_free_trial}")
            print(f"     Days remaining: {sub.days_remaining}")
            print()
        
        # عرض جميع المدفوعات
        payments = Payment.query.filter_by(user_id=user.id).all()
        print(f"💳 جميع المدفوعات ({len(payments)}):")
        
        for payment in payments:
            print(f"   - ID: {payment.id}")
            print(f"     Amount: {payment.amount} {payment.currency}")
            print(f"     Status: {payment.status}")
            print(f"     Created: {payment.created_at}")
            print(f"     Paid at: {payment.paid_at}")
            print(f"     Subscription ID: {payment.subscription_id}")
            print()

def main():
    """تشغيل التفعيل"""
    print("🚀 تفعيل اشتراك المستخدم splintergags")
    print("=" * 70)
    
    # فحص الحالة الحالية
    check_user_status()
    
    # تفعيل الاشتراك
    success = activate_subscription()
    
    # فحص الحالة بعد التفعيل
    if success:
        print("\n" + "=" * 70)
        print("🎉 تم تفعيل الاشتراك بنجاح!")
        check_user_status()
    else:
        print("\n" + "=" * 70)
        print("❌ فشل في تفعيل الاشتراك")

if __name__ == "__main__":
    main()
