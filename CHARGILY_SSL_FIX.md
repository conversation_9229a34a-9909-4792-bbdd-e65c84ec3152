# 🔧 إصلاح مشكلة Chargily SSL RecursionError

## 🚨 المشكلة
```
RecursionError: maximum recursion depth exceeded while calling a Python object
```

هذا الخطأ يحدث بسبب تضارب بين:
- **gevent monkey patching** في gunicorn
- **SSL context** في urllib3/requests  
- **Chargily API calls**

## ✅ الحلول المطبقة

### الحل 1: إصلاح SSL في subscription_manager.py
- إضافة معالجة SSL خاصة لبيئة gevent
- دالة `_safe_chargily_call` مع retry mechanism
- تعطيل SSL verification مؤقتاً
- انتظار متزايد بين المحاولات

### الحل 2: عميل Chargily مبسط
- **ملف**: `chargily_simple.py`
- عميل مخصص يتجنب مشاكل SSL
- معالجة أخطاء محسنة
- SSL context مخصص

## 🧪 اختبار الحلول

### اختبار الحل الأول:
```bash
# بعد إعادة النشر، راقب logs:
# يجب أن ترى:
✅ [SSL] تم إعداد SSL context للعمل مع gevent
🔄 [Chargily] محاولة 1/3 لـ create_product
✅ [Chargily] نجح create_product في المحاولة 1
```

### اختبار الحل الثاني:
```bash
# في terminal الحاوية:
cd /app
python chargily_simple.py
```

## 🔄 خطوات التشخيص

### 1. فحص Logs بعد إعادة النشر
ابحث عن هذه الرسائل في logs:
```
✅ [SSL] تم إعداد SSL context للعمل مع gevent
✅ [gevent] SSL تم patch بواسطة gevent
🔄 [SubscriptionManager] إنشاء منتج في Chargily...
🔄 [Chargily] محاولة 1/3 لـ create_product
```

### 2. إذا استمر RecursionError
```bash
# جرب العميل المبسط:
cd /app
python chargily_simple.py
```

### 3. فحص متغيرات البيئة
تأكد من وجود:
```
CHARGILY_PUBLIC_KEY=live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk
CHARGILY_SECRET_KEY=live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU
BASE_URL=http://ta9affi.com
```

## 🛠️ حلول إضافية

### الحل 3: تعديل gunicorn config
إذا استمرت المشكلة، يمكن تعديل `gunicorn.conf.py`:

```python
# إضافة هذا في بداية الملف:
import gevent.monkey
gevent.monkey.patch_all(ssl=False)  # تعطيل SSL patching

# أو استخدام worker مختلف:
worker_class = "sync"  # بدلاً من gevent
```

### الحل 4: استخدام العميل المبسط
إذا فشلت جميع الحلول، يمكن استبدال Chargily client:

```python
# في subscription_manager.py:
from chargily_simple import SimpleChargilyClient

# استبدال:
self.chargily = ChargilyClient(...)

# بـ:
self.chargily = SimpleChargilyClient(
    self.chargily_public_key,
    self.chargily_secret_key
)
```

## 📋 النتائج المتوقعة

### بعد الإصلاح الناجح:
```
🔄 [SubscriptionManager] إنشاء منتج في Chargily...
✅ [Chargily] نجح create_product في المحاولة 1
✅ [SubscriptionManager] تم إنشاء المنتج: prod_xxxxx
🔄 [SubscriptionManager] إنشاء سعر في Chargily...
✅ [Chargily] نجح create_price في المحاولة 1
✅ [SubscriptionManager] تم إنشاء السعر: price_xxxxx
🔄 [SubscriptionManager] إنشاء checkout في Chargily...
✅ [Chargily] نجح create_checkout في المحاولة 1
✅ [SubscriptionManager] تم إنشاء checkout: checkout_xxxxx
```

### في المتصفح:
- إعادة توجيه إلى صفحة Chargily
- عرض تفاصيل الدفع
- خيارات الدفع المتاحة

## 🚀 خطوات النشر

1. **إعادة النشر في dokploy**
2. **مراقبة logs** للتأكد من عدم وجود RecursionError
3. **اختبار عملية الدفع** من `/subscription/plans`
4. **التحقق من إعادة التوجيه** إلى Chargily

## 📞 الدعم الطارئ

إذا استمرت المشكلة:
1. **جرب العميل المبسط** `chargily_simple.py`
2. **غير worker class** في gunicorn إلى `sync`
3. **تواصل مع دعم Chargily** للتأكد من API keys
4. **فحص firewall** للتأكد من عدم حجب الاتصالات

---
**آخر تحديث**: 2025-01-16  
**الحالة**: ✅ حلول متعددة جاهزة للاختبار
