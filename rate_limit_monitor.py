#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة Rate Limiting لـ Ta9affi
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Blueprint, render_template, request, jsonify, session, flash, redirect, url_for
from flask_login import login_required, current_user
import redis
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء Blueprint
rate_limit_bp = Blueprint('rate_limit', __name__, url_prefix='/admin/rate-limits')

class RateLimitMonitor:
    """مراقب Rate Limiting"""
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client
    
    def get_all_user_limits(self) -> Dict:
        """الحصول على جميع حدود المستخدمين"""
        try:
            if not self.redis_client:
                return {}
            
            # البحث عن جميع مفاتيح Rate Limit
            pattern = "rate_limit:*"
            keys = self.redis_client.keys(pattern)
            
            user_limits = {}
            
            for key in keys:
                try:
                    key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                    parts = key_str.split(':')
                    
                    if len(parts) >= 3:
                        user_id = parts[1]
                        action = parts[2]
                        
                        # تجاهل مفاتيح IP
                        if user_id == 'ip':
                            continue
                        
                        data = self.redis_client.get(key)
                        if data:
                            limit_data = json.loads(data)
                            
                            if user_id not in user_limits:
                                user_limits[user_id] = {}
                            
                            user_limits[user_id][action] = limit_data
                            
                except Exception as e:
                    logger.error(f"خطأ في معالجة مفتاح {key}: {e}")
                    continue
            
            return user_limits
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على حدود المستخدمين: {e}")
            return {}
    
    def get_user_activity_stats(self, user_id: int, hours: int = 24) -> Dict:
        """إحصائيات نشاط المستخدم"""
        try:
            stats = {
                'add_progress': {'count': 0, 'remaining': 0, 'max': 10},
                'delete_progress': {'count': 0, 'remaining': 0, 'max': 3},
                'total_actions': 0,
                'last_activity': None
            }
            
            if not self.redis_client:
                return stats
            
            # فحص عمليات الإضافة
            add_key = f"rate_limit:{user_id}:add_progress"
            add_data = self.redis_client.get(add_key)
            if add_data:
                add_info = json.loads(add_data)
                stats['add_progress']['count'] = add_info.get('current_count', 0)
                stats['add_progress']['remaining'] = add_info.get('max_requests', 10) - add_info.get('current_count', 0)
                stats['add_progress']['max'] = add_info.get('max_requests', 10)
            
            # فحص عمليات الحذف
            delete_key = f"rate_limit:{user_id}:delete_progress"
            delete_data = self.redis_client.get(delete_key)
            if delete_data:
                delete_info = json.loads(delete_data)
                stats['delete_progress']['count'] = delete_info.get('current_count', 0)
                stats['delete_progress']['remaining'] = delete_info.get('max_requests', 3) - delete_info.get('current_count', 0)
                stats['delete_progress']['max'] = delete_info.get('max_requests', 3)
            
            stats['total_actions'] = stats['add_progress']['count'] + stats['delete_progress']['count']
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات المستخدم {user_id}: {e}")
            return stats
    
    def get_system_stats(self) -> Dict:
        """إحصائيات النظام العامة"""
        try:
            stats = {
                'total_users_with_limits': 0,
                'total_blocked_users': 0,
                'most_active_users': [],
                'action_distribution': {
                    'add_progress': 0,
                    'delete_progress': 0,
                    'login_attempts': 0,
                    'other': 0
                }
            }
            
            if not self.redis_client:
                return stats
            
            user_limits = self.get_all_user_limits()
            stats['total_users_with_limits'] = len(user_limits)
            
            user_activity = []
            
            for user_id, limits in user_limits.items():
                total_activity = 0
                is_blocked = False
                
                for action, limit_data in limits.items():
                    current_count = limit_data.get('current_count', 0)
                    max_requests = limit_data.get('max_requests', 0)
                    
                    total_activity += current_count
                    stats['action_distribution'][action] = stats['action_distribution'].get(action, 0) + current_count
                    
                    # فحص إذا كان المستخدم محظور
                    if current_count >= max_requests:
                        is_blocked = True
                
                if is_blocked:
                    stats['total_blocked_users'] += 1
                
                user_activity.append({
                    'user_id': user_id,
                    'total_activity': total_activity,
                    'is_blocked': is_blocked
                })
            
            # ترتيب المستخدمين الأكثر نشاطاً
            user_activity.sort(key=lambda x: x['total_activity'], reverse=True)
            stats['most_active_users'] = user_activity[:10]
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات النظام: {e}")
            return stats
    
    def reset_user_limits(self, user_id: int, action: str = None) -> bool:
        """إعادة تعيين حدود المستخدم"""
        try:
            if not self.redis_client:
                return False
            
            if action:
                key = f"rate_limit:{user_id}:{action}"
                self.redis_client.delete(key)
                logger.info(f"تم إعادة تعيين حد {action} للمستخدم {user_id}")
            else:
                # إعادة تعيين جميع الحدود
                pattern = f"rate_limit:{user_id}:*"
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
                logger.info(f"تم إعادة تعيين جميع حدود المستخدم {user_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين حدود المستخدم {user_id}: {e}")
            return False
    
    def get_blocked_users(self) -> List[Dict]:
        """الحصول على قائمة المستخدمين المحظورين"""
        try:
            blocked_users = []
            user_limits = self.get_all_user_limits()
            
            for user_id, limits in user_limits.items():
                user_blocked_actions = []
                
                for action, limit_data in limits.items():
                    current_count = limit_data.get('current_count', 0)
                    max_requests = limit_data.get('max_requests', 0)
                    
                    if current_count >= max_requests:
                        # حساب وقت إعادة التعيين
                        window_start = limit_data.get('window_start')
                        window_hours = limit_data.get('window_hours', 12)
                        
                        if window_start:
                            reset_time = datetime.fromisoformat(window_start) + timedelta(hours=window_hours)
                            time_until_reset = reset_time - datetime.now()
                            
                            user_blocked_actions.append({
                                'action': action,
                                'current_count': current_count,
                                'max_requests': max_requests,
                                'reset_time': reset_time.isoformat(),
                                'time_until_reset': str(time_until_reset) if time_until_reset.total_seconds() > 0 else 'منتهي'
                            })
                
                if user_blocked_actions:
                    blocked_users.append({
                        'user_id': user_id,
                        'blocked_actions': user_blocked_actions,
                        'total_blocked_actions': len(user_blocked_actions)
                    })
            
            return blocked_users
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على المستخدمين المحظورين: {e}")
            return []

# إنشاء مثيل المراقب
monitor = RateLimitMonitor()

def init_rate_limit_monitor(redis_client):
    """تهيئة مراقب Rate Limiting"""
    global monitor
    monitor = RateLimitMonitor(redis_client)

# Routes للإدارة

@rate_limit_bp.route('/')
@login_required
def dashboard():
    """لوحة تحكم Rate Limiting"""
    # فحص صلاحيات الإدارة
    if not current_user.is_admin:
        flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
        return redirect(url_for('main.dashboard'))
    
    # الحصول على الإحصائيات
    system_stats = monitor.get_system_stats()
    blocked_users = monitor.get_blocked_users()
    
    return render_template('admin/rate_limits_dashboard.html',
                         system_stats=system_stats,
                         blocked_users=blocked_users)

@rate_limit_bp.route('/user/<int:user_id>')
@login_required
def user_details(user_id):
    """تفاصيل Rate Limits لمستخدم محدد"""
    if not current_user.is_admin:
        flash('غير مصرح لك بالوصول لهذه الصفحة', 'error')
        return redirect(url_for('main.dashboard'))
    
    user_stats = monitor.get_user_activity_stats(user_id)
    
    return render_template('admin/user_rate_limits.html',
                         user_id=user_id,
                         user_stats=user_stats)

@rate_limit_bp.route('/reset', methods=['POST'])
@login_required
def reset_limits():
    """إعادة تعيين حدود المستخدم"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403
    
    user_id = request.form.get('user_id')
    action = request.form.get('action')  # اختياري
    
    if not user_id:
        return jsonify({'success': False, 'message': 'معرف المستخدم مطلوب'}), 400
    
    try:
        user_id = int(user_id)
        success = monitor.reset_user_limits(user_id, action)
        
        if success:
            message = f"تم إعادة تعيين حدود المستخدم {user_id}"
            if action:
                message += f" للعملية {action}"
            
            logger.info(f"المدير {current_user.id} قام بإعادة تعيين حدود المستخدم {user_id}")
            
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'message': 'فشل في إعادة التعيين'}), 500
            
    except ValueError:
        return jsonify({'success': False, 'message': 'معرف مستخدم غير صحيح'}), 400
    except Exception as e:
        logger.error(f"خطأ في إعادة تعيين الحدود: {e}")
        return jsonify({'success': False, 'message': 'خطأ في الخادم'}), 500

@rate_limit_bp.route('/api/stats')
@login_required
def api_stats():
    """API للحصول على الإحصائيات"""
    if not current_user.is_admin:
        return jsonify({'error': 'غير مصرح'}), 403
    
    stats = monitor.get_system_stats()
    blocked_users = monitor.get_blocked_users()
    
    return jsonify({
        'system_stats': stats,
        'blocked_users': blocked_users,
        'timestamp': datetime.now().isoformat()
    })

@rate_limit_bp.route('/api/user/<int:user_id>/status')
@login_required
def api_user_status(user_id):
    """API للحصول على حالة مستخدم محدد"""
    # السماح للمستخدم برؤية حالته الخاصة أو للمدير برؤية أي مستخدم
    if not (current_user.is_admin or current_user.id == user_id):
        return jsonify({'error': 'غير مصرح'}), 403
    
    user_stats = monitor.get_user_activity_stats(user_id)
    
    return jsonify({
        'user_id': user_id,
        'stats': user_stats,
        'timestamp': datetime.now().isoformat()
    })

# دالة مساعدة للاستخدام في القوالب
def get_user_rate_limit_status(user_id: int) -> Dict:
    """الحصول على حالة Rate Limits للمستخدم للاستخدام في القوالب"""
    return monitor.get_user_activity_stats(user_id)
