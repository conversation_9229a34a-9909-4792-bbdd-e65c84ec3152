# إعدادات Nginx لـ Ta9affi - محدث لحل مشكلة 502 Bad Gateway
server {
    listen 80;
    listen [::]:80;
    server_name ta9affi.com www.ta9affi.com;

    # إعادة توجيه HTTP إلى HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ta9affi.com www.ta9affi.com;

    # إعدادات SSL
    ssl_certificate /etc/letsencrypt/live/ta9affi.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ta9affi.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # إعدادات الأمان
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;

    # إعدادات التخزين المؤقت للملفات الثابتة
    location /static/ {
        alias /app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    location /uploads/ {
        alias /app/uploads/;
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
    }

    # إعدادات الـ favicon
    location /favicon.ico {
        alias /app/static/favicon.ico;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }

    # المسار الرئيسي للتطبيق
    location / {
        # تمرير الطلبات إلى Gunicorn على المنفذ 8000
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # إعدادات المهلة الزمنية
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # إعدادات Buffer
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        
        # إعدادات إضافية
        proxy_redirect off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # إعدادات الأمان الإضافية
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # منع الوصول للملفات الحساسة
    location ~* \.(env|log|ini|conf|bak|sql|py)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # إعدادات السجلات
    access_log /var/log/nginx/ta9affi_access.log;
    error_log /var/log/nginx/ta9affi_error.log;

    # إعدادات حجم الملفات المرفوعة
    client_max_body_size 50M;
    client_body_buffer_size 128k;
    client_header_buffer_size 3m;
    large_client_header_buffers 4 256k;
}

# إعدادات عامة لتحسين الأداء
upstream ta9affi_backend {
    server 127.0.0.1:8000;
    keepalive 32;
}

# إعدادات Rate Limiting
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

# تطبيق Rate Limiting على مسارات محددة
location /login {
    limit_req zone=login burst=3 nodelay;
    proxy_pass http://ta9affi_backend;
    # باقي إعدادات proxy...
}

location /api/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://ta9affi_backend;
    # باقي إعدادات proxy...
}
