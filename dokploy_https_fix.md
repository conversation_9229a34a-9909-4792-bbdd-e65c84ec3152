# حل مشكلة HTTPS في dokploy - تشخيص متقدم

## المشكلة المؤكدة:
```bash
curl -I https://ta9affi.com/health
# curl: (7) Failed to connect to ta9affi.com port 443: Connection refused

curl -I http://ta9affi.com/health
# HTTP/1.1 200 OK ✓
```

**التشخيص**: المنفذ 443 غير مفتوح أو nginx/proxy غير مُعد للـ HTTPS.

## 1. فحص حالة المنافذ على الخادم

```bash
# فحص المنافذ المفتوحة
netstat -tlnp | grep :443
netstat -tlnp | grep :80

# فحص nginx
systemctl status nginx
ps aux | grep nginx

# فحص dokploy containers
docker ps | grep nginx
docker ps | grep traefik
```

## 2. فحص إعدادات dokploy

### أ) فحص Traefik (dokploy يستخدم Traefik كـ reverse proxy):
```bash
# فحص حاويات dokploy
docker ps | grep traefik
docker logs traefik-container-name

# فحص إعدادات traefik
docker exec traefik-container cat /etc/traefik/traefik.yml
```

### ب) فحص شهادات Let's Encrypt:
```bash
# فحص مجلد الشهادات
ls -la /var/lib/docker/volumes/dokploy_traefik-certificates/
docker exec traefik-container ls -la /certificates/
```

## 3. الحلول المرحلية

### الحل 1: إعادة تشغيل Traefik
```bash
# في dokploy dashboard أو SSH
docker restart $(docker ps | grep traefik | awk '{print $1}')

# انتظر 30 ثانية ثم اختبر
curl -I https://ta9affi.com/health
```

### الحل 2: فحص وإصلاح إعدادات Domain في dokploy
```bash
# في dokploy dashboard:
# 1. اذهب إلى Applications → ta9affi
# 2. اذهب إلى Domains
# 3. احذف ta9affi.com
# 4. أضفه مرة أخرى بهذه الإعدادات:
#    Domain: ta9affi.com
#    Port: 8000 (مهم جداً!)
#    Path: /
#    HTTPS: ✓ مفعل
#    Certificate: Let's Encrypt
```

### الحل 3: فحص Firewall
```bash
# فحص iptables
iptables -L | grep 443
ufw status | grep 443

# فتح المنفذ 443 إذا كان مغلق
ufw allow 443
# أو
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

## 4. فحص DNS وشهادات

```bash
# فحص DNS
dig ta9affi.com A
nslookup ta9affi.com

# محاولة الاتصال المباشر بالخادم
curl -I https://*************/health
telnet ta9affi.com 443
```

## 5. الحل النهائي: إعادة تكوين كامل

إذا فشلت جميع الحلول:

### أ) إعادة تشغيل dokploy كاملاً:
```bash
cd /path/to/dokploy
docker-compose down
docker-compose up -d
```

### ب) إعادة إنشاء التطبيق:
1. احذف التطبيق من dokploy
2. أعد إنشاءه مع التأكد من:
   - Port: 8000
   - HTTPS مفعل
   - Domain صحيح

## 6. اختبار سريع

```bash
# اختبار الاتصال
curl -v https://ta9affi.com/health
curl -k https://ta9affi.com/health  # تجاهل شهادة SSL

# فحص المنفذ
nmap -p 443 ta9affi.com
```
