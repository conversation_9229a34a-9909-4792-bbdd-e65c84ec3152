# دليل تثبيت متطلبات Ta9affi للبيئة المحلية

## 1. متطلبات Python الأساسية

### إطار العمل Flask
```bash
pip install flask>=2.3.0
pip install flask-sqlalchemy>=3.0.0
pip install flask-login>=0.6.0
pip install flask-wtf>=1.1.0
pip install werkzeug>=2.3.0
```

### معالجة البيانات
```bash
pip install pandas>=1.5.0
pip install openpyxl>=3.1.0
```

### نظام الدفع
```bash
pip install chargily-pay
```

### جدولة المهام
```bash
pip install schedule>=1.2.0
```

## 2. قواعد البيانات

### SQLite (مدمج في Python - لا يحتاج تثبيت)
- يستخدم افتراضياً في بيئة التطوير

### MySQL (اختياري)
```bash
pip install pymysql>=1.0.0
```

### PostgreSQL (اختياري)
```bash
pip install psycopg2-binary>=2.9.0
```

## 3. Redis والتخزين المؤقت

### تثبيت Redis Server
**على Windows:**
```bash
# تحميل Redis من الموقع الرسمي أو استخدام WSL
# أو استخدام Docker
docker run -d -p 6379:6379 redis:7-alpine
```

**على Linux/macOS:**
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis
```

### مكتبات Python للـ Redis
```bash
pip install redis>=4.5.0
pip install flask-session>=0.5.0
```

## 4. الأمان والحماية
```bash
pip install flask-limiter>=3.3.0
pip install flask-talisman>=1.1.0
```

## 5. خادم الإنتاج
```bash
pip install gunicorn>=20.1.0
pip install gevent>=22.10.0
```

## 6. إدارة قاعدة البيانات
```bash
pip install flask-migrate>=4.0.0
```

## 7. أدوات إضافية
```bash
pip install python-dotenv>=1.0.0  # لقراءة ملفات .env
pip install requests>=2.28.0      # للطلبات HTTP
```

## 8. متطلبات النظام (System Dependencies)

### على Windows:
```bash
# Visual Studio Build Tools أو Visual Studio Community
# Python 3.11 أو أحدث
# Git
```

### على Linux:
```bash
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    gcc \
    g++ \
    libpq-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    libffi-dev \
    libssl-dev \
    curl \
    wget \
    python3-dev \
    python3-pip
```

### على macOS:
```bash
# Xcode Command Line Tools
xcode-select --install

# Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Dependencies
brew install postgresql libpq
```

## 9. تثبيت جميع المتطلبات دفعة واحدة

### الطريقة الأسهل:
```bash
pip install -r requirements.txt
```

### أو تثبيت يدوي:
```bash
pip install flask>=2.3.0 flask-sqlalchemy>=3.0.0 flask-login>=0.6.0 flask-wtf>=1.1.0 werkzeug>=2.3.0 pandas>=1.5.0 openpyxl>=3.1.0 chargily-pay schedule>=1.2.0 pymysql>=1.0.0 psycopg2-binary>=2.9.0 redis>=4.5.0 flask-session>=0.5.0 flask-limiter>=3.3.0 flask-talisman>=1.1.0 gunicorn>=20.1.0 gevent>=22.10.0 flask-migrate>=4.0.0 python-dotenv>=1.0.0 requests>=2.28.0
```

## 10. متغيرات البيئة المطلوبة

إنشاء ملف `.env` في جذر المشروع:
```env
# إعدادات Flask
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///instance/ta9affi.db

# إعدادات Redis
REDIS_URL=redis://localhost:6379/0

# إعدادات Chargily
CHARGILY_PUBLIC_KEY=your-public-key
CHARGILY_SECRET_KEY=your-secret-key
BASE_URL=http://127.0.0.1:5000
CHARGILY_WEBHOOK_URL=http://127.0.0.1:5000/chargily-webhook

# إعدادات السجلات
LOG_LEVEL=DEBUG
LOG_FILE=logs/ta9affi.log
```

## 11. خطوات التشغيل

### 1. تحضير البيئة:
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate
# على Linux/macOS:
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. تشغيل Redis:
```bash
# تشغيل Redis في terminal منفصل
redis-server

# أو باستخدام Docker
docker run -d -p 6379:6379 redis:7-alpine
```

### 3. تهيئة قاعدة البيانات:
```bash
python init_database.py
```

### 4. تشغيل التطبيق:
```bash
# للتطوير
python run.py

# أو
python app.py

# للإنتاج المحلي
gunicorn --config gunicorn.conf.py app:app
```

## 12. اختبار التثبيت

### فحص المتطلبات:
```bash
python -c "
import flask, flask_sqlalchemy, flask_login, pandas, openpyxl, redis, requests
print('✅ جميع المكتبات الأساسية مثبتة بنجاح')
"
```

### فحص Redis:
```bash
redis-cli ping
# يجب أن يرجع: PONG
```

### فحص التطبيق:
```bash
curl http://127.0.0.1:5000
# يجب أن يرجع صفحة HTML
```

## 13. استكشاف الأخطاء الشائعة

### مشكلة psycopg2:
```bash
# إذا فشل تثبيت psycopg2
pip install psycopg2-binary
```

### مشكلة gevent على Windows:
```bash
# تثبيت Visual Studio Build Tools أولاً
pip install gevent --no-cache-dir
```

### مشكلة Redis:
```bash
# التأكد من تشغيل Redis
redis-cli ping

# إعادة تشغيل Redis
sudo systemctl restart redis  # Linux
brew services restart redis   # macOS
```

## 14. الحد الأدنى للمتطلبات

للتشغيل الأساسي فقط:
```bash
pip install flask flask-sqlalchemy flask-login pandas openpyxl redis python-dotenv
```

هذا سيكفي لتشغيل التطبيق بالوظائف الأساسية.
