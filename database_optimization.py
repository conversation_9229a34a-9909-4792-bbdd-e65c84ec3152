#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسين قاعدة البيانات وإضافة الفهارس المحسنة
لتحسين الأداء مع آلاف المستخدمين المتزامنين
"""

from sqlalchemy import text, Index
from flask import current_app
from models_new import db
import logging

class DatabaseOptimizer:
    """مُحسن قاعدة البيانات"""
    
    def __init__(self, app=None):
        self.app = app
        self.optimization_log = []
    
    def log_message(self, message, level="INFO"):
        """تسجيل رسائل التحسين"""
        self.optimization_log.append(f"[{level}] {message}")
        if level == "ERROR":
            logging.error(message)
        elif level == "WARNING":
            logging.warning(message)
        else:
            logging.info(message)
        print(f"[{level}] {message}")
    
    def create_indexes(self):
        """إنشاء الفهارس المحسنة"""
        self.log_message("🔧 بدء إنشاء الفهارس المحسنة...")
        
        indexes_created = 0
        
        try:
            with db.engine.connect() as conn:
                # ===== فهارس جدول المستخدمين =====
                
                # فهرس للبحث بالاسم (يدعم LIKE)
                try:
                    conn.execute(text("""
                        CREATE INDEX IF NOT EXISTS idx_user_username_search 
                        ON "user" USING gin(username gin_trgm_ops)
                    """))
                    indexes_created += 1
                    self.log_message("✅ تم إنشاء فهرس البحث بالاسم")
                except Exception as e:
                    # إذا فشل GIN، استخدم B-tree عادي
                    conn.execute(text("""
                        CREATE INDEX IF NOT EXISTS idx_user_username_btree 
                        ON "user" (username)
                    """))
                    indexes_created += 1
                    self.log_message("✅ تم إنشاء فهرس B-tree للاسم")
                
                # فهرس للبحث برقم الهاتف
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_user_phone_number 
                    ON "user" (phone_number) WHERE phone_number IS NOT NULL
                """))
                indexes_created += 1
                
                # فهرس مركب للدور والحالة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_user_role_active 
                    ON "user" (role, _is_active)
                """))
                indexes_created += 1
                
                # فهرس للولاية والدور
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_user_wilaya_role 
                    ON "user" (wilaya_code, role) WHERE wilaya_code IS NOT NULL
                """))
                indexes_created += 1
                
                # فهرس لآخر تسجيل دخول
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_user_last_login 
                    ON "user" (last_login) WHERE last_login IS NOT NULL
                """))
                indexes_created += 1
                
                # فهرس لتاريخ الإنشاء
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_user_created_at 
                    ON "user" (created_at)
                """))
                indexes_created += 1
                
                # ===== فهارس جدول البيانات التعليمية =====
                
                # فهرس مركب محسن للاستعلامات الشائعة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_level_data_entry_optimized 
                    ON level_data_entry (database_id, entry_type, is_active, parent_id)
                """))
                indexes_created += 1
                
                # فهرس للبحث بالاسم في البيانات التعليمية
                try:
                    conn.execute(text("""
                        CREATE INDEX IF NOT EXISTS idx_level_data_entry_name_search 
                        ON level_data_entry USING gin(name gin_trgm_ops)
                    """))
                    indexes_created += 1
                    self.log_message("✅ تم إنشاء فهرس البحث في البيانات التعليمية")
                except Exception as e:
                    conn.execute(text("""
                        CREATE INDEX IF NOT EXISTS idx_level_data_entry_name_btree 
                        ON level_data_entry (name)
                    """))
                    indexes_created += 1
                
                # فهرس للترتيب
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_level_data_entry_order 
                    ON level_data_entry (database_id, entry_type, order_num, is_active)
                """))
                indexes_created += 1
                
                # ===== فهارس جدول التقدم =====
                
                # فهرس مركب للمستخدم والتاريخ
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_progress_entry_user_date 
                    ON progress_entry (user_id, date DESC)
                """))
                indexes_created += 1
                
                # فهرس للمستوى والمادة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_progress_entry_level_subject 
                    ON progress_entry (level_id, subject_id) 
                    WHERE level_id IS NOT NULL AND subject_id IS NOT NULL
                """))
                indexes_created += 1
                
                # فهرس للحالة والتاريخ
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_progress_entry_status_date 
                    ON progress_entry (status, date DESC)
                """))
                indexes_created += 1
                
                # ===== فهارس جدول الجداول الدراسية =====
                
                # فهرس للمستخدم والمستوى
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_schedule_user_level 
                    ON schedule (user_id, level_id)
                """))
                indexes_created += 1
                
                # ===== فهارس جدول الإشعارات =====
                
                # فهرس للمستقبل وحالة القراءة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_admin_inspector_notification_receiver_read 
                    ON admin_inspector_notification (receiver_id, is_read, created_at DESC)
                """))
                indexes_created += 1
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_inspector_teacher_notification_receiver_read 
                    ON inspector_teacher_notification (receiver_id, is_read, created_at DESC)
                """))
                indexes_created += 1
                
                # فهرس للإشعارات العامة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_general_notification_target 
                    ON general_notification (target_type, target_role, created_at DESC)
                """))
                indexes_created += 1
                
                # فهرس لقراءة الإشعارات العامة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_general_notification_read_user_notification 
                    ON general_notification_read (user_id, notification_id)
                """))
                indexes_created += 1
                
                # ===== فهارس جدول الاشتراكات =====
                
                # فهرس للمستخدم والحالة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_subscription_user_active 
                    ON subscription (user_id, is_active, end_date DESC)
                """))
                indexes_created += 1
                
                # فهرس لتاريخ الانتهاء
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_subscription_end_date 
                    ON subscription (end_date) WHERE is_active = true
                """))
                indexes_created += 1
                
                # ===== فهارس جدول المدفوعات =====
                
                # فهرس للمستخدم والحالة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_payment_user_status 
                    ON payment (user_id, status, created_at DESC)
                """))
                indexes_created += 1
                
                # فهرس للحالة والتاريخ
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_payment_status_date 
                    ON payment (status, created_at DESC)
                """))
                indexes_created += 1
                
                # ===== فهارس جدول الجلسات =====
                
                # فهرس للمستخدم والحالة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_user_session_user_active 
                    ON user_session (user_id, is_active, last_activity DESC)
                """))
                indexes_created += 1
                
                # فهرس لآخر نشاط
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_user_session_last_activity 
                    ON user_session (last_activity DESC) WHERE is_active = true
                """))
                indexes_created += 1
                
                # ===== فهارس جدول الأخبار =====
                
                # فهرس للحالة والتاريخ
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_news_update_active_date 
                    ON news_update (is_active, created_at DESC)
                """))
                indexes_created += 1
                
                # فهرس لتاريخ الانتهاء
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_news_update_end_date 
                    ON news_update (end_date) WHERE end_date IS NOT NULL
                """))
                indexes_created += 1
                
                # ===== فهارس جدول العلاقات =====
                
                # فهرس لجدول العلاقة بين المفتشين والأساتذة
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_inspector_teacher_inspector 
                    ON inspector_teacher (inspector_id)
                """))
                indexes_created += 1
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_inspector_teacher_teacher 
                    ON inspector_teacher (teacher_id)
                """))
                indexes_created += 1
                
                # تأكيد التغييرات
                conn.commit()
                
                self.log_message(f"✅ تم إنشاء {indexes_created} فهرس بنجاح", "SUCCESS")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في إنشاء الفهارس: {str(e)}", "ERROR")
            raise
    
    def enable_query_optimization(self):
        """تفعيل تحسينات الاستعلامات"""
        self.log_message("🔧 تفعيل تحسينات الاستعلامات...")
        
        try:
            with db.engine.connect() as conn:
                # تفعيل إحصائيات الاستعلامات
                conn.execute(text("SET track_activities = on"))
                conn.execute(text("SET track_counts = on"))
                conn.execute(text("SET track_functions = all"))
                
                # تحسين إعدادات الذاكرة
                conn.execute(text("SET work_mem = '16MB'"))
                conn.execute(text("SET maintenance_work_mem = '256MB'"))
                
                # تحسين إعدادات الاستعلامات
                conn.execute(text("SET enable_hashjoin = on"))
                conn.execute(text("SET enable_mergejoin = on"))
                conn.execute(text("SET enable_nestloop = on"))
                
                # تحسين إعدادات الفهرسة
                conn.execute(text("SET random_page_cost = 1.1"))
                conn.execute(text("SET seq_page_cost = 1.0"))
                
                self.log_message("✅ تم تفعيل تحسينات الاستعلامات")
                
        except Exception as e:
            self.log_message(f"⚠️ تحذير: لم يتم تطبيق بعض التحسينات: {str(e)}", "WARNING")
    
    def analyze_tables(self):
        """تحليل الجداول لتحديث الإحصائيات"""
        self.log_message("📊 تحليل الجداول وتحديث الإحصائيات...")
        
        tables = [
            'user', 'level_data_entry', 'progress_entry', 'schedule',
            'admin_inspector_notification', 'inspector_teacher_notification',
            'general_notification', 'general_notification_read',
            'subscription', 'payment', 'user_session', 'news_update',
            'inspector_teacher', 'level_database', 'educational_level'
        ]
        
        try:
            with db.engine.connect() as conn:
                for table in tables:
                    try:
                        conn.execute(text(f"ANALYZE {table}"))
                        self.log_message(f"✅ تم تحليل جدول {table}")
                    except Exception as e:
                        self.log_message(f"⚠️ تحذير: فشل تحليل جدول {table}: {str(e)}", "WARNING")
                
                self.log_message("✅ تم الانتهاء من تحليل الجداول")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحليل الجداول: {str(e)}", "ERROR")
    
    def check_query_performance(self):
        """فحص أداء الاستعلامات الشائعة"""
        self.log_message("🔍 فحص أداء الاستعلامات الشائعة...")
        
        test_queries = [
            # استعلام البحث في المستخدمين
            """
            EXPLAIN ANALYZE 
            SELECT * FROM "user" 
            WHERE username ILIKE '%test%' 
            LIMIT 20
            """,
            
            # استعلام البيانات التعليمية
            """
            EXPLAIN ANALYZE 
            SELECT * FROM level_data_entry 
            WHERE database_id = 1 AND entry_type = 'subject' AND is_active = true
            """,
            
            # استعلام التقدم
            """
            EXPLAIN ANALYZE 
            SELECT * FROM progress_entry 
            WHERE user_id = 1 
            ORDER BY date DESC 
            LIMIT 10
            """,
            
            # استعلام الإشعارات
            """
            EXPLAIN ANALYZE 
            SELECT * FROM general_notification 
            WHERE target_type = 'all' 
            ORDER BY created_at DESC 
            LIMIT 10
            """
        ]
        
        try:
            with db.engine.connect() as conn:
                for i, query in enumerate(test_queries, 1):
                    try:
                        result = conn.execute(text(query))
                        execution_plan = result.fetchall()
                        
                        # البحث عن وقت التنفيذ في خطة التنفيذ
                        execution_time = "غير معروف"
                        for row in execution_plan:
                            if "Execution Time:" in str(row[0]):
                                execution_time = str(row[0]).split("Execution Time:")[1].strip()
                                break
                        
                        self.log_message(f"✅ استعلام {i}: وقت التنفيذ = {execution_time}")
                        
                    except Exception as e:
                        self.log_message(f"⚠️ فشل اختبار الاستعلام {i}: {str(e)}", "WARNING")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في فحص الأداء: {str(e)}", "ERROR")
    
    def optimize_database(self):
        """تشغيل جميع تحسينات قاعدة البيانات"""
        self.log_message("🚀 بدء تحسين قاعدة البيانات الشامل...")
        
        try:
            # إنشاء الفهارس
            self.create_indexes()
            
            # تفعيل تحسينات الاستعلامات
            self.enable_query_optimization()
            
            # تحليل الجداول
            self.analyze_tables()
            
            # فحص الأداء
            self.check_query_performance()
            
            self.log_message("🎉 تم الانتهاء من تحسين قاعدة البيانات بنجاح!", "SUCCESS")
            
            return True
            
        except Exception as e:
            self.log_message(f"❌ فشل في تحسين قاعدة البيانات: {str(e)}", "ERROR")
            return False
    
    def get_optimization_report(self):
        """الحصول على تقرير التحسين"""
        return "\n".join(self.optimization_log)

def main():
    """الدالة الرئيسية للتحسين"""
    from app_postgresql import app
    
    with app.app_context():
        optimizer = DatabaseOptimizer(app)
        
        print("🔧 بدء تحسين قاعدة البيانات Ta9affi...")
        print("=" * 60)
        
        success = optimizer.optimize_database()
        
        print("\n" + "=" * 60)
        print("📋 تقرير التحسين:")
        print("=" * 60)
        print(optimizer.get_optimization_report())
        
        if success:
            print("\n✅ تم تحسين قاعدة البيانات بنجاح!")
            print("🚀 التطبيق جاهز الآن للأداء العالي مع آلاف المستخدمين")
        else:
            print("\n❌ حدثت مشاكل أثناء التحسين")
            print("📝 راجع السجلات أعلاه للتفاصيل")

if __name__ == "__main__":
    main()
