# متغيرات البيئة للتطوير المحلي - Ta9affi

# إعدادات البيئة
FLASK_ENV=development
DEBUG=True

# إعدادات قاعدة البيانات (SQLite للتطوير)
SQLALCHEMY_DATABASE_URI=sqlite:///ta9affi.db

# الأمان (مفتاح بسيط للتطوير)
SECRET_KEY=dev-secret-key-ta9affi-2024

# إعدادات URLs للتطوير المحلي
BASE_URL=http://127.0.0.1:5000
CHARGILY_WEBHOOK_URL=http://127.0.0.1:5000/chargily-webhook

# Chargily (نفس المفاتيح الحقيقية لأن Chargily يدعم البيئة المحلية)
CHARGILY_PUBLIC_KEY=live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk
CHARGILY_SECRET_KEY=live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU

# Redis (اختياري للتطوير)
REDIS_URL=redis://localhost:6379/0

# إعدادات التسجيل
LOG_LEVEL=DEBUG
LOG_FILE=logs/ta9affi_dev.log

# إعدادات Rate Limiting (مخففة للتطوير)
RATELIMIT_DEFAULT=10000 per hour

# إعدادات أخرى للتطوير
WTF_CSRF_ENABLED=False
SESSION_COOKIE_SECURE=False
TESTING=False

# ملاحظات:
# 1. هذا الملف للتطوير المحلي فقط
# 2. لا تستخدم هذه الإعدادات في الإنتاج
# 3. تأكد من أن المنفذ 5000 متاح
# 4. يمكنك تغيير BASE_URL إذا كنت تستخدم منفذ مختلف
