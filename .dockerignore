# ملفات لا تحتاج في Docker
.git
.gitignore
README.md
Dockerfile
.dockerignore
docker-compose*.yml

# ملفات Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# ملفات IDE
.vscode
.idea
*.swp
*.swo

# ملفات النظام
.DS_Store
Thumbs.db
desktop.ini

# ملفات التطوير
test_*
*_test.py
debug_*
diagnose_*
fix_*
safe_*
update_*

# ملفات مؤقتة
*.tmp
*.temp
*.log
ta9affi_backup_*.db

# مجلدات التطوير
venv
env
node_modules
