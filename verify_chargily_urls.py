#!/usr/bin/env python3
"""
التحقق من تحديث جميع روابط Chargily
"""

import os
import re

def check_file_for_urls(file_path, expected_domain="ta9affi.com"):
    """فحص ملف للبحث عن روابط Chargily"""
    if not os.path.exists(file_path):
        return f"❌ الملف غير موجود: {file_path}"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن روابط Chargily
        patterns = [
            r'success_url\s*=\s*["\']([^"\']+)["\']',
            r'failure_url\s*=\s*["\']([^"\']+)["\']',
            r'webhook_url\s*=\s*["\']([^"\']+)["\']',
            r'CHARGILY_WEBHOOK_URL\s*=\s*["\']([^"\']+)["\']',
            r'CHARGILY_WEBHOOK_URL:\s*["\']([^"\']+)["\']',
        ]
        
        results = []
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if 'chargily' in match.lower() or 'payment' in match.lower():
                    if expected_domain in match:
                        results.append(f"✅ {match}")
                    else:
                        results.append(f"❌ {match}")
        
        return results if results else ["ℹ️ لا توجد روابط Chargily"]
        
    except Exception as e:
        return [f"❌ خطأ في قراءة الملف: {str(e)}"]

def main():
    """الدالة الرئيسية للفحص"""
    print("🔍 فحص تحديث روابط Chargily في جميع الملفات...")
    print("=" * 60)
    
    # قائمة الملفات للفحص
    files_to_check = [
        'app.py',
        'subscription_manager.py',
        'config_production.py',
        'dokploy.config.js',
        '.env.example',
        '.env.dokploy'
    ]
    
    all_good = True
    
    for file_path in files_to_check:
        print(f"\n📄 فحص ملف: {file_path}")
        print("-" * 40)
        
        results = check_file_for_urls(file_path)
        
        if isinstance(results, str):
            print(results)
            if "❌" in results:
                all_good = False
        else:
            for result in results:
                print(result)
                if "❌" in result:
                    all_good = False
    
    print("\n" + "=" * 60)
    
    if all_good:
        print("✅ جميع روابط Chargily محدثة بشكل صحيح!")
        print("\n🎯 الروابط المتوقعة:")
        print("- Success: http://ta9affi.com/payment/success")
        print("- Failure: http://ta9affi.com/payment/failure")
        print("- Webhook: http://ta9affi.com/chargily-webhook")
        
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل: bash commit_changes.sh")
        print("2. Deploy في Dokploy")
        print("3. تحديث webhook في Chargily dashboard")
        
    else:
        print("❌ يوجد روابط تحتاج تحديث!")
        print("يرجى مراجعة الملفات المذكورة أعلاه")

if __name__ == "__main__":
    main()
