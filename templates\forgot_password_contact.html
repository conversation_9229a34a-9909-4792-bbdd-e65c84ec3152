{% extends "base.html" %}

{% block title %}نسيت كلمة المرور - Ta9affi{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="card shadow-lg border-0 rounded-lg mt-5">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="font-weight-light my-2">
                        <i class="fas fa-key me-2"></i>
                        استرجاع كلمة المرور
                    </h3>
                </div>
                <div class="card-body p-5">
                    <!-- رسالة توضيحية -->
                    <div class="alert alert-info border-0 shadow-sm mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle fa-2x text-info me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-2">تنبيه مهم</h5>
                                <p class="mb-0">
                                    لاسترجاع كلمة المرور، يرجى التواصل مع إدارة المستخدمين عبر إحدى الطرق التالية:
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- خيارات التواصل -->
                    <div class="row g-3">
                        <!-- WhatsApp -->
                        <div class="col-12">
                            <div class="contact-option">
                                <a href="https://wa.me/213540809687" target="_blank" rel="noopener noreferrer" 
                                   class="btn btn-success btn-lg w-100 d-flex align-items-center justify-content-center py-3 contact-btn">
                                    <i class="fab fa-whatsapp fa-2x me-3"></i>
                                    <div class="text-start">
                                        <div class="fw-bold">التواصل عبر WhatsApp</div>
                                        <small class="opacity-75">الطريقة الأسرع للحصول على المساعدة</small>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Messenger -->
                        <div class="col-12">
                            <div class="contact-option">
                                <a href="https://m.me/113844974919183" target="_blank" rel="noopener noreferrer" 
                                   class="btn btn-primary btn-lg w-100 d-flex align-items-center justify-content-center py-3 contact-btn">
                                    <i class="fab fa-facebook-messenger fa-2x me-3"></i>
                                    <div class="text-start">
                                        <div class="fw-bold">التواصل عبر Messenger</div>
                                        <small class="opacity-75">تواصل مباشر عبر Facebook Messenger</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="mt-4">
                        <div class="alert alert-warning border-0 shadow-sm">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-exclamation-triangle text-warning me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-2">معلومات مهمة:</h6>
                                    <ul class="mb-0 small">
                                        <li>يرجى تقديم اسم المستخدم أو البريد الإلكتروني المسجل</li>
                                        <li>قد تحتاج إلى تقديم معلومات إضافية للتحقق من الهوية</li>
                                        <li>سيتم إرسال كلمة المرور الجديدة خلال 24 ساعة</li>
                                        <li>أوقات الاستجابة: من 8 صباحاً إلى 8 مساءً</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- زر العودة -->
                    <div class="text-center mt-4">
                        <a href="{{ url_for('login') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة إلى تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .contact-btn {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .contact-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .contact-option {
        margin-bottom: 1rem;
    }

    .btn-success {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #128C7E 0%, #25D366 100%);
    }

    .btn-primary {
        background: linear-gradient(135deg, #0084FF 0%, #0066CC 100%);
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #0066CC 0%, #0084FF 100%);
    }

    .alert {
        border-radius: 15px;
    }

    .card {
        border-radius: 20px;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .fab {
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    /* تأثيرات الحركة */
    .contact-btn {
        position: relative;
        overflow: hidden;
    }

    .contact-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .contact-btn:hover::before {
        left: 100%;
    }

    /* تجاوب الأجهزة */
    @media (max-width: 768px) {
        .card-body {
            padding: 2rem !important;
        }
        
        .contact-btn {
            padding: 1rem !important;
        }
        
        .fab {
            font-size: 1.5rem !important;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير النقر على الأزرار
        const contactBtns = document.querySelectorAll('.contact-btn');
        
        contactBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                // إنشاء تأثير الموجة
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    });
</script>

<style>
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
</style>
{% endblock %}
