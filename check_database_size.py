#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص حجم قاعدة البيانات
تستخدم لتشخيص مشاكل الذاكرة في عرض البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models_new import LevelDatabase, LevelDataEntry, EducationalLevel

def check_database_sizes():
    """فحص أحجام قواعد البيانات المختلفة"""
    
    with app.app_context():
        print("=" * 60)
        print("تقرير أحجام قواعد البيانات")
        print("=" * 60)
        
        # الحصول على جميع قواعد البيانات
        databases = LevelDatabase.query.all()
        
        total_entries = 0
        
        for database in databases:
            print(f"\n📊 قاعدة بيانات: {database.name}")
            print(f"   المستوى: {database.level.name if database.level else 'غير محدد'}")
            print(f"   الحالة: {'نشطة' if database.is_active else 'معطلة'}")
            print("-" * 40)
            
            # إحصائيات تفصيلية
            subjects_count = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='subject').count()
            domains_count = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='domain').count()
            materials_count = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='material').count()
            competencies_count = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='competency').count()
            
            # العناصر النشطة
            subjects_active = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='subject', is_active=True).count()
            domains_active = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='domain', is_active=True).count()
            materials_active = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='material', is_active=True).count()
            competencies_active = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='competency', is_active=True).count()
            
            db_total = subjects_count + domains_count + materials_count + competencies_count
            db_active_total = subjects_active + domains_active + materials_active + competencies_active
            
            print(f"   📚 المواد الدراسية: {subjects_count} (نشطة: {subjects_active})")
            print(f"   🎯 الميادين/الأنشطة: {domains_count} (نشطة: {domains_active})")
            print(f"   📖 الموارد المعرفية: {materials_count} (نشطة: {materials_active})")
            print(f"   ✅ الكفاءات المستهدفة: {competencies_count} (نشطة: {competencies_active})")
            print(f"   📊 المجموع: {db_total} (نشطة: {db_active_total})")
            
            # تحذيرات الأداء
            if competencies_count > 1000:
                print(f"   ⚠️  تحذير: عدد كبير من الكفاءات ({competencies_count}) قد يسبب مشاكل في الأداء")
            if materials_count > 500:
                print(f"   ⚠️  تحذير: عدد كبير من الموارد المعرفية ({materials_count}) قد يسبب مشاكل في الأداء")
            if db_total > 2000:
                print(f"   🚨 تحذير: إجمالي البيانات كبير جداً ({db_total}) - يُنصح بالتحسين")
            
            total_entries += db_total
        
        print("\n" + "=" * 60)
        print(f"📈 إجمالي البيانات في جميع قواعد البيانات: {total_entries}")
        
        if total_entries > 10000:
            print("🚨 تحذير: إجمالي البيانات كبير جداً - يُنصح بتطبيق تحسينات الأداء")
        elif total_entries > 5000:
            print("⚠️  تنبيه: إجمالي البيانات متوسط - قد تحتاج لتحسينات")
        else:
            print("✅ إجمالي البيانات ضمن الحدود المقبولة")
        
        print("=" * 60)

def check_memory_usage():
    """فحص استخدام الذاكرة التقديري"""
    
    with app.app_context():
        print("\n" + "=" * 60)
        print("تقدير استخدام الذاكرة")
        print("=" * 60)
        
        # تقدير تقريبي لحجم كل عنصر في الذاكرة
        avg_entry_size = 1024  # 1KB تقريباً لكل عنصر (تقدير محافظ)
        
        databases = LevelDatabase.query.all()
        
        for database in databases:
            total_entries = LevelDataEntry.query.filter_by(database_id=database.id).count()
            estimated_memory = (total_entries * avg_entry_size) / (1024 * 1024)  # MB
            
            print(f"📊 {database.name}:")
            print(f"   العناصر: {total_entries}")
            print(f"   الذاكرة المقدرة: {estimated_memory:.2f} MB")
            
            if estimated_memory > 50:
                print(f"   🚨 خطر: استخدام ذاكرة عالي جداً!")
            elif estimated_memory > 20:
                print(f"   ⚠️  تحذير: استخدام ذاكرة مرتفع")
            else:
                print(f"   ✅ استخدام ذاكرة مقبول")

def main():
    """الدالة الرئيسية"""
    try:
        check_database_sizes()
        check_memory_usage()
        
        print("\n" + "=" * 60)
        print("💡 نصائح لتحسين الأداء:")
        print("   1. استخدام التصفح (Pagination) لعرض البيانات")
        print("   2. تحميل البيانات بشكل تدريجي عبر AJAX")
        print("   3. إضافة فهارس للجداول")
        print("   4. عرض العناصر النشطة فقط بشكل افتراضي")
        print("   5. استخدام التخزين المؤقت (Caching)")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
