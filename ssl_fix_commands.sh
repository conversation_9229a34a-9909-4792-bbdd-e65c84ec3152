#!/bin/bash
# 🔧 إصلاح SSL لـ ta9affi.com باستخدام certbot

echo "🔧 بدء إصلاح SSL لـ ta9affi.com..."

# 1. تحديث النظام
echo "📦 تحديث النظام..."
apt update && apt upgrade -y

# 2. تثبيت certbot
echo "📦 تثبيت certbot..."
apt install certbot python3-certbot-nginx -y

# 3. إيقاف nginx مؤقتاً (إذا كان يعمل)
echo "⏸️ إيقاف nginx مؤقتاً..."
systemctl stop nginx 2>/dev/null || echo "nginx غير مثبت أو متوقف بالفعل"

# 4. فحص البورت 80 و 443
echo "🔍 فحص البورتات..."
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# 5. إنشاء شهادة SSL جديدة
echo "🔐 إنشاء شهادة SSL لـ ta9affi.com..."
certbot certonly --standalone \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d ta9affi.com \
  -d www.ta9affi.com

# 6. فحص الشهادة
echo "✅ فحص الشهادة المنشأة..."
certbot certificates

# 7. إعداد التجديد التلقائي
echo "🔄 إعداد التجديد التلقائي..."
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -

# 8. فحص تكوين dokploy
echo "🔍 فحص تكوين dokploy..."
docker ps | grep dokploy
docker logs dokploy 2>&1 | tail -20

# 9. إعادة تشغيل dokploy
echo "🔄 إعادة تشغيل dokploy..."
docker restart dokploy

echo "✅ تم الانتهاء من إصلاح SSL!"
echo "📋 الخطوات التالية:"
echo "1. انتظر دقيقتين لإعادة تشغيل dokploy"
echo "2. اذهب إلى dokploy dashboard"
echo "3. أعد نشر التطبيق"
echo "4. اختبر https://ta9affi.com"
