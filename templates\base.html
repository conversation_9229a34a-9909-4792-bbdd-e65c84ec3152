<!DOCTYPE html>
<html lang="ar" dir="rtl">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}Ta9affi - نظام إدارة البرنامج السنوي للتدريس{% endblock %}</title>

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="{{ url_for('static', filename='img/favicon.png') }}">
        <link rel="shortcut icon" type="image/png" href="{{ url_for('static', filename='img/favicon.png') }}">

        <!-- Bootstrap RTL CSS -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Custom CSS -->
        <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
        <link rel="stylesheet" href="{{ url_for('static', filename='css/hover-effects.css') }}">

        {% block styles %}{% endblock %}
        {% block extra_css %}{% endblock %}
    </head>

    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-graduation-cap animated-icon pulse-icon me-2"></i>
                    Ta9affi
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('index') }}">
                                <i class="fas fa-home animated-icon me-1"></i>
                                الرئيسية
                            </a>
                        </li>
                        {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            {% if current_user.role == 'teacher' %}
                            <a class="nav-link" href="{{ url_for('teacher_dashboard') }}">
                                <i class="fas fa-tachometer-alt animated-icon me-1"></i>
                                لوحة التحكم
                            </a>
                            {% else %}
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt animated-icon me-1"></i>
                                لوحة التحكم
                            </a>
                            {% endif %}
                        </li>
                        {% if current_user.role == 'teacher' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teaching_program') }}">
                                <i class="fas fa-plus-circle animated-icon me-1"></i>
                                إضافة تقدّم
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link position-relative" href="{{ url_for('view_notifications') }}">
                                <i class="fas fa-bell animated-icon me-1"></i>
                                الإشعارات
                                <span id="notification-badge"
                                    class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                                    style="display: none;">
                                    0
                                </span>
                            </a>
                        </li>
                        {% if current_user.role in ['admin', 'inspector'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('send_notification') }}">
                                <i class="fas fa-paper-plane animated-icon me-1"></i>
                                إرسال إشعار
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.role == 'teacher' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teaching_program') }}">
                                <i class="fas fa-calendar-check animated-icon me-1"></i>
                                البرنامج السنوي
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.role == 'teacher' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_schedule') }}">
                                <i class="fas fa-table animated-icon me-1"></i>
                                جدول التدريس
                            </a>
                        </li>
                        {% elif current_user.role == 'inspector' %}
                        {% elif current_user.role == 'admin' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_databases') }}">
                                <i class="fas fa-database"></i> قواعد البيانات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_news') }}">
                                <i class="fas fa-newspaper"></i> إدارة الأخبار
                            </a>
                        </li>
                        {% endif %}
                        {% endif %}
                    </ul>
                    <ul class="navbar-nav">
                        {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user animated-icon pulse-icon"></i> {{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('profile') }}">
                                        <i class="fas fa-user-circle me-2"></i>
                                        الملف الشخصي
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('logout') }}">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">
                                <i class="fas fa-sign-in-alt animated-icon me-1"></i>
                                تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('register') }}">
                                <i class="fas fa-user-plus animated-icon me-1"></i>
                                طلب حساب
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Flash Messages -->
        <div class="container mt-3">
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}
            {% endwith %}
        </div>

        <!-- Main Content -->
        <main class="container mt-4">
            {% block content %}{% endblock %}
        </main>

        <!-- Footer -->
        <footer class="bg-light text-center text-lg-start mt-5">
            <div class="container p-4">
                <div class="row">
                    <div class="col-lg-6 col-md-12 mb-4 mb-md-0">
                        <h5 class="text-uppercase">Ta9affi</h5>
                        <p>
                            نظام إدارة البرنامج السنوي للتدريس في التعليم الإبتدائي في الجزائر
                        </p>
                    </div>
                    <div class="col-lg-6 col-md-12 mb-4 mb-md-0">
                        <h5 class="text-uppercase">روابط مفيدة</h5>
                        <ul class="list-unstyled mb-0">
                            <li>
                                <a href="https://discord.gg/mVNcbqUm" class="text-dark" target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-discord me-1"></i>
                                    الاقتراحات
                                </a>
                            </li>
                            <li>
                                <a href="https://www.youtube.com/playlist?list=PLCmTo5tEfX3XE7GwVrLwwLLhb4gH7-Wxr" class="text-dark" target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-youtube me-1"></i>
                                    الشروحات
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('terms_and_privacy') }}" class="text-dark">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    شروط الاستخدام وسياسة الخصوصية
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('contact') }}" class="text-dark">
                                    <i class="fas fa-envelope me-1"></i>
                                    اتصل بنا
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="text-center p-3" style="background-color: rgba(0, 0, 0, 0.05);">
                © 2025 Ta9affi - جميع الحقوق محفوظة
                <br>Developed By SPLIN
            </div>
        </footer>

        <!-- Bootstrap JS Bundle with Popper -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

        <!-- jQuery -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <!-- Custom JS -->
        <script src="{{ url_for('static', filename='js/main.js') }}"></script>

        <!-- تحديث عداد الإشعارات -->
        <script>
            function updateNotificationBadge() {
                {% if current_user.is_authenticated %}
                fetch('/api/unread_notifications_count')
                    .then(response => response.json())
                    .then(data => {
                        const badge = document.getElementById('notification-badge');
                        if (badge) {
                            if (data.count > 0) {
                                badge.textContent = data.count;
                                badge.style.display = 'inline-block';
                            } else {
                                badge.style.display = 'none';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error updating notification badge:', error);
                    });
                {% endif %}
            }

            // تحديث العداد عند تحميل الصفحة
            document.addEventListener('DOMContentLoaded', function () {
                updateNotificationBadge();

                // تحديث العداد كل 30 ثانية
                setInterval(updateNotificationBadge, 30000);
            });
        </script>

        {% block extra_js %}{% endblock %}
    </body>

</html>