#!/usr/bin/env python3
"""
سكريبت تهيئة قاعدة البيانات للإنتاج - Ta9affi
"""

import os
import sys
from pathlib import Path

def setup_database():
    """إعداد قاعدة البيانات للإنتاج"""
    
    print("🗄️ تهيئة قاعدة البيانات للإنتاج...")
    
    # تعيين متغيرات البيئة للإنتاج
    os.environ['FLASK_ENV'] = 'production'
    os.environ['PRODUCTION_MODE'] = 'true'
    
    try:
        # استيراد التطبيق والنماذج
        from app import app, db
        from models_new import User, Role, SubscriptionPlan
        from subscription_manager import subscription_manager
        
        print("✅ تم استيراد التطبيق والنماذج")
        
        with app.app_context():
            # إنشاء جميع الجداول
            print("🔄 إنشاء جداول قاعدة البيانات...")
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")
            
            # التحقق من وجود المستخدم الإداري
            admin_user = User.query.filter_by(role=Role.ADMIN).first()
            if not admin_user:
                print("🔄 إنشاء مستخدم إداري...")
                from werkzeug.security import generate_password_hash
                
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role=Role.ADMIN,
                    is_verified=True
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء مستخدم إداري")
            else:
                print("✅ المستخدم الإداري موجود")
            
            # تهيئة باقات الاشتراك
            print("🔄 تهيئة باقات الاشتراك...")
            try:
                subscription_manager.initialize_subscription_plans()
                print("✅ تم تهيئة باقات الاشتراك")
            except Exception as e:
                print(f"⚠️ تحذير في تهيئة باقات الاشتراك: {str(e)}")
            
            # عرض إحصائيات قاعدة البيانات
            user_count = User.query.count()
            plan_count = SubscriptionPlan.query.count()
            
            print(f"\n📊 إحصائيات قاعدة البيانات:")
            print(f"   - المستخدمين: {user_count}")
            print(f"   - باقات الاشتراك: {plan_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}")
        import traceback
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

def check_database_file():
    """التحقق من ملف قاعدة البيانات"""
    
    print("🔍 فحص ملف قاعدة البيانات...")
    
    # مسارات محتملة لقاعدة البيانات
    possible_paths = [
        '/app/ta9affi.db',
        '/app/instance/ta9affi.db',
        'ta9affi.db',
        'instance/ta9affi.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✅ وُجد ملف قاعدة البيانات: {path} ({size} bytes)")
            
            # التحقق من صلاحيات الكتابة
            if os.access(path, os.W_OK):
                print(f"✅ صلاحيات الكتابة متاحة")
            else:
                print(f"❌ صلاحيات الكتابة غير متاحة")
                
            return path
        else:
            print(f"❌ لم يوجد: {path}")
    
    print("⚠️ لم يتم العثور على ملف قاعدة البيانات")
    return None

def create_database_directory():
    """إنشاء مجلد قاعدة البيانات"""
    
    print("📁 إنشاء مجلدات قاعدة البيانات...")
    
    directories = ['/app', '/app/instance']
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✅ مجلد: {directory}")
        except Exception as e:
            print(f"❌ فشل في إنشاء {directory}: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 تهيئة قاعدة البيانات Ta9affi للإنتاج")
    print("=" * 50)
    
    # إنشاء المجلدات المطلوبة
    create_database_directory()
    
    # فحص ملف قاعدة البيانات الحالي
    db_path = check_database_file()
    
    # تهيئة قاعدة البيانات
    if setup_database():
        print("\n🎉 تم تهيئة قاعدة البيانات بنجاح!")
        print("\n📋 معلومات تسجيل الدخول:")
        print("   - المستخدم: admin")
        print("   - كلمة المرور: admin123")
        print("   - الرابط: http://ta9affi.com/login")
    else:
        print("\n❌ فشل في تهيئة قاعدة البيانات")
        sys.exit(1)

if __name__ == '__main__':
    main()
