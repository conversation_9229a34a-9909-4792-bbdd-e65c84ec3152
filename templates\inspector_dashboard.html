{% extends 'base.html' %}

{% block title %}لوحة تحكم المفتش{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي مع معلومات المفتش -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-tie me-2 text-primary"></i>
                لوحة تحكم المفتش
            </h1>
            <p class="text-muted mb-0">
                <i class="fas fa-user me-1"></i>
                مرحباً {{ current_user.username }}
                <span class="mx-2">|</span>
                <i class="fas fa-calendar me-1"></i>
                اليوم - {{ current_date }}
            </p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                <i class="fas fa-user-plus me-1"></i>
                إضافة أستاذ
            </button>
            <button class="btn btn-outline-info" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث
            </button>
        </div>
    </div>

    <!-- البطاقات الإحصائية المحدثة -->
    <div class="row mb-4">
        <!-- إجمالي الأساتذة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                الأساتذة تحت الإشراف
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ teachers|length }}</div>
                            <div class="text-xs text-muted">
                                {% if available_teachers|length > 0 %}
                                    <i class="fas fa-plus-circle text-success me-1"></i>
                                    {{ available_teachers|length }} متاح للإضافة
                                {% else %}
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    جميع الأساتذة مُعيَّنون
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نسبة الإنجاز الإجمالية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                نسبة الإنجاز الإجمالية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_completion_rate|default(0)|round|int }}%</div>
                            <div class="progress progress-sm mr-2">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ overall_completion_rate|default(0) }}%" 
                                     aria-valuenow="{{ overall_completion_rate|default(0) }}" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الدروس المنجزة اليوم -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                الدروس المنجزة اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_completed_lessons|default(0) }}</div>
                            <div class="text-xs text-muted">
                                <i class="fas fa-calendar-day me-1"></i>
                                {{ current_date }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الدروس قيد التنفيذ -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الدروس قيد التنفيذ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_in_progress_lessons|default(0) }}</div>
                            <div class="text-xs text-muted">
                                <i class="fas fa-users me-1"></i>
                                جميع الأساتذة تحت الإشراف
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأساتذة تحت الإشراف - محدث ومطور -->
    <div class="card shadow-lg mb-4 border-0">
        <div class="card-header py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="icon-circle bg-white bg-opacity-20 me-3">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 text-white font-weight-bold">
                            الأساتذة تحت الإشراف
                        </h5>
                        <small class="text-white-50">
                            إجمالي {{ teachers|length }} أستاذ |
                            نشط {{ teachers|selectattr('is_active')|list|length }} |
                            معدل الإنجاز {{ overall_completion_rate|round(1) }}%
                        </small>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i>
                            تصفية
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="filterTeachers('all')">جميع الأساتذة</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTeachers('active')">النشطون فقط</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTeachers('high-progress')">تقدم عالي (+80%)</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTeachers('low-progress')">تقدم منخفض (-40%)</a></li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة أستاذ
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" onclick="exportTeachersData()">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" onclick="refreshTeachersTable()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            {% if teachers %}
                <!-- شريط البحث والإحصائيات السريعة -->
                <div class="p-4 border-bottom bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text bg-white border-end-0">
                                    <i class="fas fa-search text-muted"></i>
                                </span>
                                <input type="text" class="form-control border-start-0" id="teacherSearch"
                                       placeholder="البحث عن أستاذ (الاسم، البريد الإلكتروني...)"
                                       onkeyup="searchTeachers()">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end gap-3">
                                <div class="text-center">
                                    <div class="h5 mb-0 text-success">{{ teachers|selectattr('is_active')|list|length }}</div>
                                    <small class="text-muted">نشط</small>
                                </div>
                                <div class="text-center">
                                    <div class="h5 mb-0 text-primary">{{ overall_completion_rate|round(1) }}%</div>
                                    <small class="text-muted">متوسط الإنجاز</small>
                                </div>
                                <div class="text-center">
                                    <div class="h5 mb-0 text-info">{{ progress_stats.completed_materials }}</div>
                                    <small class="text-muted">مواد مكتملة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="teachersTable">
                        <thead style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-bottom: 2px solid #dee2e6;">
                            <tr>
                                <th class="border-0 py-3" style="color: #495057; font-weight: 600;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        الأستاذ
                                        <i class="fas fa-sort ms-2 text-muted cursor-pointer" onclick="sortTable(0)"></i>
                                    </div>
                                </th>
                                <th class="border-0 py-3" style="color: #495057; font-weight: 600;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-envelope me-2 text-info"></i>
                                        معلومات الاتصال
                                    </div>
                                </th>
                                <th class="border-0 py-3" style="color: #495057; font-weight: 600;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-chart-line me-2 text-success"></i>
                                        نسبة الإنجاز
                                        <i class="fas fa-sort ms-2 text-muted cursor-pointer" onclick="sortTable(2)"></i>
                                    </div>
                                </th>
                                <th class="border-0 py-3" style="color: #495057; font-weight: 600;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-tasks me-2 text-warning"></i>
                                        إحصائيات التقدم
                                    </div>
                                </th>
                                <th class="border-0 py-3" style="color: #495057; font-weight: 600;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-clock me-2 text-secondary"></i>
                                        آخر نشاط
                                        <i class="fas fa-sort ms-2 text-muted cursor-pointer" onclick="sortTable(4)"></i>
                                    </div>
                                </th>
                                <th class="border-0 py-3 text-center" style="color: #495057; font-weight: 600;">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <i class="fas fa-cogs me-2 text-danger"></i>
                                        الإجراءات
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for teacher in teachers %}
                            <tr class="teacher-row border-0" data-teacher-id="{{ teacher.id }}"
                                data-teacher-name="{{ teacher.username }}"
                                data-completion-rate="{{ teacher_progress[teacher.id]['completion_rate']|default(0) if teacher_progress and teacher.id in teacher_progress else 0 }}"
                                data-status="{{ 'active' if teacher.is_active else 'inactive' }}"
                                style="transition: all 0.3s ease; cursor: pointer;">
                                <td class="py-4">
                                    <div class="d-flex align-items-center">
                                        <div class="position-relative me-3">
                                            <div class="avatar-lg rounded-circle d-flex align-items-center justify-content-center text-white font-weight-bold"
                                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);">
                                                {{ teacher.username[0].upper() }}
                                            </div>
                                            {% if teacher.is_active %}
                                                <span class="position-absolute top-0 start-100 translate-middle p-1 bg-success border border-light rounded-circle">
                                                    <span class="visually-hidden">نشط</span>
                                                </span>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-1 font-weight-bold text-dark">{{ teacher.username }}</h6>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="badge badge-soft-primary">أستاذ</span>
                                                {% if teacher.is_active %}
                                                    <span class="badge badge-soft-success">نشط</span>
                                                {% else %}
                                                    <span class="badge badge-soft-secondary">غير نشط</span>
                                                {% endif %}
                                                {% if teacher_progress and teacher.id in teacher_progress %}
                                                    {% set completion_rate = teacher_progress[teacher.id]['completion_rate'] %}
                                                    {% if completion_rate >= 80 %}
                                                        <span class="badge badge-soft-success">متفوق</span>
                                                    {% elif completion_rate >= 60 %}
                                                        <span class="badge badge-soft-info">جيد</span>
                                                    {% elif completion_rate >= 40 %}
                                                        <span class="badge badge-soft-warning">متوسط</span>
                                                    {% elif completion_rate > 0 %}
                                                        <span class="badge badge-soft-danger">ضعيف</span>
                                                    {% endif %}
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4">
                                    <div class="d-flex flex-column">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-envelope text-info me-2"></i>
                                            <a href="mailto:{{ teacher.email }}" class="text-decoration-none text-dark">
                                                {{ teacher.email }}
                                            </a>
                                        </div>
                                        {% if teacher.phone %}
                                            <div class="d-flex align-items-center mb-1">
                                                <i class="fas fa-phone text-success me-2"></i>
                                                <span class="text-muted small">{{ teacher.phone }}</span>
                                            </div>
                                        {% endif %}
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-plus text-secondary me-2"></i>
                                            <span class="text-muted small">انضم {{ teacher.created_at.strftime('%Y-%m-%d') }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4">
                                    {% set completion_rate = teacher_progress[teacher.id]['completion_rate']|default(0) if teacher_progress and teacher.id in teacher_progress else 0 %}
                                    <div class="d-flex flex-column">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="progress flex-grow-1 me-3" style="height: 12px; border-radius: 10px;">
                                                <div class="progress-bar
                                                    {% if completion_rate >= 80 %}bg-success
                                                    {% elif completion_rate >= 60 %}bg-info
                                                    {% elif completion_rate >= 40 %}bg-warning
                                                    {% else %}bg-danger{% endif %}"
                                                    role="progressbar"
                                                    style="width: {{ completion_rate }}%; border-radius: 10px; background: linear-gradient(90deg,
                                                        {% if completion_rate >= 80 %}#28a745, #20c997
                                                        {% elif completion_rate >= 60 %}#17a2b8, #6f42c1
                                                        {% elif completion_rate >= 40 %}#ffc107, #fd7e14
                                                        {% else %}#dc3545, #e83e8c{% endif %});"
                                                    aria-valuenow="{{ completion_rate }}"
                                                    aria-valuemin="0"
                                                    aria-valuemax="100">
                                                </div>
                                            </div>
                                            <span class="font-weight-bold h6 mb-0
                                                {% if completion_rate >= 80 %}text-success
                                                {% elif completion_rate >= 60 %}text-info
                                                {% elif completion_rate >= 40 %}text-warning
                                                {% else %}text-danger{% endif %}">
                                                {{ completion_rate|round|int }}%
                                            </span>
                                        </div>
                                        {% if teacher_progress and teacher.id in teacher_progress %}
                                            <div class="d-flex justify-content-between text-muted small">
                                                <span>{{ teacher_progress[teacher.id]['completed_materials'] }} / {{ teacher_progress[teacher.id]['total_materials'] }}</span>
                                                <span>مادة معرفية</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="py-4">
                                    {% if teacher_progress and teacher.id in teacher_progress %}
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <div class="card border-0 bg-success bg-opacity-10 text-center py-2">
                                                    <div class="h6 mb-0 text-success">{{ teacher_progress[teacher.id]['stats']['completed'] }}</div>
                                                    <small class="text-success">مكتمل</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="card border-0 bg-warning bg-opacity-10 text-center py-2">
                                                    <div class="h6 mb-0 text-warning">{{ teacher_progress[teacher.id]['stats']['in_progress'] }}</div>
                                                    <small class="text-warning">قيد التنفيذ</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="card border-0 bg-info bg-opacity-10 text-center py-2">
                                                    <div class="h6 mb-0 text-info">{{ teacher_progress[teacher.id]['stats']['planned'] }}</div>
                                                    <small class="text-info">مخطط</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="card border-0 bg-primary bg-opacity-10 text-center py-2">
                                                    <div class="h6 mb-0 text-primary">{{ teacher_progress[teacher.id]['stats']['total'] }}</div>
                                                    <small class="text-primary">الإجمالي</small>
                                                </div>
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="text-center py-3">
                                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                                            <div class="text-muted small">لا يوجد تقدم</div>
                                        </div>
                                    {% endif %}
                                </td>
                                <td class="py-4">
                                    {% if teacher.updated_at %}
                                        <div class="d-flex flex-column">
                                            <div class="d-flex align-items-center mb-1">
                                                <i class="fas fa-calendar-day text-primary me-2"></i>
                                                <span class="text-dark small font-weight-bold">{{ teacher.updated_at.strftime('%Y-%m-%d') }}</span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-clock text-info me-2"></i>
                                                <span class="text-muted small">{{ teacher.updated_at.strftime('%H:%M') }}</span>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge badge-soft-info activity-badge" data-date="{{ teacher.updated_at.strftime('%Y-%m-%d') }}">
                                                    حديث
                                                </span>
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="text-center py-3">
                                            <i class="fas fa-question-circle fa-2x text-muted mb-2"></i>
                                            <div class="text-muted small">لا يوجد نشاط</div>
                                        </div>
                                    {% endif %}
                                </td>
                                <td class="py-4">
                                    <div class="d-flex flex-column gap-2">
                                        <!-- الصف الأول من الأزرار -->
                                        <div class="d-flex gap-1 justify-content-center">
                                            <button type="button" class="btn btn-sm btn-primary rounded-pill px-3"
                                                    onclick="showTeacherProgress({{ teacher.id }}, '{{ teacher.username }}')"
                                                    title="عرض التقدم التفصيلي"
                                                    style="transition: all 0.3s ease;">
                                                <i class="fas fa-chart-line me-1"></i>
                                                <span class="d-none d-lg-inline">التقدم</span>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-info rounded-pill px-3"
                                                    onclick="viewTeacherProfile({{ teacher.id }}, '{{ teacher.username }}')"
                                                    title="عرض الملف الشخصي"
                                                    style="transition: all 0.3s ease;">
                                                <i class="fas fa-user me-1"></i>
                                                <span class="d-none d-lg-inline">الملف</span>
                                            </button>
                                        </div>
                                        <!-- الصف الثاني من الأزرار -->
                                        <div class="d-flex gap-1 justify-content-center">
                                            <button type="button" class="btn btn-sm btn-success rounded-pill px-3"
                                                    onclick="sendNotificationToTeacher({{ teacher.id }}, '{{ teacher.username }}')"
                                                    title="إرسال إشعار"
                                                    style="transition: all 0.3s ease;">
                                                <i class="fas fa-bell me-1"></i>
                                                <span class="d-none d-lg-inline">إشعار</span>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger rounded-pill px-3"
                                                    onclick="removeTeacher({{ teacher.id }}, '{{ teacher.username }}')"
                                                    title="إزالة من الإشراف"
                                                    style="transition: all 0.3s ease;">
                                                <i class="fas fa-user-minus me-1"></i>
                                                <span class="d-none d-lg-inline">إزالة</span>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد أساتذة تحت الإشراف</h5>
                    <p class="text-muted mb-4">ابدأ بإضافة أساتذة لمتابعة تقدمهم</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة أول أستاذ
                    </button>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- الرسوم البيانية والإحصائيات التفصيلية -->
    <div class="row mb-4">
        <!-- رسم بياني للتقدم حسب المستوى -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقدم حسب المستوى التعليمي
                    </h6>
                </div>
                <div class="card-body">
                    {% if level_stats %}
                        <canvas id="levelProgressChart" width="400" height="200"></canvas>
                        <div class="mt-3">
                            {% for level_id, stats in level_stats.items() %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-sm">{{ stats.name }}</span>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 100px; height: 6px;">
                                        <div class="progress-bar bg-primary" role="progressbar"
                                             style="width: {{ stats.completion_rate|default(0) }}%"></div>
                                    </div>
                                    <span class="text-sm font-weight-bold">{{ stats.completion_rate|default(0)|round|int }}%</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- رسم بياني للتقدم حسب المادة -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        التقدم حسب المادة الدراسية
                    </h6>
                </div>
                <div class="card-body">
                    {% if subject_stats %}
                        <canvas id="subjectProgressChart" width="400" height="200"></canvas>
                        <div class="mt-3">
                            <div class="row">
                                {% for subject_id, stats in subject_stats.items() %}
                                <div class="col-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle me-2" style="width: 8px; height: 8px;"></div>
                                        <span class="text-sm">{{ stats.name }}</span>
                                        <span class="text-sm font-weight-bold ms-auto">{{ stats.completion_rate|default(0)|round|int }}%</span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة إضافية -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4">
            <div class="card text-white shadow h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); transition: all 0.3s ease;">
                <div class="card-body d-flex flex-column justify-content-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div style="color: rgba(255, 255, 255, 0.7);" class="small">متوسط التقدم اليومي</div>
                            <div class="text-white h5 mb-0">
                                {{ daily_average_progress|default(0)|round(1) }} مادة/يوم
                            </div>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7);">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card text-white shadow h-100" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); transition: all 0.3s ease;">
                <div class="card-body d-flex flex-column justify-content-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div style="color: rgba(255, 255, 255, 0.7);" class="small">أفضل أستاذ هذا الشهر</div>
                            <div class="text-white h5 mb-0" style="min-height: 2.5rem;">
                                {% if best_teacher_this_month %}
                                    <div>{{ best_teacher_this_month.name }}</div>
                                    <small class="d-block" style="color: rgba(255, 255, 255, 0.85);">{{ best_teacher_this_month.completed_count }} درس</small>
                                {% else %}
                                    <div>لا يوجد بيانات</div>
                                {% endif %}
                            </div>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7);">
                            <i class="fas fa-trophy fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card text-white shadow h-100" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); transition: all 0.3s ease;">
                <div class="card-body d-flex flex-column justify-content-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div style="color: rgba(255, 255, 255, 0.7);" class="small">الإشعارات غير المقروءة</div>
                            <div class="text-white h5 mb-0">
                                {{ unread_notifications_count|default(0) }}
                            </div>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7);">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Modal عرض التقدم التفصيلي -->
<div class="modal fade" id="teacherProgressModal" tabindex="-1" aria-labelledby="teacherProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="teacherProgressModalLabel">
                    <i class="fas fa-chart-line me-2"></i>
                    التقدم التفصيلي للأستاذ
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="progressModalBody">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات التقدم...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal عرض الملف الشخصي -->
<div class="modal fade" id="teacherProfileModal" tabindex="-1" aria-labelledby="teacherProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="teacherProfileModalLabel">
                    <i class="fas fa-user me-2"></i>
                    الملف الشخصي للأستاذ
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="profileModalBody">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل الملف الشخصي...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}



<!-- JavaScript للوظائف -->
<script>
// إضافة تأثيرات الحوم للبطاقات وتحديث شارات النشاط
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات الحوم للبطاقات
    const cards = document.querySelectorAll('.card[style*="linear-gradient"]');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // تحديث شارات النشاط
    updateActivityBadges();
});

// تحديث شارات النشاط بناءً على التاريخ
function updateActivityBadges() {
    const badges = document.querySelectorAll('.activity-badge');
    const today = new Date();

    badges.forEach(badge => {
        const dateStr = badge.dataset.date;
        if (dateStr) {
            const activityDate = new Date(dateStr);
            const diffTime = Math.abs(today - activityDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // إزالة الكلاسات القديمة
            badge.classList.remove('badge-soft-success', 'badge-soft-info', 'badge-soft-warning', 'badge-soft-danger');

            if (diffDays === 0) {
                badge.textContent = 'اليوم';
                badge.classList.add('badge-soft-success');
            } else if (diffDays === 1) {
                badge.textContent = 'أمس';
                badge.classList.add('badge-soft-info');
            } else if (diffDays <= 7) {
                badge.textContent = `منذ ${diffDays} أيام`;
                badge.classList.add('badge-soft-warning');
            } else {
                badge.textContent = `منذ ${diffDays} يوم`;
                badge.classList.add('badge-soft-danger');
            }
        }
    });
}
// عرض التقدم التفصيلي للأستاذ
function showTeacherProgress(teacherId, teacherName) {
    alert('تم النقر على زر عرض التقدم التفصيلي للأستاذ: ' + teacherName + '\nمعرف الأستاذ: ' + teacherId);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('teacherProgressModal'));
    modal.show();

    // تحديث عنوان النافذة
    document.getElementById('teacherProgressModalLabel').innerHTML =
        '<i class="fas fa-chart-line me-2"></i>التقدم التفصيلي للأستاذ: ' + teacherName;

    // عرض رسالة بسيطة
    document.getElementById('progressModalBody').innerHTML = `
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>معلومات الأستاذ</h5>
            <p><strong>الاسم:</strong> ${teacherName}</p>
            <p><strong>المعرف:</strong> ${teacherId}</p>
            <p><strong>الحالة:</strong> سيتم تحميل آخر 5 مواد معرفية مكتملة هنا</p>
        </div>
        <div class="text-center py-4">
            <i class="fas fa-graduation-cap fa-3x text-primary mb-3"></i>
            <h5>وظيفة عرض التقدم التفصيلي</h5>
            <p class="text-muted">هذه الوظيفة تعمل بنجاح!</p>
        </div>
    `;
}

// عرض الملف الشخصي للأستاذ
function viewTeacherProfile(teacherId, teacherName) {
    alert('تم النقر على زر عرض الملف الشخصي للأستاذ: ' + teacherName + '\nمعرف الأستاذ: ' + teacherId);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('teacherProfileModal'));
    modal.show();

    // تحديث عنوان النافذة
    document.getElementById('teacherProfileModalLabel').innerHTML =
        '<i class="fas fa-user me-2"></i>الملف الشخصي للأستاذ: ' + teacherName;

    // عرض معلومات بسيطة
    document.getElementById('profileModalBody').innerHTML = `
        <div class="alert alert-success">
            <h5><i class="fas fa-user me-2"></i>معلومات الأستاذ</h5>
            <p><strong>الاسم:</strong> ${teacherName}</p>
            <p><strong>المعرف:</strong> ${teacherId}</p>
            <p><strong>الحالة:</strong> سيتم عرض الملف الشخصي الكامل هنا</p>
        </div>
        <div class="text-center py-4">
            <i class="fas fa-user-circle fa-3x text-success mb-3"></i>
            <h5>وظيفة عرض الملف الشخصي</h5>
            <p class="text-muted">هذه الوظيفة تعمل بنجاح!</p>
        </div>
    `;
}

// إزالة أستاذ من الإشراف
function removeTeacher(teacherId, teacherName) {
    const confirmMessage = `هل أنت متأكد من إزالة الأستاذ "${teacherName}" من إشرافك؟\n\nهذا الإجراء لا يمكن التراجع عنه.`;

    if (confirm(confirmMessage)) {
        alert(`تم تأكيد إزالة الأستاذ: ${teacherName}\nمعرف الأستاذ: ${teacherId}\n\nسيتم تنفيذ العملية...`);

        // محاكاة العملية
        setTimeout(() => {
            alert('✅ تم إزالة الأستاذ من الإشراف بنجاح!\n\nسيتم تحديث الصفحة...');
            // يمكن إضافة إعادة تحميل الصفحة هنا
            // location.reload();
        }, 1000);
    }
}

// إرسال إشعار للأستاذ
function sendNotificationToTeacher(teacherId, teacherName) {
    alert(`إرسال إشعار للأستاذ: ${teacherName}\nمعرف الأستاذ: ${teacherId}\n\nسيتم فتح نافذة إرسال الإشعار...`);
    // يمكن إضافة modal لإرسال الإشعار هنا
}

// تصفية الأساتذة
function filterTeachers(filterType) {
    const rows = document.querySelectorAll('.teacher-row');

    rows.forEach(row => {
        const completionRate = parseFloat(row.dataset.completionRate) || 0;
        const status = row.dataset.status;
        let show = true;

        switch(filterType) {
            case 'active':
                show = status === 'active';
                break;
            case 'high-progress':
                show = completionRate >= 80;
                break;
            case 'low-progress':
                show = completionRate < 40;
                break;
            case 'all':
            default:
                show = true;
                break;
        }

        row.style.display = show ? '' : 'none';
    });

    // تحديث عداد النتائج
    const visibleRows = document.querySelectorAll('.teacher-row[style=""], .teacher-row:not([style])');
    console.log(`تم عرض ${visibleRows.length} من أصل ${rows.length} أستاذ`);
}

// البحث في الأساتذة
function searchTeachers() {
    const searchTerm = document.getElementById('teacherSearch').value.toLowerCase();
    const rows = document.querySelectorAll('.teacher-row');

    rows.forEach(row => {
        const teacherName = row.dataset.teacherName.toLowerCase();
        const teacherEmail = row.querySelector('a[href^="mailto:"]')?.textContent.toLowerCase() || '';

        const matches = teacherName.includes(searchTerm) || teacherEmail.includes(searchTerm);
        row.style.display = matches ? '' : 'none';
    });
}

// ترتيب الجدول
function sortTable(columnIndex) {
    const table = document.getElementById('teachersTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    rows.sort((a, b) => {
        let aValue, bValue;

        switch(columnIndex) {
            case 0: // الاسم
                aValue = a.dataset.teacherName;
                bValue = b.dataset.teacherName;
                break;
            case 2: // نسبة الإنجاز
                aValue = parseFloat(a.dataset.completionRate) || 0;
                bValue = parseFloat(b.dataset.completionRate) || 0;
                break;
            case 4: // آخر نشاط
                aValue = a.cells[4].textContent.trim();
                bValue = b.cells[4].textContent.trim();
                break;
            default:
                return 0;
        }

        if (typeof aValue === 'string') {
            return aValue.localeCompare(bValue);
        } else {
            return bValue - aValue; // ترتيب تنازلي للأرقام
        }
    });

    // إعادة ترتيب الصفوف
    rows.forEach(row => tbody.appendChild(row));
}

// تصدير بيانات الأساتذة
function exportTeachersData() {
    alert('ميزة تصدير البيانات قيد التطوير...\n\nسيتم إضافة إمكانية تصدير البيانات إلى Excel/PDF قريباً');
}

// تحديث جدول الأساتذة
function refreshTeachersTable() {
    alert('جاري تحديث البيانات...');
    setTimeout(() => {
        location.reload();
    }, 1000);
}
</script>

<!-- CSS مخصص للجدول المطور -->
<style>
    /* أيقونة دائرية للرأس */
    .icon-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تأثيرات الصفوف */
    .teacher-row {
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
    }

    .teacher-row:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        border-left-color: #667eea;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    /* شارات مخصصة */
    .badge-soft-primary {
        background-color: rgba(102, 126, 234, 0.1);
        color: #667eea;
        border: 1px solid rgba(102, 126, 234, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .badge-soft-success {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .badge-soft-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
        border: 1px solid rgba(23, 162, 184, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .badge-soft-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .badge-soft-danger {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .badge-soft-secondary {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border: 1px solid rgba(108, 117, 125, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* تأثيرات الأزرار */
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .btn-sm.rounded-pill {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 600;
    }

    /* مؤشر الفرز */
    .cursor-pointer {
        cursor: pointer;
    }

    .cursor-pointer:hover {
        color: #667eea !important;
    }

    /* تحسين شريط البحث */
    .input-group .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* تحسين البطاقات الصغيرة */
    .card.border-0 {
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .card.border-0:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    /* تحسين شريط التقدم */
    .progress {
        background-color: rgba(0,0,0,0.05);
        border-radius: 10px;
    }

    /* تحسين الجدول */
    .table-responsive {
        border-radius: 0 0 15px 15px;
        overflow: hidden;
    }

    /* تحسين رأس الجدول */
    thead th {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    /* تأثيرات الحوم للأيقونات */
    .fas:hover {
        transform: scale(1.1);
        transition: transform 0.2s ease;
    }

    /* تحسين النصوص */
    .font-weight-bold {
        font-weight: 600 !important;
    }

    /* تحسين المسافات */
    .gap-1 {
        gap: 0.25rem !important;
    }

    .gap-2 {
        gap: 0.5rem !important;
    }

    .gap-3 {
        gap: 1rem !important;
    }

    /* تحسين الظلال */
    .shadow-lg {
        box-shadow: 0 1rem 3rem rgba(0,0,0,0.175) !important;
    }

    /* تحسين الحدود */
    .border-0 {
        border: 0 !important;
    }

    /* تحسين الخلفيات الشفافة */
    .bg-opacity-10 {
        background-color: rgba(var(--bs-bg-opacity-rgb), 0.1) !important;
    }

    /* تحسين التمرير */
    .table-responsive::-webkit-scrollbar {
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    /* تحسين الأفاتار */
    .avatar-lg {
        position: relative;
        display: inline-block;
    }

    /* تحسين الانتقالات */
    .teacher-row td {
        vertical-align: middle;
    }

    /* تحسين الروابط */
    a:hover {
        text-decoration: underline !important;
    }

    /* تحسين الإحصائيات السريعة */
    .bg-light {
        background-color: #f8f9fa !important;
    }

    /* تحسين الدروب داون */
    .dropdown-menu {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
    }

    .dropdown-item:hover {
        background-color: rgba(102, 126, 234, 0.1);
        color: #667eea;
    }
</style>
