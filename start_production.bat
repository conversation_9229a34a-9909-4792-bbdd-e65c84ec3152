@echo off
REM سكريپت تشغيل Ta9affi في وضع Production على Windows
REM استخدام: start_production.bat

echo 🚀 بدء تشغيل Ta9affi في وضع Production
echo ==========================================

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير مثبت
    pause
    exit /b 1
)

REM تعيين متغيرات البيئة
set PRODUCTION_MODE=true
if "%SERVER_IP%"=="" set SERVER_IP=*************
if "%PORT%"=="" set PORT=8000

echo 📍 الخادم: %SERVER_IP%
echo 🔌 المنفذ: %PORT%

REM التحقق من وجود ملف .env
if exist ".env" (
    echo ✅ تم العثور على ملف .env
    REM تحميل متغيرات البيئة من .env
    for /f "delims=" %%x in (.env) do (set "%%x")
) else (
    echo ⚠️ ملف .env غير موجود، سيتم استخدام متغيرات البيئة النظام
)

REM التحقق من المتطلبات
echo 📦 التحقق من المتطلبات...
if exist "requirements.txt" (
    pip install -r requirements.txt
) else (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

REM إنشاء مجلدات السجلات
if not exist "logs" mkdir logs

REM التحقق من نوع التشغيل المطلوب
gunicorn --version >nul 2>&1
if errorlevel 1 (
    echo 🔧 تشغيل باستخدام Flask المدمج...
    python run_production.py
) else (
    echo 🔧 تشغيل باستخدام Gunicorn...
    gunicorn --config gunicorn_config.py app:app
)

pause
