{% extends "base.html" %}

{% block title %}إدارة حدود الاستخدام - Ta9affi{% endblock %}

{% block extra_css %}
<style>
.rate-limit-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

.blocked-user {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.blocked-action {
    background: #e53e3e;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 2px;
    display: inline-block;
}

.progress-bar {
    background: #e2e8f0;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-danger { background: #e53e3e; }
.progress-warning { background: #ed8936; }
.progress-success { background: #38a169; }

.action-buttons {
    margin-top: 10px;
}

.btn-reset {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.8rem;
    margin: 2px;
}

.btn-reset:hover {
    background: #5a67d8;
}

.refresh-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: scale(1.1);
    background: #5a67d8;
}

.time-until-reset {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
}

@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 15px;
    }
    
    .rate-limit-card {
        padding: 15px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="rate-limit-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-shield-alt"></i> إدارة حدود الاستخدام</h2>
                <p class="mb-0">مراقبة وإدارة حدود استخدام المستخدمين لحماية موارد الخادم</p>
            </div>
            <div class="col-md-4 text-right">
                <button class="btn btn-light" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> تحديث البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات النظام -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{{ system_stats.total_users_with_limits }}</div>
                <div class="stat-label">مستخدمين لديهم حدود</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number text-danger">{{ system_stats.total_blocked_users }}</div>
                <div class="stat-label">مستخدمين محظورين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number text-warning">{{ system_stats.action_distribution.add_progress }}</div>
                <div class="stat-label">عمليات إضافة اليوم</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number text-info">{{ system_stats.action_distribution.delete_progress }}</div>
                <div class="stat-label">عمليات حذف اليوم</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- المستخدمين المحظورين -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-ban text-danger"></i> المستخدمين المحظورين</h5>
                </div>
                <div class="card-body">
                    {% if blocked_users %}
                        {% for user in blocked_users %}
                        <div class="blocked-user">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <strong>المستخدم #{{ user.user_id }}</strong>
                                </div>
                                <div class="col-md-5">
                                    {% for action in user.blocked_actions %}
                                    <span class="blocked-action">
                                        {{ action.action }}: {{ action.current_count }}/{{ action.max_requests }}
                                    </span>
                                    {% endfor %}
                                </div>
                                <div class="col-md-4">
                                    <div class="action-buttons">
                                        <button class="btn-reset" onclick="resetUserLimits({{ user.user_id }})">
                                            <i class="fas fa-undo"></i> إعادة تعيين الكل
                                        </button>
                                        {% for action in user.blocked_actions %}
                                        <button class="btn-reset" onclick="resetUserLimits({{ user.user_id }}, '{{ action.action }}')">
                                            إعادة {{ action.action }}
                                        </button>
                                        {% endfor %}
                                    </div>
                                    {% if user.blocked_actions %}
                                    <div class="time-until-reset">
                                        إعادة تعيين تلقائي: {{ user.blocked_actions[0].time_until_reset }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>لا يوجد مستخدمين محظورين حالياً</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- المستخدمين الأكثر نشاطاً -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line text-primary"></i> الأكثر نشاطاً</h5>
                </div>
                <div class="card-body">
                    {% if system_stats.most_active_users %}
                        {% for user in system_stats.most_active_users[:5] %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>المستخدم #{{ user.user_id }}</strong>
                                {% if user.is_blocked %}
                                <span class="badge badge-danger">محظور</span>
                                {% endif %}
                            </div>
                            <div>
                                <span class="badge badge-primary">{{ user.total_activity }} عملية</span>
                                <button class="btn btn-sm btn-outline-primary ml-2" onclick="viewUserDetails({{ user.user_id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="progress-bar">
                            {% set progress = (user.total_activity / 13 * 100) | round %}
                            <div class="progress-fill {% if progress >= 90 %}progress-danger{% elif progress >= 70 %}progress-warning{% else %}progress-success{% endif %}" 
                                 style="width: {{ progress }}%"></div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted">
                            <p>لا توجد بيانات نشاط</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- إحصائيات العمليات -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie text-success"></i> توزيع العمليات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>إضافة تقدم</span>
                            <span class="font-weight-bold">{{ system_stats.action_distribution.add_progress }}</span>
                        </div>
                        <div class="progress-bar">
                            {% set total = system_stats.action_distribution.add_progress + system_stats.action_distribution.delete_progress %}
                            {% if total > 0 %}
                                {% set add_percent = (system_stats.action_distribution.add_progress / total * 100) | round %}
                            {% else %}
                                {% set add_percent = 0 %}
                            {% endif %}
                            <div class="progress-fill progress-success" style="width: {{ add_percent }}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>حذف تقدم</span>
                            <span class="font-weight-bold">{{ system_stats.action_distribution.delete_progress }}</span>
                        </div>
                        <div class="progress-bar">
                            {% if total > 0 %}
                                {% set delete_percent = (system_stats.action_distribution.delete_progress / total * 100) | round %}
                            {% else %}
                                {% set delete_percent = 0 %}
                            {% endif %}
                            <div class="progress-fill progress-warning" style="width: {{ delete_percent }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- زر التحديث العائم -->
<button class="refresh-btn" onclick="refreshData()" title="تحديث البيانات">
    <i class="fas fa-sync-alt"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
// تحديث البيانات
function refreshData() {
    location.reload();
}

// إعادة تعيين حدود المستخدم
function resetUserLimits(userId, action = null) {
    if (!confirm('هل أنت متأكد من إعادة تعيين حدود هذا المستخدم؟')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('user_id', userId);
    if (action) {
        formData.append('action', action);
    }
    
    fetch('{{ url_for("rate_limit.reset_limits") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إعادة تعيين الحدود بنجاح');
            refreshData();
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// عرض تفاصيل المستخدم
function viewUserDetails(userId) {
    window.open('{{ url_for("rate_limit.user_details", user_id=0) }}'.replace('0', userId), '_blank');
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    fetch('{{ url_for("rate_limit.api_stats") }}')
    .then(response => response.json())
    .then(data => {
        // تحديث الإحصائيات بدون إعادة تحميل الصفحة
        updateStats(data);
    })
    .catch(error => console.error('Error updating stats:', error));
}, 30000);

function updateStats(data) {
    // تحديث الأرقام في الإحصائيات
    const stats = data.system_stats;
    document.querySelector('.stat-number').textContent = stats.total_users_with_limits;
    // يمكن إضافة المزيد من التحديثات هنا
}

// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي للكروت
    const cards = document.querySelectorAll('.stat-card, .blocked-user');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
