@echo off
chcp 65001 >nul
echo.
echo 🚀 بدء رفع تحديثات Ta9affi إلى GitHub...
echo.

echo 📁 إضافة الملفات المحدثة...
git add .

echo.
echo 💾 إنشاء commit...
git commit -m "إصلاح روابط Chargily وإضافة صفحات الدفع

✅ التحديثات المنجزة:

🔗 إصلاح روابط Chargily:
- تحديث Success URL إلى: http://ta9affi.com/payment/success
- تحديث Failure URL إلى: http://ta9affi.com/payment/failure  
- تحديث Webhook URL إلى: http://ta9affi.com/chargily-webhook

📄 الملفات المحدثة:
- app.py: إضافة routes للدفع وإصلاح الروابط
- subscription_manager.py: تحديث webhook URL
- config_production.py: تحديث إعدادات الإنتاج
- dokploy.config.js: تحديث متغيرات البيئة
- .env.example: تحديث المثال
- .env.dokploy: تحديث إعدادات dokploy

🆕 ملفات جديدة:
- templates/payment_success.html: صفحة نجاح الدفع
- templates/payment_failure.html: صفحة فشل الدفع
- DEPLOYMENT_README.md: دليل النشر المحدث

🔧 إصلاحات تقنية:
- إضافة import json في app.py
- إصلاح مسارات الدفع
- تحسين معالجة الأخطاء

🎯 الهدف:
حل مشكلة فشل الدفع وضمان عمل نظام Chargily بشكل صحيح"

echo.
echo ⬆️ رفع إلى GitHub...
git push origin main

echo.
echo ✅ تم رفع التحديثات بنجاح!
echo.
echo 📋 الخطوات التالية:
echo 1. اذهب إلى Dokploy dashboard
echo 2. اضغط Deploy للتطبيق
echo 3. تحقق من تحديث webhook URL في Chargily
echo 4. اختبر عملية الدفع
echo.
echo 🔗 الروابط المحدثة:
echo - Success: http://ta9affi.com/payment/success
echo - Failure: http://ta9affi.com/payment/failure
echo - Webhook: http://ta9affi.com/chargily-webhook
echo.
pause
