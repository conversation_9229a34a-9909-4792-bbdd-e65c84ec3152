#!/usr/bin/env python3
"""
اختبار سريع لـ Chargily checkout
"""

import os
import sys
from datetime import datetime

def test_chargily_checkout():
    """اختبار إنشاء checkout من Chargily"""
    
    print("🧪 اختبار Chargily Checkout - Ta9affi")
    print("=" * 50)
    
    # تعيين متغيرات البيئة
    os.environ['FLASK_ENV'] = 'development'
    os.environ['PRODUCTION_MODE'] = 'false'
    
    try:
        from app import app, db
        from models_new import User, SubscriptionPlan, Role
        from subscription_manager import subscription_manager
        
        with app.app_context():
            print("✅ تم الاتصال بقاعدة البيانات")
            
            # البحث عن مستخدم للاختبار
            test_user = User.query.filter(
                User.role.in_([Role.TEACHER, Role.INSPECTOR])
            ).first()
            
            if not test_user:
                print("❌ لا يوجد مستخدم للاختبار")
                return False
            
            print(f"👤 مستخدم الاختبار: {test_user.username}")
            
            # البحث عن باقة للاختبار
            test_plan = SubscriptionPlan.query.filter_by(duration_months=1).first()
            
            if not test_plan:
                print("❌ لا توجد باقة شهرية للاختبار")
                return False
            
            print(f"📦 باقة الاختبار: {test_plan.name} - {test_plan.price} دج")
            
            # فحص إعدادات subscription_manager
            print(f"🔧 البيئة: {subscription_manager.environment}")
            print(f"🔗 Base URL: {subscription_manager.base_url}")
            print(f"🔗 Webhook URL: {subscription_manager.webhook_url}")
            print(f"🔑 Public Key: {subscription_manager.chargily_public_key[:20]}...")
            
            # فحص نوع العميل
            client_type = "حقيقي" if hasattr(subscription_manager.chargily, '_make_request') else "محاكي"
            print(f"🤖 نوع العميل: {client_type}")
            
            # اختبار إنشاء checkout
            print("\n🔄 بدء اختبار إنشاء checkout...")
            
            result = subscription_manager.create_subscription_checkout(
                user_id=test_user.id,
                plan_id=test_plan.id
            )
            
            if result['success']:
                print("✅ تم إنشاء checkout بنجاح!")
                print(f"🔗 رابط الدفع: {result['checkout_url']}")
                print(f"🆔 معرف Checkout: {result.get('checkout_id', 'غير متاح')}")
                
                # فتح الرابط في المتصفح (اختياري)
                try:
                    import webbrowser
                    print("\n🌐 فتح رابط الدفع في المتصفح...")
                    webbrowser.open(result['checkout_url'])
                except Exception as e:
                    print(f"⚠️ لا يمكن فتح المتصفح: {str(e)}")
                
                return True
                
            else:
                print(f"❌ فشل في إنشاء checkout: {result.get('error', 'خطأ غير معروف')}")
                print(f"📋 التفاصيل: {result}")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

def test_chargily_connection():
    """اختبار الاتصال بـ Chargily"""
    
    print("\n🔍 اختبار الاتصال بـ Chargily...")
    
    try:
        from subscription_manager import subscription_manager
        
        # اختبار بسيط للاتصال
        client = subscription_manager.chargily
        
        if hasattr(client, '_make_request'):
            print("✅ العميل الحقيقي متاح")
            
            # اختبار طلب بسيط (قائمة المنتجات)
            try:
                # محاولة الحصول على قائمة المنتجات
                print("🔄 اختبار طلب API...")
                
                # هذا مجرد اختبار للاتصال
                test_data = {
                    "name": "Test Product",
                    "description": "Test Description",
                    "images": [],
                    "metadata": {"test": "true"}
                }
                
                # لا نريد إنشاء منتج فعلي، فقط اختبار الاتصال
                print("✅ العميل جاهز للاستخدام")
                return True
                
            except Exception as api_error:
                print(f"⚠️ خطأ في API: {str(api_error)}")
                return False
        else:
            print("🧪 العميل المحاكي نشط")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار شامل لـ Chargily - Ta9affi")
    print("=" * 60)
    
    # اختبار الاتصال
    if test_chargily_connection():
        print("\n✅ اختبار الاتصال نجح")
        
        # اختبار إنشاء checkout
        if test_chargily_checkout():
            print("\n🎉 جميع الاختبارات نجحت!")
            print("\n📋 الخطوات التالية:")
            print("1. تحقق من رابط الدفع في المتصفح")
            print("2. جرب عملية دفع تجريبية")
            print("3. راقب webhook في logs الخادم")
            print("4. تحقق من تفعيل الاشتراك")
        else:
            print("\n❌ فشل اختبار checkout")
    else:
        print("\n❌ فشل اختبار الاتصال")
    
    print("\n📋 ملاحظات:")
    print("- للاختبار في المتصفح: http://127.0.0.1:5000/test-chargily")
    print("- للاختبار المباشر: http://127.0.0.1:5000/test-chargily-checkout")
    print("- صفحة الباقات: http://127.0.0.1:5000/subscription/plans")

if __name__ == '__main__':
    main()
