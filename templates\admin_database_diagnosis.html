{% extends "base.html" %}

{% block title %}تشخيص قاعدة البيانات{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-stethoscope me-2 text-primary"></i>
                    تشخيص قاعدة البيانات
                </h2>
                <a href="{{ url_for('admin_advanced') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للإدارة المتقدمة
                </a>
            </div>

            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ diagnosis.get('user_count', 0) }}</h4>
                                    <p class="mb-0">إجمالي المستخدمين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ diagnosis.get('progress_entry_count', 0) }}</h4>
                                    <p class="mb-0">إجمالي التقدمات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ diagnosis.get('schedule_count', 0) }}</h4>
                                    <p class="mb-0">الجداول الدراسية</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ diagnosis.tables|length }}</h4>
                                    <p class="mb-0">عدد الجداول</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-table fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الجداول -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        تفاصيل الجداول
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الجدول</th>
                                    <th>عدد السجلات</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for table in diagnosis.tables %}
                                <tr>
                                    <td>
                                        <i class="fas fa-table me-2"></i>
                                        {{ table }}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ diagnosis.get(table + '_count', 'غير معروف') }}
                                        </span>
                                    </td>
                                    <td>
                                        {% set count = diagnosis.get(table + '_count', 0) %}
                                        {% if count == 'خطأ' %}
                                            <span class="badge bg-danger">خطأ</span>
                                        {% elif count > 0 %}
                                            <span class="badge bg-success">يحتوي على بيانات</span>
                                        {% else %}
                                            <span class="badge bg-secondary">فارغ</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تقدمات المستخدمين -->
            {% if diagnosis.progress_by_user %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقدمات المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>عدد التقدمات</th>
                                    <th>أول تقدم</th>
                                    <th>آخر تقدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for username, count, first_date, last_date in diagnosis.progress_by_user %}
                                <tr>
                                    <td>
                                        <i class="fas fa-user me-2"></i>
                                        {{ username }}
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ count }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ first_date or 'غير محدد' }}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ last_date or 'غير محدد' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- المستخدمين حسب الدور -->
            {% if diagnosis.users_by_role %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users-cog me-2"></i>
                        المستخدمين حسب الدور
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for role, count in diagnosis.users_by_role %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5 class="mb-1">{{ count }}</h5>
                                            <p class="mb-0 text-muted">
                                                {% if role == 'admin' %}
                                                    <i class="fas fa-crown me-1"></i>مدير
                                                {% elif role == 'inspector' %}
                                                    <i class="fas fa-search me-1"></i>مفتش
                                                {% elif role == 'teacher' %}
                                                    <i class="fas fa-chalkboard-teacher me-1"></i>أستاذ
                                                {% else %}
                                                    <i class="fas fa-user me-1"></i>{{ role }}
                                                {% endif %}
                                            </p>
                                        </div>
                                        <div class="align-self-center">
                                            {% if role == 'admin' %}
                                                <i class="fas fa-crown fa-2x text-warning"></i>
                                            {% elif role == 'inspector' %}
                                                <i class="fas fa-search fa-2x text-info"></i>
                                            {% elif role == 'teacher' %}
                                                <i class="fas fa-chalkboard-teacher fa-2x text-success"></i>
                                            {% else %}
                                                <i class="fas fa-user fa-2x text-secondary"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- أدوات إضافية -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        أدوات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('export_database') }}" class="btn btn-primary btn-block mb-2">
                                <i class="fas fa-download me-2"></i>
                                تصدير قاعدة البيانات
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ url_for('backup_database') }}" class="btn btn-success btn-block mb-2">
                                <i class="fas fa-save me-2"></i>
                                إنشاء نسخة احتياطية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.badge {
    font-size: 0.875rem;
}
</style>
{% endblock %}
