@echo off
REM تشغيل Ta9affi مع Rate Limiting على Windows

echo ========================================
echo 🛡️ Ta9affi مع نظام Rate Limiting
echo ========================================
echo.

echo 🔍 فحص المتطلبات...

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python متاح

REM فحص ملفات Rate Limiting
if not exist "rate_limiter.py" (
    echo ❌ ملف rate_limiter.py غير موجود
    pause
    exit /b 1
)
if not exist "rate_limit_monitor.py" (
    echo ❌ ملف rate_limit_monitor.py غير موجود
    pause
    exit /b 1
)
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)
echo ✅ جميع الملفات موجودة

echo.
echo 📦 تثبيت المتطلبات...

REM تثبيت مكتبة Redis (اختياري)
python -c "import redis" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت مكتبة Redis...
    pip install redis
    if errorlevel 1 (
        echo ⚠️ فشل تثبيت Redis - سيعمل النظام بدونها
    ) else (
        echo ✅ تم تثبيت Redis
    )
) else (
    echo ✅ مكتبة Redis متاحة
)

echo.
echo 🧪 اختبار نظام Rate Limiting...

REM تشغيل اختبار سريع
python -c "
try:
    from rate_limiter import AdvancedRateLimiter
    limiter = AdvancedRateLimiter()
    print('✅ نظام Rate Limiting يعمل بشكل صحيح')
except Exception as e:
    print(f'❌ خطأ في نظام Rate Limiting: {e}')
"

echo.
echo 📊 معلومات النظام:
echo ================================
echo 📋 القواعد المطبقة:
echo    - إضافة التقدمات: 10 كل 12 ساعة
echo    - حذف التقدمات: 3 كل 12 ساعة
echo    - التعديل: غير محدود
echo    - تحضير خطة الدرس: غير محدود
echo.
echo 🔄 إعادة التعيين:
echo    - كل 12 ساعة تلقائياً
echo    - النوافذ الزمنية: 00:00-12:00 و 12:00-24:00
echo.
echo 🎛️ الإدارة:
echo    - لوحة التحكم: http://localhost:5000/admin/rate-limits/
echo    - API المستخدمين: http://localhost:5000/api/my-rate-limits
echo ================================

echo.
echo 🚀 بدء تشغيل Ta9affi...
echo يمكنك الوصول للتطبيق على: http://localhost:5000
echo للإيقاف: اضغط Ctrl+C
echo.

REM تشغيل التطبيق
python app.py

echo.
echo 👋 تم إيقاف التطبيق
pause
