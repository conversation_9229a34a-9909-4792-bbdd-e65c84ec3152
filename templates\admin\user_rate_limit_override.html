{% extends "base.html" %}

{% block title %}تخصيص حدود المستخدمين - Ta9affi{% endblock %}

{% block extra_css %}
<style>
    .user-card {
        transition: all 0.3s;
        border: 2px solid transparent;
    }

    .user-card:hover {
        border-color: #007bff;
        transform: translateY(-2px);
    }

    .override-form {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid #007bff;
    }

    .override-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-cog text-primary"></i> تخصيص حدود المستخدمين</h2>
                <div>
                    <a href="{{ url_for('admin_rate_limit.dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للوحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- نموذج تخصيص جديد -->
        <div class="col-lg-6">
            <div class="override-form">
                <h4 class="text-center mb-4"><i class="fas fa-plus-circle"></i> إضافة تخصيص جديد</h4>

                <form method="POST" action="{{ url_for('admin_rate_limit.set_user_override') }}">
                    <!-- اختيار المستخدم -->
                    <div class="form-section">
                        <h6 class="text-primary"><i class="fas fa-user"></i> اختيار المستخدم</h6>
                        <div class="form-group">
                            <label for="user_id">المستخدم:</label>
                            <select class="form-control" id="user_id" name="user_id" required>
                                <option value="">اختر مستخدم...</option>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- إعدادات إضافة التقدمات -->
                    <div class="form-section">
                        <h6 class="text-success"><i class="fas fa-plus-circle"></i> إعدادات إضافة التقدمات</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="add_progress_limit">الحد الأقصى للإضافات:</label>
                                    <input type="number" class="form-control" id="add_progress_limit"
                                        name="add_progress_limit" min="1" max="100"
                                        placeholder="اتركه فارغاً للاستخدام الافتراضي">
                                    <small class="form-text text-muted">عدد التقدمات التي يمكن إضافتها</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="add_progress_window_hours">النافذة الزمنية (ساعة):</label>
                                    <input type="number" class="form-control" id="add_progress_window_hours"
                                        name="add_progress_window_hours" min="1" max="168"
                                        placeholder="اتركه فارغاً للاستخدام الافتراضي">
                                    <small class="form-text text-muted">المدة الزمنية للحد</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات حذف التقدمات -->
                    <div class="form-section">
                        <h6 class="text-danger"><i class="fas fa-trash-alt"></i> إعدادات حذف التقدمات</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="delete_progress_limit">الحد الأقصى للحذف:</label>
                                    <input type="number" class="form-control" id="delete_progress_limit"
                                        name="delete_progress_limit" min="1" max="50"
                                        placeholder="اتركه فارغاً للاستخدام الافتراضي">
                                    <small class="form-text text-muted">عدد التقدمات التي يمكن حذفها</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="delete_progress_window_hours">النافذة الزمنية (ساعة):</label>
                                    <input type="number" class="form-control" id="delete_progress_window_hours"
                                        name="delete_progress_window_hours" min="1" max="168"
                                        placeholder="اتركه فارغاً للاستخدام الافتراضي">
                                    <small class="form-text text-muted">المدة الزمنية للحد</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات إضافية -->
                    <div class="form-section">
                        <h6 class="text-info"><i class="fas fa-cog"></i> إعدادات إضافية</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expires_days">انتهاء الصلاحية (أيام):</label>
                                    <input type="number" class="form-control" id="expires_days" name="expires_days"
                                        min="1" max="365" placeholder="اتركه فارغاً للتخصيص الدائم">
                                    <small class="form-text text-muted">عدد الأيام قبل انتهاء التخصيص</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="reason">سبب التخصيص:</label>
                                    <input type="text" class="form-control" id="reason" name="reason"
                                        placeholder="سبب منح التخصيص">
                                    <small class="form-text text-muted">سبب اختياري للتوثيق</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i> حفظ التخصيص
                        </button>
                        <button type="reset" class="btn btn-secondary btn-lg ml-2">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- قائمة التخصيصات الحالية -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> التخصيصات الحالية</h5>
                </div>
                <div class="card-body">
                    {% if overrides %}
                    {% for override in overrides %}
                    {% set user = override.user if override.user else {'username': 'مستخدم محذوف', 'email': 'غير متاح',
                    'id': override.user_id} %}
                    <div class="user-card card mb-3 position-relative">
                        <span class="override-badge badge badge-success">نشط</span>
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-user"></i> {{ user.username }}
                                <small class="text-muted">({{ user.email }})</small>
                            </h6>

                            <div class="row">
                                <div class="col-6">
                                    <p class="mb-1"><strong>إضافة التقدمات:</strong></p>
                                    <span class="badge badge-primary">
                                        {{ override.add_progress_limit or 'افتراضي' }}
                                    </span>
                                    <span class="badge badge-info">
                                        {{ override.add_progress_window_hours or 'افتراضي' }}ساعة
                                    </span>
                                </div>
                                <div class="col-6">
                                    <p class="mb-1"><strong>حذف التقدمات:</strong></p>
                                    <span class="badge badge-danger">
                                        {{ override.delete_progress_limit or 'افتراضي' }}
                                    </span>
                                    <span class="badge badge-warning">
                                        {{ override.delete_progress_window_hours or 'افتراضي' }}ساعة
                                    </span>
                                </div>
                            </div>

                            {% if override.reason %}
                            <p class="mt-2 mb-1"><strong>السبب:</strong> {{ override.reason }}</p>
                            {% endif %}

                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <small class="text-muted">
                                    {% if override.expires_at %}
                                    ينتهي: {{ override.expires_at.strftime('%Y-%m-%d') }}
                                    {% else %}
                                    دائم
                                    {% endif %}
                                </small>
                                <form method="POST"
                                    action="{{ url_for('admin_rate_limit.remove_user_override', user_id=user.id) }}"
                                    style="display: inline;"
                                    onsubmit="return confirm('هل أنت متأكد من إزالة التخصيص؟')">
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-times"></i> إزالة
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد تخصيصات حالياً</p>
                        <p class="small text-muted">استخدم النموذج على اليسار لإضافة تخصيص جديد</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- نصائح سريعة -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb"></i> نصائح سريعة</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success"></i> اترك الحقول فارغة لاستخدام الإعدادات الافتراضية
                        </li>
                        <li><i class="fas fa-check text-success"></i> يمكن تعيين تاريخ انتهاء للتخصيصات المؤقتة</li>
                        <li><i class="fas fa-check text-success"></i> التخصيصات تُطبق فوراً على المستخدم</li>
                        <li><i class="fas fa-check text-success"></i> يمكن تعديل التخصيص بإعادة إنشائه</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function () {
        // إضافة تأثيرات بصرية للنموذج
        const formInputs = document.querySelectorAll('.form-control');
        formInputs.forEach(input => {
            input.addEventListener('focus', function () {
                this.parentElement.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.parentElement.style.transition = 'transform 0.2s';
            });

            input.addEventListener('blur', function () {
                this.parentElement.parentElement.style.transform = 'scale(1)';
            });
        });

        // تحديث placeholder بناءً على اختيار المستخدم
        const userSelect = document.getElementById('user_id');
        userSelect.addEventListener('change', function () {
            if (this.value) {
                // يمكن إضافة منطق لجلب الحدود الحالية للمستخدم
                console.log('تم اختيار المستخدم:', this.value);
            }
        });
    });

    // دالة لجلب حدود المستخدم الحالية
    function loadUserCurrentLimits(userId) {
        if (!userId) return;

        fetch(`{{ url_for('admin_rate_limit.api_user_limits', user_id=0) }}`.replace('0', userId))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const limits = data.limits;
                    // تحديث placeholder بالقيم الحالية
                    document.getElementById('add_progress_limit').placeholder =
                        `الحالي: ${limits.add_progress.max_requests}`;
                    document.getElementById('add_progress_window_hours').placeholder =
                        `الحالي: ${limits.add_progress.window_hours} ساعة`;
                    document.getElementById('delete_progress_limit').placeholder =
                        `الحالي: ${limits.delete_progress.max_requests}`;
                    document.getElementById('delete_progress_window_hours').placeholder =
                        `الحالي: ${limits.delete_progress.window_hours} ساعة`;
                }
            })
            .catch(error => console.error('خطأ في جلب حدود المستخدم:', error));
    }

    // ربط الدالة بتغيير المستخدم
    document.getElementById('user_id').addEventListener('change', function () {
        loadUserCurrentLimits(this.value);
    });
</script>
{% endblock %}