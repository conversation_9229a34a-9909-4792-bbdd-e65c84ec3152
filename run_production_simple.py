#!/usr/bin/env python3
"""
سكريپت تشغيل Ta9affi في وضع Production - مبسط ومضمون
"""

import os
import sys

def main():
    """تشغيل التطبيق مع الإعدادات الصحيحة"""
    
    print("🚀 تشغيل Ta9affi في وضع Production")
    print("="*50)
    
    # تعيين متغيرات البيئة بشكل مباشر
    os.environ['PRODUCTION_MODE'] = 'true'
    os.environ['SERVER_IP'] = '*************'
    os.environ['PORT'] = '8000'
    os.environ['SECRET_KEY'] = 'ta9affi-production-secret-key-2024'
    
    # استخدام SQLite كبديل آمن
    if not os.environ.get('DATABASE_URL'):
        os.environ['DATABASE_URL'] = 'sqlite:///ta9affi_production.db'
    
    print("✅ تم تعيين متغيرات البيئة:")
    print(f"   🏭 وضع Production: {os.environ['PRODUCTION_MODE']}")
    print(f"   🌐 عنوان الخادم: {os.environ['SERVER_IP']}")
    print(f"   🔌 المنفذ: {os.environ['PORT']}")
    print(f"   🗄️ قاعدة البيانات: {os.environ['DATABASE_URL']}")
    
    print("\n🔗 روابط الوصول:")
    print(f"   📱 التطبيق: http://*************:8000")
    print(f"   🔐 تسجيل الدخول: http://*************:8000/login")
    print(f"   📝 التسجيل: http://*************:8000/register")
    print(f"   ❤️ فحص الصحة: http://*************:8000/health")
    
    print("\n" + "="*50)
    print("🚀 بدء تشغيل التطبيق...")
    print("⏹️ اضغط Ctrl+C لإيقاف التطبيق")
    print("="*50)
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        
        # تشغيل التطبيق مع الإعدادات المحددة
        app.run(
            host='0.0.0.0',        # الاستماع على جميع العناوين
            port=8000,             # المنفذ 8000
            debug=False,           # تعطيل debug في Production
            threaded=True,         # دعم multiple threads
            use_reloader=False     # تعطيل auto-reload
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {str(e)}")
        print("\n💡 نصائح لحل المشكلة:")
        print("1. تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        print("2. تأكد من صلاحيات الملفات")
        print("3. تأكد من فتح المنفذ 8000 في Firewall")
        print("4. جرب تشغيل: python diagnose_server.py")
        sys.exit(1)

if __name__ == '__main__':
    main()
