#!/usr/bin/env python3
"""
إعداد SSL للتطبيق
"""

import os
import subprocess
import sys

def install_pyopenssl():
    """تثبيت PyOpenSSL"""
    
    print("📦 تثبيت PyOpenSSL...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyopenssl'])
        print("✅ تم تثبيت PyOpenSSL بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت PyOpenSSL: {e}")
        return False

def create_self_signed_cert():
    """إنشاء شهادة self-signed للاختبار"""
    
    print("🔐 إنشاء شهادة self-signed للاختبار...")
    
    try:
        from OpenSSL import crypto
        
        # إنشاء مفتاح خاص
        key = crypto.PKey()
        key.generate_key(crypto.TYPE_RSA, 2048)
        
        # إنشاء شهادة
        cert = crypto.X509()
        cert.get_subject().C = "DZ"
        cert.get_subject().ST = "Algeria"
        cert.get_subject().L = "Algiers"
        cert.get_subject().O = "Ta9affi"
        cert.get_subject().OU = "Education"
        cert.get_subject().CN = "ta9affi.com"
        
        # إعدادات الشهادة
        cert.set_serial_number(1000)
        cert.gmtime_adj_notBefore(0)
        cert.gmtime_adj_notAfter(365*24*60*60)  # سنة واحدة
        cert.set_issuer(cert.get_subject())
        cert.set_pubkey(key)
        cert.sign(key, 'sha256')
        
        # إنشاء مجلد للشهادات
        ssl_dir = "ssl_certs"
        os.makedirs(ssl_dir, exist_ok=True)
        
        # حفظ الشهادة والمفتاح
        cert_path = os.path.join(ssl_dir, "ta9affi.crt")
        key_path = os.path.join(ssl_dir, "ta9affi.key")
        
        with open(cert_path, "wb") as f:
            f.write(crypto.dump_certificate(crypto.FILETYPE_PEM, cert))
        
        with open(key_path, "wb") as f:
            f.write(crypto.dump_privatekey(crypto.FILETYPE_PEM, key))
        
        print(f"✅ تم إنشاء الشهادة: {cert_path}")
        print(f"✅ تم إنشاء المفتاح: {key_path}")
        
        return cert_path, key_path
        
    except ImportError:
        print("❌ PyOpenSSL غير مثبت. قم بتشبيته أولاً.")
        return None, None
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشهادة: {e}")
        return None, None

def setup_letsencrypt():
    """إعداد Let's Encrypt"""
    
    print("🔐 إعداد Let's Encrypt...")
    
    commands = [
        "sudo apt update",
        "sudo apt install -y certbot",
        "sudo certbot certonly --standalone -d ta9affi.com -d www.ta9affi.com --email <EMAIL> --agree-tos --no-eff-email"
    ]
    
    print("📋 الأوامر المطلوبة:")
    for cmd in commands:
        print(f"   {cmd}")
    
    print("\n⚠️ تحذير: تأكد من:")
    print("1. إيقاف nginx مؤقتاً: sudo systemctl stop nginx")
    print("2. فتح البورت 80 و 443")
    print("3. توجيه DNS للخادم")

def create_nginx_ssl_config():
    """إنشاء إعداد nginx مع SSL"""
    
    config = """
# إعداد nginx مع SSL لـ Ta9affi
server {
    listen 80;
    server_name ta9affi.com www.ta9affi.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ta9affi.com www.ta9affi.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/ta9affi.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ta9affi.com/privkey.pem;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Proxy to Flask app
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
    }
}
"""
    
    with open("nginx_ssl_config.conf", "w") as f:
        f.write(config)
    
    print("✅ تم إنشاء ملف nginx_ssl_config.conf")

def update_requirements():
    """تحديث requirements.txt"""
    
    requirements_addition = """
# SSL Support
pyopenssl>=23.0.0
cryptography>=3.4.8
"""
    
    with open("requirements_ssl.txt", "w") as f:
        # قراءة requirements الحالي
        try:
            with open("requirements.txt", "r") as current:
                f.write(current.read())
        except FileNotFoundError:
            pass
        
        f.write(requirements_addition)
    
    print("✅ تم إنشاء requirements_ssl.txt")

def main():
    """الدالة الرئيسية"""
    
    print("🔐 إعداد SSL لـ Ta9affi")
    print("=" * 40)
    
    print("\n📋 الخيارات المتاحة:")
    print("1. تثبيت PyOpenSSL")
    print("2. إنشاء شهادة self-signed للاختبار")
    print("3. إعداد Let's Encrypt")
    print("4. إنشاء إعداد nginx مع SSL")
    print("5. تحديث requirements")
    print("6. الكل")
    
    choice = input("\nاختر رقم (1-6): ").strip()
    
    if choice == "1" or choice == "6":
        install_pyopenssl()
    
    if choice == "2" or choice == "6":
        cert_path, key_path = create_self_signed_cert()
        if cert_path and key_path:
            print(f"\n🔧 لاستخدام الشهادة، قم بتعيين:")
            print(f"export SSL_CERT_PATH={os.path.abspath(cert_path)}")
            print(f"export SSL_KEY_PATH={os.path.abspath(key_path)}")
    
    if choice == "3" or choice == "6":
        setup_letsencrypt()
    
    if choice == "4" or choice == "6":
        create_nginx_ssl_config()
    
    if choice == "5" or choice == "6":
        update_requirements()
    
    print("\n✅ تم الانتهاء من الإعداد!")
    
    print("\n📋 الخطوات التالية:")
    print("1. للاختبار المحلي: python app_with_ssl.py")
    print("2. للوضع المزدوج: python app_with_ssl.py dual")
    print("3. لـ nginx مع SSL: استخدم nginx_ssl_config.conf")

if __name__ == '__main__':
    main()
