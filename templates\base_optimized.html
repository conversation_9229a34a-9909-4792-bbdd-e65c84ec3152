<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- معلومات SEO -->
    <title>{% block title %}Ta9affi - منصة التعليم الذكية{% endblock %}</title>
    <meta name="description" content="{% block description %}منصة Ta9affi للتعليم الذكي والتفاعلي{% endblock %}">
    <meta name="keywords" content="{% block keywords %}تعليم، منصة تعليمية، Ta9affi{% endblock %}">
    <meta name="author" content="Ta9affi Team">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{% block og_title %}Ta9affi - منصة التعليم الذكية{% endblock %}">
    <meta property="og:description" content="{% block og_description %}منصة Ta9affi للتعليم الذكي والتفاعلي{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:image" content="{{ url_for('static', filename='images/og-image.jpg') }}">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}Ta9affi - منصة التعليم الذكية{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}منصة Ta9affi للتعليم الذكي والتفاعلي{% endblock %}">
    <meta name="twitter:image" content="{{ url_for('static', filename='images/twitter-card.jpg') }}">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Preconnect للخطوط والموارد الخارجية -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    <link rel="manifest" href="{{ url_for('static', filename='site.webmanifest') }}">
    
    <!-- CSS Critical Path -->
    <style>
        /* CSS حرج للتحميل السريع */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .navbar {
            background-color: #007bff;
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
    
    <!-- CSS الرئيسي -->
    {% assets "css_main" %}
        <link rel="stylesheet" href="{{ ASSET_URL }}">
    {% endassets %}
    
    <!-- CSS للأداء -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/performance.css') }}">
    
    <!-- CSS إضافي للصفحات المحددة -->
    {% block extra_css %}{% endblock %}
    
    <!-- Preload للموارد المهمة -->
    <link rel="preload" href="{{ url_for('static', filename='fonts/main-font.woff2') }}" as="font" type="font/woff2" crossorigin>
    {% assets "js_main" %}
        <link rel="preload" href="{{ ASSET_URL }}" as="script">
    {% endassets %}
    
    <!-- Resource Hints -->
    {% block resource_hints %}{% endblock %}
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
    </div>
    
    <!-- تخطي للمحتوى الرئيسي (للوصولية) -->
    <a href="#main-content" class="sr-only sr-only-focusable">تخطي إلى المحتوى الرئيسي</a>
    
    <!-- الشريط العلوي -->
    <nav class="navbar" role="navigation" aria-label="التنقل الرئيسي">
        <div class="container">
            {% block navbar %}
            <div class="navbar-brand">
                <a href="{{ url_for('main.index') }}" aria-label="الصفحة الرئيسية">
                    <img src="{{ url_for('static', filename='images/logo.svg') }}" 
                         alt="شعار Ta9affi" 
                         width="120" 
                         height="40"
                         loading="eager">
                </a>
            </div>
            
            <div class="navbar-nav">
                {% if current_user.is_authenticated %}
                    <a href="{{ url_for('main.dashboard') }}" class="nav-link">لوحة التحكم</a>
                    <a href="{{ url_for('auth.logout') }}" class="nav-link">تسجيل الخروج</a>
                {% else %}
                    <a href="{{ url_for('auth.login') }}" class="nav-link">تسجيل الدخول</a>
                    <a href="{{ url_for('auth.register') }}" class="nav-link">إنشاء حساب</a>
                {% endif %}
            </div>
            {% endblock %}
        </div>
    </nav>
    
    <!-- المحتوى الرئيسي -->
    <main id="main-content" role="main" tabindex="-1">
        <!-- رسائل Flash -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container mt-3">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        <!-- محتوى الصفحة -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- التذييل -->
    <footer class="footer mt-5" role="contentinfo">
        <div class="container">
            {% block footer %}
            <div class="row">
                <div class="col-md-6">
                    <h5>Ta9affi</h5>
                    <p>منصة التعليم الذكية والتفاعلية</p>
                </div>
                <div class="col-md-6">
                    <h5>روابط مفيدة</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.about') }}">حول Ta9affi</a></li>
                        <li><a href="{{ url_for('main.contact') }}">اتصل بنا</a></li>
                        <li><a href="{{ url_for('main.privacy') }}">سياسة الخصوصية</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; {{ moment().format('YYYY') }} Ta9affi. جميع الحقوق محفوظة.</p>
            </div>
            {% endblock %}
        </div>
    </footer>
    
    <!-- JavaScript الرئيسي -->
    {% assets "js_main" %}
        <script src="{{ ASSET_URL }}" defer></script>
    {% endassets %}
    
    <!-- JavaScript للأداء -->
    <script src="{{ url_for('static', filename='js/performance.js') }}" defer></script>
    
    <!-- JavaScript إضافي للصفحات المحددة -->
    {% block extra_js %}{% endblock %}
    
    <!-- سكريبت إخفاء شاشة التحميل -->
    <script>
        // إخفاء شاشة التحميل عند اكتمال التحميل
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }
        });
        
        // إخفاء شاشة التحميل بعد 3 ثواني كحد أقصى
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen && loadingScreen.style.display !== 'none') {
                loadingScreen.style.display = 'none';
            }
        }, 3000);
    </script>
    
    <!-- Google Analytics (إذا كان متاحاً) -->
    {% if config.GOOGLE_ANALYTICS_ID %}
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config.GOOGLE_ANALYTICS_ID }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ config.GOOGLE_ANALYTICS_ID }}');
    </script>
    {% endif %}
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Ta9affi",
        "description": "منصة التعليم الذكية والتفاعلية",
        "url": "{{ request.url_root }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.url_root }}search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    
    {% block structured_data %}{% endblock %}
</body>
</html>
