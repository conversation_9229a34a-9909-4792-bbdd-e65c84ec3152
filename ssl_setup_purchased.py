#!/usr/bin/env python3
"""
إعداد الشهادة المشتراة من cheapsslweb.com
"""

import os
import ssl
import requests
from datetime import datetime

def save_private_key():
    """حفظ المفتاح الخاص"""
    
    private_key = """*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    
    # إنشاء مجلد SSL
    ssl_dir = "ssl_purchased"
    os.makedirs(ssl_dir, exist_ok=True)
    
    # حفظ المفتاح الخاص
    key_path = os.path.join(ssl_dir, "ta9affi.key")
    with open(key_path, "w") as f:
        f.write(private_key)
    
    # تعيين صلاحيات آمنة
    os.chmod(key_path, 0o600)
    
    print(f"✅ تم حفظ المفتاح الخاص: {key_path}")
    return key_path

def create_csr():
    """إنشاء Certificate Signing Request"""
    
    try:
        from OpenSSL import crypto
        
        # قراءة المفتاح الخاص
        key_path = "ssl_purchased/ta9affi.key"
        with open(key_path, "r") as f:
            key_data = f.read()
        
        # تحميل المفتاح
        private_key = crypto.load_privatekey(crypto.FILETYPE_PEM, key_data)
        
        # إنشاء CSR
        req = crypto.X509Req()
        req.get_subject().C = "DZ"
        req.get_subject().ST = "Algeria"
        req.get_subject().L = "Algiers"
        req.get_subject().O = "Ta9affi Education Platform"
        req.get_subject().OU = "IT Department"
        req.get_subject().CN = "ta9affi.com"
        
        # إضافة Subject Alternative Names
        san_list = ["DNS:ta9affi.com", "DNS:www.ta9affi.com"]
        san_extension = crypto.X509Extension(
            b"subjectAltName",
            False,
            ", ".join(san_list).encode()
        )
        req.add_extensions([san_extension])
        
        req.set_pubkey(private_key)
        req.sign(private_key, "sha256")
        
        # حفظ CSR
        csr_path = "ssl_purchased/ta9affi.csr"
        with open(csr_path, "wb") as f:
            f.write(crypto.dump_certificate_request(crypto.FILETYPE_PEM, req))
        
        print(f"✅ تم إنشاء CSR: {csr_path}")
        
        # عرض CSR للنسخ
        with open(csr_path, "r") as f:
            csr_content = f.read()
        
        print("\n📋 CSR للإرسال إلى cheapsslweb.com:")
        print("=" * 50)
        print(csr_content)
        print("=" * 50)
        
        return csr_path
        
    except ImportError:
        print("❌ PyOpenSSL غير مثبت. قم بتثبيته: pip install pyopenssl")
        return None
    except Exception as e:
        print(f"❌ خطأ في إنشاء CSR: {e}")
        return None

def save_certificate():
    """حفظ الشهادة المستلمة من cheapsslweb.com"""
    
    print("📜 الصق الشهادة المستلمة من cheapsslweb.com:")
    print("(اضغط Enter مرتين للانتهاء)")
    
    cert_lines = []
    while True:
        line = input()
        if line == "" and len(cert_lines) > 0:
            break
        cert_lines.append(line)
    
    certificate = "\n".join(cert_lines)
    
    # حفظ الشهادة
    cert_path = "ssl_purchased/ta9affi.crt"
    with open(cert_path, "w") as f:
        f.write(certificate)
    
    print(f"✅ تم حفظ الشهادة: {cert_path}")
    return cert_path

def download_intermediate_cert():
    """تحميل الشهادة الوسطية"""
    
    # شهادات وسطية شائعة لـ cheapsslweb.com
    intermediate_urls = [
        "https://crt.sh/?d=2835394",  # Sectigo RSA Domain Validation Secure Server CA
        "https://crt.sh/?d=1199354",  # COMODO RSA Domain Validation Secure Server CA
    ]
    
    for i, url in enumerate(intermediate_urls):
        try:
            print(f"🔄 محاولة تحميل الشهادة الوسطية {i+1}...")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                intermediate_path = f"ssl_purchased/intermediate_{i+1}.crt"
                with open(intermediate_path, "w") as f:
                    f.write(response.text)
                
                print(f"✅ تم تحميل الشهادة الوسطية: {intermediate_path}")
                return intermediate_path
                
        except Exception as e:
            print(f"⚠️ فشل تحميل الشهادة الوسطية {i+1}: {e}")
    
    print("❌ لم يتم العثور على شهادة وسطية. قم بتحميلها يدوياً من cheapsslweb.com")
    return None

def create_fullchain():
    """إنشاء fullchain certificate"""
    
    cert_path = "ssl_purchased/ta9affi.crt"
    intermediate_path = "ssl_purchased/intermediate_1.crt"
    
    if not os.path.exists(cert_path):
        print("❌ الشهادة الأساسية غير موجودة")
        return None
    
    fullchain_path = "ssl_purchased/fullchain.pem"
    
    with open(fullchain_path, "w") as fullchain:
        # الشهادة الأساسية أولاً
        with open(cert_path, "r") as cert:
            fullchain.write(cert.read())
        
        # الشهادة الوسطية
        if os.path.exists(intermediate_path):
            with open(intermediate_path, "r") as intermediate:
                fullchain.write("\n" + intermediate.read())
        else:
            print("⚠️ الشهادة الوسطية غير موجودة")
    
    print(f"✅ تم إنشاء fullchain: {fullchain_path}")
    return fullchain_path

def test_ssl_setup():
    """اختبار إعداد SSL"""
    
    key_path = "ssl_purchased/ta9affi.key"
    cert_path = "ssl_purchased/ta9affi.crt"
    
    if not os.path.exists(key_path) or not os.path.exists(cert_path):
        print("❌ الملفات غير مكتملة")
        return False
    
    try:
        # اختبار تحميل الملفات
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(cert_path, key_path)
        
        print("✅ إعداد SSL صحيح!")
        
        # عرض معلومات الشهادة
        with open(cert_path, "r") as f:
            cert_data = f.read()
        
        from OpenSSL import crypto
        cert = crypto.load_certificate(crypto.FILETYPE_PEM, cert_data)
        
        print(f"📜 معلومات الشهادة:")
        print(f"   - الموضوع: {cert.get_subject()}")
        print(f"   - المُصدر: {cert.get_issuer()}")
        print(f"   - صالحة حتى: {cert.get_notAfter().decode()}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار SSL: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔐 إعداد الشهادة المشتراة من cheapsslweb.com")
    print("=" * 60)
    
    print("\n📋 الخطوات:")
    print("1. حفظ المفتاح الخاص")
    print("2. إنشاء CSR (إذا لم تكن قد أرسلته)")
    print("3. حفظ الشهادة المستلمة")
    print("4. تحميل الشهادة الوسطية")
    print("5. إنشاء fullchain")
    print("6. اختبار الإعداد")
    
    choice = input("\nاختر رقم الخطوة (1-6) أو 'all' للكل: ").strip()
    
    if choice == "1" or choice == "all":
        save_private_key()
    
    if choice == "2" or choice == "all":
        create_csr()
    
    if choice == "3" or choice == "all":
        save_certificate()
    
    if choice == "4" or choice == "all":
        download_intermediate_cert()
    
    if choice == "5" or choice == "all":
        create_fullchain()
    
    if choice == "6" or choice == "all":
        test_ssl_setup()
    
    print("\n✅ تم الانتهاء!")
    print("\n📋 الملفات المُنشأة:")
    print("- ssl_purchased/ta9affi.key (المفتاح الخاص)")
    print("- ssl_purchased/ta9affi.crt (الشهادة)")
    print("- ssl_purchased/fullchain.pem (الشهادة الكاملة)")
    
    print("\n🚀 للاستخدام مع Flask:")
    print("export SSL_CERT_PATH=ssl_purchased/fullchain.pem")
    print("export SSL_KEY_PATH=ssl_purchased/ta9affi.key")
    print("python app_with_ssl.py")

if __name__ == '__main__':
    main()
