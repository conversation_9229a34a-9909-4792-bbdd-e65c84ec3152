#!/usr/bin/env python3
"""
Chargily API Client مبسط لتجنب مشاكل SSL مع gevent
"""

import requests
import json
import ssl
import urllib3
from urllib3.util.ssl_ import create_urllib3_context

# تعطيل تحذيرات SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SimpleChargilyClient:
    """عميل Chargily مبسط مع معالجة SSL محسنة"""
    
    def __init__(self, public_key, secret_key, base_url="https://pay.chargily.dz/api/v2"):
        self.public_key = public_key
        self.secret_key = secret_key
        self.base_url = base_url
        
        # إعداد session مع SSL مخصص
        self.session = requests.Session()
        
        # إعداد headers
        self.session.headers.update({
            'Authorization': f'Bearer {secret_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # إعداد SSL context مخصص
        try:
            # إنشاء SSL context آمن
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # إعداد adapter مخصص
            adapter = requests.adapters.HTTPAdapter()
            self.session.mount('https://', adapter)
            
            # تعطيل SSL verification
            self.session.verify = False
            
            print("✅ [SimpleChargily] تم إعداد SSL context")
            
        except Exception as e:
            print(f"⚠️ [SimpleChargily] تحذير في إعداد SSL: {str(e)}")
    
    def _make_request(self, method, endpoint, data=None, max_retries=3):
        """إجراء طلب HTTP مع معالجة أخطاء"""
        url = f"{self.base_url}/{endpoint}"
        
        for attempt in range(max_retries):
            try:
                print(f"🔄 [SimpleChargily] {method} {endpoint} (محاولة {attempt + 1})")
                
                if method.upper() == 'POST':
                    response = self.session.post(url, json=data, timeout=30)
                elif method.upper() == 'GET':
                    response = self.session.get(url, timeout=30)
                else:
                    raise ValueError(f"HTTP method غير مدعوم: {method}")
                
                print(f"📊 [SimpleChargily] Response: {response.status_code}")
                
                if response.status_code in [200, 201]:
                    result = response.json()
                    print(f"✅ [SimpleChargily] نجح {method} {endpoint}")
                    return result
                else:
                    print(f"❌ [SimpleChargily] خطأ HTTP {response.status_code}: {response.text}")
                    if attempt == max_retries - 1:
                        raise Exception(f"HTTP {response.status_code}: {response.text}")
                        
            except (ssl.SSLError, RecursionError) as e:
                print(f"❌ [SimpleChargily] SSL/Recursion Error (محاولة {attempt + 1}): {str(e)[:100]}...")
                if attempt < max_retries - 1:
                    import time
                    time.sleep((attempt + 1) * 2)
                else:
                    raise e
                    
            except Exception as e:
                print(f"❌ [SimpleChargily] خطأ عام (محاولة {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep((attempt + 1) * 2)
                else:
                    raise e
        
        return None
    
    def create_product(self, name, description=""):
        """إنشاء منتج"""
        data = {
            "name": name,
            "description": description
        }
        return self._make_request('POST', 'products', data)
    
    def create_price(self, amount, currency, product_id):
        """إنشاء سعر"""
        data = {
            "amount": int(amount),
            "currency": currency.lower(),
            "product_id": product_id
        }
        return self._make_request('POST', 'prices', data)
    
    def create_checkout(self, items, success_url, failure_url, metadata=None):
        """إنشاء checkout"""
        data = {
            "items": items,
            "success_url": success_url,
            "failure_url": failure_url
        }
        
        if metadata:
            data["metadata"] = metadata
            
        return self._make_request('POST', 'checkouts', data)

def test_simple_chargily():
    """اختبار العميل المبسط"""
    print("🧪 اختبار SimpleChargilyClient...")
    
    # استخدام المفاتيح الحقيقية
    public_key = "live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk"
    secret_key = "live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU"
    
    try:
        client = SimpleChargilyClient(public_key, secret_key)
        
        # اختبار إنشاء منتج
        print("🔄 اختبار إنشاء منتج...")
        product = client.create_product(
            name="اختبار منتج Ta9affi",
            description="منتج اختبار للتأكد من عمل API"
        )
        
        if product:
            print(f"✅ تم إنشاء المنتج: {product.get('id')}")
            return True
        else:
            print("❌ فشل في إنشاء المنتج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == '__main__':
    test_simple_chargily()
