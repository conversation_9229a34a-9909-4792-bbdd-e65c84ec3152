#!/usr/bin/env python3
"""
إصلاح سريع لمشكلة تسجيل الدخول - Ta9affi
"""

import os
from werkzeug.security import generate_password_hash

def quick_fix():
    """إصلاح سريع لمشكلة تسجيل الدخول"""
    
    print("🔧 إصلاح سريع لمشكلة تسجيل الدخول...")
    
    # تعيين متغيرات البيئة
    os.environ['FLASK_ENV'] = 'production'
    os.environ['PRODUCTION_MODE'] = 'true'
    
    try:
        from app import app, db
        from models_new import User, Role
        
        with app.app_context():
            print("✅ تم الاتصال بقاعدة البيانات")
            
            # البحث عن مستخدم admin
            admin_user = User.query.filter_by(username='admin').first()
            
            if not admin_user:
                print("🔄 إنشاء مستخدم admin جديد...")
                
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password=generate_password_hash('admin123'),
                    role=Role.ADMIN,
                    phone_number='0123456789',
                    is_active=True
                )
                
                db.session.add(admin_user)
                db.session.commit()
                
                print("✅ تم إنشاء مستخدم admin")
                print("   - اسم المستخدم: admin")
                print("   - كلمة المرور: admin123")
            
            else:
                print("✅ مستخدم admin موجود")
                
                # التأكد من أن كلمة المرور مشفرة
                if not admin_user.password.startswith('pbkdf2:sha256:'):
                    print("🔄 تشفير كلمة مرور admin...")
                    admin_user.password = generate_password_hash('admin123')
                    db.session.commit()
                    print("✅ تم تشفير كلمة المرور")
                
                # التأكد من أن الحساب نشط
                if not admin_user.is_active:
                    print("🔄 تفعيل حساب admin...")
                    admin_user.is_active = True
                    db.session.commit()
                    print("✅ تم تفعيل الحساب")
            
            # إنشاء مستخدم معلم للاختبار
            teacher_user = User.query.filter_by(username='teacher').first()
            
            if not teacher_user:
                print("🔄 إنشاء مستخدم معلم للاختبار...")
                
                teacher_user = User(
                    username='teacher',
                    email='<EMAIL>',
                    password=generate_password_hash('teacher123'),
                    role=Role.TEACHER,
                    phone_number='0123456789',
                    is_active=True
                )
                
                db.session.add(teacher_user)
                db.session.commit()
                
                print("✅ تم إنشاء مستخدم معلم")
                print("   - اسم المستخدم: teacher")
                print("   - كلمة المرور: teacher123")
            
            # عرض إحصائيات
            total_users = User.query.count()
            active_users = User.query.filter_by(is_active=True).count()
            
            print(f"\n📊 إحصائيات قاعدة البيانات:")
            print(f"   - إجمالي المستخدمين: {total_users}")
            print(f"   - المستخدمين النشطين: {active_users}")
            
            print(f"\n🎯 حسابات الاختبار:")
            print(f"   👤 Admin: admin / admin123")
            print(f"   👨‍🏫 Teacher: teacher / teacher123")
            print(f"   🌐 الرابط: http://ta9affi.com/login")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {str(e)}")
        import traceback
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    if quick_fix():
        print("\n🎉 تم الإصلاح بنجاح!")
        print("\nجرب تسجيل الدخول الآن:")
        print("http://ta9affi.com/login")
    else:
        print("\n❌ فشل في الإصلاح")
