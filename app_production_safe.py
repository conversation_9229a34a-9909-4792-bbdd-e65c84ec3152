"""
تطبيق Ta9affi - إصدار آمن للإنتاج
"""

import os
import sys

# تعيين متغيرات البيئة الافتراضية للإنتاج
if not os.environ.get('FLASK_ENV'):
    os.environ['FLASK_ENV'] = 'production'
if not os.environ.get('PRODUCTION_MODE'):
    os.environ['PRODUCTION_MODE'] = 'true'
if not os.environ.get('BASE_URL'):
    os.environ['BASE_URL'] = 'http://ta9affi.com'

print(f"🌍 البيئة: {os.environ.get('FLASK_ENV')}")
print(f"🔗 Base URL: {os.environ.get('BASE_URL')}")

try:
    # استيراد التطبيق الأساسي
    from app import app
    print("✅ تم تحميل التطبيق بنجاح")
    
    # تشغيل التطبيق
    if __name__ == '__main__':
        port = int(os.environ.get('PORT', 8000))
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False
        )
        
except Exception as e:
    print(f"❌ خطأ في تحميل التطبيق: {str(e)}")
    import traceback
    print(f"📋 التفاصيل: {traceback.format_exc()}")
    
    # إنشاء تطبيق بسيط للتشخيص
    from flask import Flask, jsonify
    
    app = Flask(__name__)
    
    @app.route('/')
    def index():
        return jsonify({
            'status': 'error',
            'message': 'خطأ في تحميل التطبيق الرئيسي',
            'error': str(e),
            'environment': os.environ.get('FLASK_ENV'),
            'base_url': os.environ.get('BASE_URL')
        })
    
    @app.route('/health')
    def health():
        return jsonify({
            'status': 'unhealthy',
            'message': 'التطبيق الرئيسي لا يعمل',
            'timestamp': str(datetime.now())
        })
    
    if __name__ == '__main__':
        port = int(os.environ.get('PORT', 8000))
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False
        )
