{% extends 'base.html' %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-test-tube me-2"></i>
                        اختبار Chargily Checkout
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>هذه صفحة اختبار</strong> لتجربة إنشاء checkout من Chargily في البيئة المحلية والإنتاجية
                    </div>

                    <!-- معلومات البيئة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-card">
                                <h5><i class="fas fa-server me-2"></i>معلومات البيئة</h5>
                                <p><strong>البيئة:</strong> <span id="environment">جاري التحميل...</span></p>
                                <p><strong>المستخدم:</strong> {{ current_user.username }}</p>
                                <p><strong>نوع العضوية:</strong> {{ current_user.role.value }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-card">
                                <h5><i class="fas fa-credit-card me-2"></i>معلومات Chargily</h5>
                                <p><strong>نوع العميل:</strong> <span id="chargily-type">جاري التحميل...</span></p>
                                <p><strong>حالة الاتصال:</strong> <span id="connection-status">جاري الفحص...</span></p>
                            </div>
                        </div>
                    </div>

                    <!-- زر الاختبار -->
                    <div class="text-center mb-4">
                        <button id="test-checkout-btn" class="btn btn-primary btn-lg">
                            <i class="fas fa-play me-2"></i>
                            اختبار إنشاء Checkout
                        </button>
                    </div>

                    <!-- نتائج الاختبار -->
                    <div id="test-results" class="d-none">
                        <h5><i class="fas fa-clipboard-list me-2"></i>نتائج الاختبار</h5>
                        <div id="results-content"></div>
                    </div>

                    <!-- سجل العمليات -->
                    <div id="test-log" class="mt-4">
                        <h5><i class="fas fa-list me-2"></i>سجل العمليات</h5>
                        <div id="log-content" class="log-container">
                            <p class="text-muted">سيتم عرض سجل العمليات هنا...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    border-left: 4px solid #007bff;
    height: 100%;
}

.log-container {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #4a5568;
}

.log-entry:last-child {
    border-bottom: none;
}

.success-result {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #c3e6cb;
    border-radius: 10px;
    padding: 1.5rem;
}

.error-result {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #f5c6cb;
    border-radius: 10px;
    padding: 1.5rem;
}

.checkout-link {
    background: #007bff;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    display: inline-block;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.checkout-link:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const testBtn = document.getElementById('test-checkout-btn');
    const resultsDiv = document.getElementById('test-results');
    const resultsContent = document.getElementById('results-content');
    const logContent = document.getElementById('log-content');
    
    function addLogEntry(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        let icon = '📋';
        if (type === 'success') icon = '✅';
        else if (type === 'error') icon = '❌';
        else if (type === 'warning') icon = '⚠️';
        
        logEntry.innerHTML = `<span style="color: #a0aec0;">[${timestamp}]</span> ${icon} ${message}`;
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
    }
    
    // تنظيف السجل
    logContent.innerHTML = '';
    addLogEntry('تم تحميل صفحة الاختبار');
    
    testBtn.addEventListener('click', function() {
        // تعطيل الزر وإظهار loading
        testBtn.disabled = true;
        testBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الاختبار...';
        
        addLogEntry('بدء اختبار إنشاء checkout...', 'info');
        
        // إجراء الطلب
        fetch('/test-chargily-checkout')
            .then(response => {
                addLogEntry(`استجابة الخادم: ${response.status}`, response.ok ? 'success' : 'error');
                return response.json();
            })
            .then(data => {
                // إعادة تفعيل الزر
                testBtn.disabled = false;
                testBtn.innerHTML = '<i class="fas fa-play me-2"></i>اختبار إنشاء Checkout';
                
                // إظهار النتائج
                resultsDiv.classList.remove('d-none');
                
                if (data.success) {
                    addLogEntry('تم إنشاء checkout بنجاح!', 'success');
                    
                    resultsContent.innerHTML = `
                        <div class="success-result">
                            <h6><i class="fas fa-check-circle me-2 text-success"></i>نجح الاختبار!</h6>
                            <p><strong>رسالة:</strong> ${data.message}</p>
                            <p><strong>معرف Checkout:</strong> <code>${data.checkout_id || 'غير متاح'}</code></p>
                            <p><strong>اسم الباقة:</strong> ${data.plan_name}</p>
                            <p><strong>سعر الباقة:</strong> ${data.plan_price} دج</p>
                            <p><strong>البيئة:</strong> ${data.environment}</p>
                            <p><strong>نوع Chargily:</strong> ${data.chargily_type}</p>
                            
                            ${data.checkout_url ? `
                                <a href="${data.checkout_url}" target="_blank" class="checkout-link">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    فتح صفحة الدفع
                                </a>
                            ` : ''}
                        </div>
                    `;
                    
                    // تحديث معلومات البيئة
                    document.getElementById('environment').textContent = data.environment;
                    document.getElementById('chargily-type').textContent = data.chargily_type === 'real' ? 'عميل حقيقي' : 'عميل محاكي';
                    document.getElementById('connection-status').textContent = 'متصل';
                    
                } else {
                    addLogEntry(`فشل الاختبار: ${data.error}`, 'error');
                    
                    resultsContent.innerHTML = `
                        <div class="error-result">
                            <h6><i class="fas fa-exclamation-triangle me-2 text-danger"></i>فشل الاختبار</h6>
                            <p><strong>خطأ:</strong> ${data.error}</p>
                            ${data.details ? `<p><strong>تفاصيل:</strong> <pre>${JSON.stringify(data.details, null, 2)}</pre></p>` : ''}
                            ${data.traceback ? `<details><summary>تفاصيل تقنية</summary><pre>${data.traceback}</pre></details>` : ''}
                        </div>
                    `;
                    
                    document.getElementById('connection-status').textContent = 'خطأ في الاتصال';
                }
            })
            .catch(error => {
                addLogEntry(`خطأ في الطلب: ${error.message}`, 'error');
                
                testBtn.disabled = false;
                testBtn.innerHTML = '<i class="fas fa-play me-2"></i>اختبار إنشاء Checkout';
                
                resultsDiv.classList.remove('d-none');
                resultsContent.innerHTML = `
                    <div class="error-result">
                        <h6><i class="fas fa-exclamation-triangle me-2 text-danger"></i>خطأ في الشبكة</h6>
                        <p><strong>خطأ:</strong> ${error.message}</p>
                    </div>
                `;
                
                document.getElementById('connection-status').textContent = 'خطأ في الشبكة';
            });
    });
});
</script>
{% endblock %}
