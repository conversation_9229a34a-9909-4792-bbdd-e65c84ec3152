# ملف البيئة للتشغيل المحلي - Ta9affi
# إعدادات مبسطة للتطوير والاختبار المحلي

# ===== إعدادات التطبيق الأساسية =====
FLASK_ENV=development
SECRET_KEY=local-development-secret-key-change-in-production
APP_NAME=Ta9affi
APP_VERSION=1.0.0
DEBUG=True

# ===== إعدادات قاعدة البيانات =====
DB_HOST=localhost
DB_PORT=5433
DB_NAME=ta9affi_local
DB_USER=ta9affi_user
DB_PASSWORD=ta9affi_local_password

# رابط قاعدة البيانات الكامل
DATABASE_URL=postgresql://ta9affi_user:ta9affi_local_password@localhost:5433/ta9affi_local

# ===== إعدادات Redis =====
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6380/0

# ===== إعدادات البريد الإلكتروني (للاختبار) =====
MAIL_SERVER=localhost
MAIL_PORT=1025
MAIL_USE_TLS=False
MAIL_USE_SSL=False
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_DEFAULT_SENDER=<EMAIL>

# إعدادات البريد للتنبيهات
ALERT_FROM_EMAIL=<EMAIL>
ALERT_TO_EMAILS=<EMAIL>

# ===== إعدادات الأمان (مبسطة للتطوير) =====
ENCRYPTION_KEY=local-encryption-key-32-chars!!
WTF_CSRF_ENABLED=False
SESSION_TIMEOUT=7200
PERMANENT_SESSION_LIFETIME=86400
PASSWORD_MIN_LENGTH=6
MAX_LOGIN_ATTEMPTS=10
LOCKOUT_DURATION=300

# ===== إعدادات التخزين السحابي (معطلة محلياً) =====
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
AWS_BACKUP_BUCKET=

# ===== إعدادات النسخ الاحتياطي =====
BACKUP_DIR=backups
MAX_LOCAL_BACKUPS=3
MAX_REMOTE_BACKUPS=0
BACKUP_CLOUD_PROVIDER=

# ===== إعدادات المراقبة =====
GRAFANA_PASSWORD=admin123
PROMETHEUS_RETENTION=24h
ENABLE_MONITORING=True
MONITORING_INTERVAL=60

# ===== إعدادات الأداء (محسنة للتطوير) =====
WORKERS=4
WORKER_CLASS=gevent
WORKER_CONNECTIONS=500
MAX_REQUESTS=1000
TIMEOUT=120

# Redis Cache
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=ta9affi_local:

# ===== إعدادات الملفات =====
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx,txt

# ===== إعدادات SSL/TLS (معطلة محلياً) =====
SSL_DISABLE=True
PREFERRED_URL_SCHEME=http

# ===== إعدادات الشبكة =====
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:5000,http://127.0.0.1:5000

# ===== إعدادات Rate Limiting (مخففة) =====
RATELIMIT_STORAGE_URL=redis://localhost:6380/1
RATELIMIT_DEFAULT=10000 per hour

# ===== إعدادات Celery =====
CELERY_BROKER_URL=redis://localhost:6380/2
CELERY_RESULT_BACKEND=redis://localhost:6380/3
CELERY_TASK_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=Africa/Algiers

# ===== إعدادات السجلات =====
LOG_LEVEL=DEBUG
LOG_FILE=logs/ta9affi_local.log
LOG_MAX_BYTES=5242880  # 5MB
LOG_BACKUP_COUNT=3

# إعدادات Elasticsearch (معطلة محلياً)
ELASTICSEARCH_URL=
ENABLE_ELASTICSEARCH_LOGGING=False

# ===== إعدادات التطوير =====
TESTING=False
WTF_CSRF_ENABLED=False
DEVELOPER_MODE=True
MAINTENANCE_MODE=False

# ===== إعدادات قاعدة البيانات المحلية (محسنة) =====
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# ===== إعدادات الضغط (معطلة للتطوير) =====
COMPRESS_MIMETYPES=
COMPRESS_LEVEL=1
COMPRESS_MIN_SIZE=1000

# ===== إعدادات التخزين المؤقت =====
CACHE_REDIS_HOST=localhost
CACHE_REDIS_PORT=6380
CACHE_REDIS_DB=4
CACHE_REDIS_PASSWORD=

PAGE_CACHE_TIMEOUT=60
API_CACHE_TIMEOUT=30

# ===== إعدادات الصحة والمراقبة =====
HEALTH_CHECK_ENABLED=True
HEALTH_CHECK_PATH=/health
METRICS_ENABLED=True
METRICS_PATH=/metrics

# ===== إعدادات البيئة المحددة =====
ENVIRONMENT=local
DEPLOYMENT_DATE=2024-01-01
VERSION_HASH=local-development

# ===== إعدادات التحسين (معطلة للتطوير) =====
ENABLE_GZIP=False
ENABLE_BROTLI=False
STATIC_FILE_CACHE_TIMEOUT=0
