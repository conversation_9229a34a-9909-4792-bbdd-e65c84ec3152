#!/usr/bin/env python3
"""
سكريبت تشخيص أخطاء الخادم - Ta9affi
"""

import os
import sys
import traceback

def test_imports():
    """اختبار جميع الاستيرادات المطلوبة"""
    print("🧪 اختبار الاستيرادات...")
    
    try:
        print("1. اختبار Flask...")
        from flask import Flask
        print("   ✅ Flask")
        
        print("2. اختبار config...")
        from config import get_config
        print("   ✅ config")
        
        print("3. اختبار models_new...")
        from models_new import db, User, Role
        print("   ✅ models_new")
        
        print("4. اختبار subscription_manager...")
        from subscription_manager import SubscriptionManager
        print("   ✅ subscription_manager")
        
        print("5. اختبار chargily_pay...")
        from chargily_pay import ChargilyClient
        print("   ✅ chargily_pay")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        print(f"📋 التفاصيل: {traceback.format_exc()}")
        return False

def test_environment_setup():
    """اختبار إعداد البيئة"""
    print("\n🌍 اختبار إعداد البيئة...")
    
    # عرض متغيرات البيئة المهمة
    env_vars = [
        'FLASK_ENV',
        'PRODUCTION_MODE', 
        'BASE_URL',
        'CHARGILY_PUBLIC_KEY',
        'CHARGILY_SECRET_KEY',
        'CHARGILY_WEBHOOK_URL'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, 'غير محدد')
        if 'KEY' in var and value != 'غير محدد':
            value = value[:20] + "..."  # إخفاء المفاتيح
        print(f"   {var}: {value}")

def test_subscription_manager():
    """اختبار SubscriptionManager"""
    print("\n🔧 اختبار SubscriptionManager...")
    
    try:
        from subscription_manager import SubscriptionManager
        
        # إنشاء مثيل
        manager = SubscriptionManager()
        print("   ✅ تم إنشاء SubscriptionManager")
        
        # اختبار get_payment_urls
        success_url, failure_url = manager.get_payment_urls()
        print(f"   ✅ Success URL: {success_url}")
        print(f"   ✅ Failure URL: {failure_url}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في SubscriptionManager: {str(e)}")
        print(f"   📋 التفاصيل: {traceback.format_exc()}")
        return False

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    print("\n🚀 اختبار إنشاء التطبيق...")
    
    try:
        # تعيين متغيرات البيئة للإنتاج
        os.environ['FLASK_ENV'] = 'production'
        os.environ['PRODUCTION_MODE'] = 'true'
        os.environ['BASE_URL'] = 'http://ta9affi.com'
        
        from flask import Flask
        from config import get_config
        
        app = Flask(__name__)
        
        # تحميل الإعدادات
        config_class = get_config()
        app.config.from_object(config_class)
        
        print("   ✅ تم إنشاء التطبيق بنجاح")
        print(f"   📊 إعدادات قاعدة البيانات: {app.config.get('SQLALCHEMY_DATABASE_URI', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء التطبيق: {str(e)}")
        print(f"   📋 التفاصيل: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص أخطاء الخادم - Ta9affi")
    print("=" * 50)
    
    tests = [
        ("الاستيرادات", test_imports),
        ("إعداد البيئة", test_environment_setup),
        ("SubscriptionManager", test_subscription_manager),
        ("إنشاء التطبيق", test_app_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ غير متوقع - {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n💡 إذا كان الخادم لا يزال يعطي خطأ، تحقق من:")
        print("1. logs الخادم في dokploy")
        print("2. متغيرات البيئة في dokploy")
        print("3. حالة قاعدة البيانات")
    else:
        print("❌ بعض الاختبارات فشلت - راجع الأخطاء أعلاه")
        
        print("\n🔧 خطوات الإصلاح المقترحة:")
        print("1. تحقق من requirements.txt")
        print("2. تأكد من وجود جميع الملفات المطلوبة")
        print("3. راجع متغيرات البيئة في dokploy")

if __name__ == '__main__':
    main()
