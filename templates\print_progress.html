<!DOCTYPE html>
<html lang="ar" dir="rtl">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>طباعة التقدمات - {{ selected_date }}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            @media print {
                .no-print {
                    display: none !important;
                }

                body {
                    font-size: 14px !important;
                    line-height: 1.3 !important;
                    color: #000 !important;
                    background: white !important;
                }

                .table {
                    font-size: 13px !important;
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-bottom: 12px !important;
                }

                .table th,
                .table td {
                    padding: 5px 3px !important;
                    line-height: 1.2 !important;
                    border: 1px solid #666 !important;
                    vertical-align: middle !important;
                    text-align: center !important;
                    white-space: nowrap !important;
                    color: #000 !important;
                }

                .table th {
                    background-color: #e8e8e8 !important;
                    font-weight: bold !important;
                    color: #000 !important;
                    border: 1px solid #666 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .table tbody tr {
                    page-break-inside: avoid !important;
                }

                .page-break {
                    page-break-before: always;
                }

                @page {
                    margin: 0.8cm 0.6cm !important;
                    size: A4 landscape !important;
                }

                .print-container {
                    max-width: none !important;
                    width: 100% !important;
                    margin: 0 !important;
                    padding: 8px !important;
                    box-shadow: none !important;
                }

                .header-section {
                    margin-bottom: 10px !important;
                    padding-bottom: 8px !important;
                    border-bottom: 2px solid #666 !important;
                }

                .header-section h1 {
                    font-size: 18px !important;
                    margin-bottom: 6px !important;
                    color: #333 !important;
                }

                .header-section h3 {
                    font-size: 14px !important;
                    color: #555 !important;
                }

                .info-section {
                    margin-bottom: 8px !important;
                    padding: 6px !important;
                    background-color: #f8f8f8 !important;
                    border: 1px solid #999 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .competency-cell {
                    max-width: 250px !important;
                    word-wrap: break-word !important;
                    white-space: normal !important;
                    font-size: 12px !important;
                    text-align: right !important;
                    padding: 5px !important;
                }

                .level-cell {
                    white-space: nowrap !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                    max-width: 80px !important;
                }

                .table-responsive {
                    overflow: visible !important;
                }

                .badge {
                    font-size: 10px !important;
                    padding: 3px 6px !important;
                    border-radius: 3px !important;
                    color: #000 !important;
                    border: 1px solid #000 !important;
                    background-color: #f0f0f0 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .summary-compact {
                    padding: 6px !important;
                    margin-bottom: 6px !important;
                    border: 1px solid #666 !important;
                    background-color: #f5f5f5 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .summary-compact h6 {
                    font-size: 13px !important;
                    margin-bottom: 4px !important;
                    color: #333 !important;
                }

                .summary-item {
                    margin: 0 6px !important;
                    font-size: 12px !important;
                }

                .summary-item .badge {
                    font-size: 11px !important;
                }

                .summary-item small {
                    font-size: 11px !important;
                }

                .footer-section {
                    margin-top: 8px !important;
                    font-size: 12px !important;
                    color: #333 !important;
                }

                /* إصلاح مشكلة عرض الأسطر - ألوان رمادية لتوفير الحبر */
                .table tbody tr:nth-child(even) {
                    background-color: #f5f5f5 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .table tbody tr:nth-child(odd) {
                    background-color: white !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                /* التأكد من عرض جميع الأسطر */
                .table tbody tr {
                    display: table-row !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    height: auto !important;
                    min-height: 22px !important;
                }

                .table tbody tr td {
                    display: table-cell !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }

                /* إصلاح مشكلة الأسطر المخفية */
                .progress-row {
                    display: table-row !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }

                /* تحسين عرض الحالات - ألوان رمادية لتوفير الحبر */
                .status-badge {
                    display: inline-block !important;
                    padding: 3px 6px !important;
                    border: 1px solid #666 !important;
                    background-color: #e8e8e8 !important;
                    color: #000 !important;
                    font-size: 11px !important;
                    font-weight: bold !important;
                }

                /* ضمان أن جميع النصوص داخل الجدول سوداء */
                .table tbody td,
                .table thead th {
                    color: #000 !important;
                }

                .table tbody td * {
                    color: #000 !important;
                }

                /* تحسين استغلال المساحة */
                .table-container {
                    margin: 0 !important;
                    padding: 0 !important;
                }

                /* تقليل المسافات للحصول على مساحة أكبر */
                .row {
                    margin: 0 !important;
                }

                .col-md-4 {
                    padding: 2px !important;
                }

                /* إزالة أي تأثيرات قد تخفي الأسطر - ألوان رمادية فاتحة */
                .table-striped tbody tr:nth-of-type(odd) {
                    background-color: #f8f8f8 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .table-striped tbody tr:nth-of-type(even) {
                    background-color: white !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                /* التأكد من عدم وجود overflow مخفي */
                * {
                    overflow: visible !important;
                }
            }

            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background-color: #f8f9fa;
            }

            .print-container {
                background: white;
                padding: 30px;
                margin: 20px auto;
                max-width: 1000px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }

            .header-section {
                text-align: center;
                border-bottom: 3px solid #007bff;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }

            .info-section {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
            }

            .table th {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                text-align: center;
                vertical-align: middle;
            }

            .table td {
                vertical-align: middle;
                text-align: center;
            }

            .status-completed {
                background-color: #d4edda;
                color: #155724;
            }

            .status-in-progress {
                background-color: #fff3cd;
                color: #856404;
            }

            .status-planned {
                background-color: #f8d7da;
                color: #721c24;
            }

            .footer-section {
                margin-top: 40px;
                text-align: center;
                color: #6c757d;
                font-size: 0.9em;
            }

            /* تحسين عرض الكفاءات في الشاشة العادية */
            .competency-cell {
                word-wrap: break-word;
                white-space: normal;
                max-width: 300px;
            }

            .table-responsive {
                overflow-x: auto;
            }

            /* تصميم مضغوط للملخص */
            .summary-compact {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                text-align: center;
            }

            .summary-item {
                display: inline-flex;
                align-items: center;
                margin: 0 10px;
            }

            .gap-4 {
                gap: 1.5rem !important;
            }

            /* إظهار الأزرار في الشاشة العادية فقط */
            @media screen {
                .no-print {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
            }
        </style>
    </head>

    <body>
        <div class="print-container">
            <!-- Header Section -->
            <div class="header-section">
                <h1 class="mb-3">
                    <i class="fas fa-graduation-cap text-primary"></i>
                    تقرير التقدمات اليومية
                </h1>
                <h3 class="text-muted">نظام تقفي للمتابعة التربوية</h3>
            </div>

            <!-- Info Section -->
            <div class="info-section">
                <div class="row">
                    <div class="col-md-4">
                        <strong>
                            <i class="fas fa-user text-primary"></i>
                            اسم الأستاذ:
                        </strong>
                        <span>{{ teacher_name }}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>
                            <i class="fas fa-calendar text-primary"></i>
                            التاريخ:
                        </strong>
                        <span>{{ selected_date }}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>
                            <i class="fas fa-list-ol text-primary"></i>
                            عدد التقدمات:
                        </strong>
                        <span>{{ total_entries }}</span>
                    </div>
                </div>
            </div>

            <!-- Print Button -->
            <div class="text-center mb-4 no-print">
                <button onclick="window.print()" class="btn btn-primary btn-lg">
                    <i class="fas fa-print me-2"></i>
                    طباعة التقرير
                </button>
                <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-secondary btn-lg ms-3">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>

            <!-- Progress Table -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th style="width: 4%;">#</th>
                            <th style="width: 12%;">المستوى</th>
                            <th style="width: 15%;">المادة الدراسية</th>
                            <th style="width: 17%;">الميدان/النشاط</th>
                            <th style="width: 17%;">الموارد المعرفية</th>
                            <th style="width: 25%;">الكفاءة المستهدفة</th>
                            <th style="width: 10%;">الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in progress_data %}
                        <tr class="progress-row {% if item.entry.status == 'completed' %}status-completed{% elif item.entry.status == 'in_progress' %}status-in-progress{% elif item.entry.status == 'planned' %}status-planned{% endif %}"
                            style="page-break-inside: avoid; height: auto; min-height: 22px;">
                            <td style="font-weight: bold; text-align: center; vertical-align: middle;">{{ loop.index }}
                            </td>
                            <td class="level-cell" style="text-align: center; vertical-align: middle;">{{
                                item.entry.level.name if
                                item.entry.level else 'غير محدد' }}</td>
                            <td style="text-align: center; vertical-align: middle;">{{ item.entry.subject.name if
                                item.entry.subject else 'غير محدد' }}</td>
                            <td style="text-align: center; vertical-align: middle;">{{ item.entry.domain.name if
                                item.entry.domain else 'غير محدد' }}</td>
                            <td style="text-align: center; vertical-align: middle;">{{ item.entry.material.name if
                                item.entry.material else 'غير محدد' }}</td>
                            <td class="competency-cell"
                                style="text-align: right; padding: 5px; vertical-align: middle; word-wrap: break-word;">
                                {{ item.competency_name }}
                            </td>
                            <td style="text-align: center; vertical-align: middle;">
                                <span class="badge status-badge">{{ item.status_text }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Summary Section - Compact Horizontal Layout -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="summary-compact">
                        <h6 class="mb-2">
                            <i class="fas fa-chart-pie text-primary"></i>
                            ملخص التقدمات:
                        </h6>
                        <div class="d-flex justify-content-center gap-4">
                            {% set completed_count = progress_data|selectattr('entry.status', 'eq',
                            'completed')|list|length %}
                            {% set in_progress_count = progress_data|selectattr('entry.status', 'eq',
                            'in_progress')|list|length %}
                            {% set planned_count = progress_data|selectattr('entry.status', 'eq', 'planned')|list|length
                            %}

                            <div class="summary-item">
                                <span class="badge bg-success fs-6">{{ completed_count }}</span>
                                <small class="text-success ms-1">مكتملة</small>
                            </div>
                            <div class="summary-item">
                                <span class="badge bg-warning fs-6">{{ in_progress_count }}</span>
                                <small class="text-warning ms-1">قيد التنفيذ</small>
                            </div>
                            <div class="summary-item">
                                <span class="badge bg-danger fs-6">{{ planned_count }}</span>
                                <small class="text-danger ms-1">مخططة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Section -->
            <div class="footer-section">
                <hr>
                <p class="mb-1">
                    <i class="fas fa-calendar-alt"></i>
                    تم إنشاء هذا التقرير في: <span id="current-datetime"></span>
                </p>
                <p class="mb-0">
                    <i class="fas fa-laptop"></i>
                    نظام تقفي للمتابعة التربوية - جميع الحقوق محفوظة
                </p>
            </div>
        </div>

        <script>
            // تلقائياً فتح نافذة الطباعة عند تحميل الصفحة
            window.addEventListener('load', function () {
                // إظهار التاريخ والوقت الحالي
                const now = new Date();
                const dateTimeString = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0') + ':' +
                    String(now.getSeconds()).padStart(2, '0');

                const datetimeElement = document.getElementById('current-datetime');
                if (datetimeElement) {
                    datetimeElement.textContent = dateTimeString;
                }

                // التأكد من عرض جميع الأسطر
                const tableRows = document.querySelectorAll('.progress-row');
                tableRows.forEach((row, index) => {
                    row.style.display = 'table-row';
                    row.style.visibility = 'visible';
                    row.style.opacity = '1';

                    // إضافة تلوين متناوب للأسطر - ألوان رمادية لتوفير الحبر
                    if (index % 2 === 0) {
                        row.style.backgroundColor = '#f5f5f5';
                    } else {
                        row.style.backgroundColor = 'white';
                    }
                });

                // فتح نافذة الطباعة بعد تحميل كامل للصفحة
                setTimeout(function () {
                    window.print();
                }, 500);
            });

            // التأكد من إعدادات الطباعة الصحيحة
            window.addEventListener('beforeprint', function () {
                // التأكد من عرض جميع الأسطر قبل الطباعة
                const tableRows = document.querySelectorAll('.progress-row');
                tableRows.forEach(row => {
                    row.style.display = 'table-row !important';
                    row.style.visibility = 'visible !important';
                    row.style.opacity = '1 !important';
                });
            });
        </script>
    </body>

</html>