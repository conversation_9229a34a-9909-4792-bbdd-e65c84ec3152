#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة Ta9affi الكاملة مع جميع الملفات المطلوبة
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_dockerfile():
    """إنشاء Dockerfile محسن"""
    dockerfile_content = '''# استخدام Python 3.11 slim
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV FLASK_APP=app.py
ENV FLASK_ENV=production

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \\
    gcc \\
    libpq-dev \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# ترقية pip
RUN pip install --upgrade pip

# نسخ ملف المتطلبات أولاً (للاستفادة من Docker cache)
COPY requirements_production.txt .

# تثبيت متطلبات Python
RUN pip install --no-cache-dir -r requirements_production.txt

# نسخ جميع ملفات التطبيق
COPY . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p uploads logs static/uploads instance

# تعيين الصلاحيات
RUN chmod -R 755 /app

# فتح المنفذ
EXPOSE 5000

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \\
    CMD curl -f http://localhost:5000/health || exit 1

# تشغيل التطبيق
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "--timeout", "30", "app:app"]
'''
    return dockerfile_content

def create_dockerignore():
    """إنشاء .dockerignore"""
    dockerignore_content = '''__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git/
.mypy_cache/
.pytest_cache/
.hypothesis/
.DS_Store
*.sqlite
*.db
node_modules/
.env
.env.local
.env.production
.env.development
backup_*/
database_backup/
create_ta9affi_complete.py
create_complete_package.py
create_package.bat
*.tar.gz
*.zip
*.rar
'''
    return dockerignore_content

def create_requirements_production():
    """إنشاء requirements_production.txt مبسط"""
    requirements_content = '''Flask==3.0.0
Flask-SQLAlchemy==3.1.1
Flask-Login==0.6.3
Flask-WTF==1.2.1
WTForms==3.1.1
gunicorn==21.2.0
pandas==2.1.4
openpyxl==3.1.2
requests==2.31.0
python-dateutil==2.8.2
Werkzeug==3.0.1
bcrypt==4.1.2
python-magic==0.4.27
six==1.16.0
certifi==2023.11.17
charset-normalizer==3.3.2
idna==3.6
'''
    return requirements_content

def create_simple_app():
    """إنشاء app.py مبسط للاختبار"""
    app_content = '''# -*- coding: utf-8 -*-
"""
Ta9affi - نظام إدارة التقدم التعليمي (النسخة المبسطة للنشر)
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)

# الإعدادات
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'ta9affi-secret-2024')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///ta9affi.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# نموذج المستخدم المبسط
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='student')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_id(self):
        return str(self.id)
    
    def is_authenticated(self):
        return True
    
    def is_active(self):
        return True
    
    def is_anonymous(self):
        return False

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# فحص صحة التطبيق
@app.route('/health')
def health():
    return jsonify({
        "status": "healthy",
        "app": "Ta9affi",
        "version": "1.0",
        "timestamp": datetime.utcnow().isoformat()
    })

# معالج الأخطاء
@app.errorhandler(404)
def not_found(error):
    return '<h1>404 - الصفحة غير موجودة</h1>', 404

@app.errorhandler(500)
def internal_error(error):
    return '<h1>500 - خطأ في الخادم</h1>', 500

# إنشاء الجداول
with app.app_context():
    try:
        os.makedirs('instance', exist_ok=True)
        os.makedirs('uploads', exist_ok=True)
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء الجداول: {e}")

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5000)
'''
    return app_content

def create_index_template():
    """إنشاء قالب index.html"""
    template_content = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ta9affi - نظام إدارة التقدم التعليمي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        .success {
            color: #27ae60;
            font-size: 1.2em;
            margin: 20px 0;
            font-weight: bold;
        }
        .description {
            color: #7f8c8d;
            margin: 20px 0;
            font-size: 1.1em;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: background 0.3s;
            font-weight: bold;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.success:hover {
            background: #229954;
        }
        .footer {
            margin-top: 30px;
            color: #95a5a6;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Ta9affi</h1>
        <div class="success">✅ تم نشر التطبيق بنجاح!</div>
        <div class="description">
            نظام إدارة التقدم التعليمي<br>
            مرحباً بك في منصة Ta9affi التعليمية
        </div>
        
        <div style="margin: 30px 0;">
            <a href="/health" class="btn success">فحص صحة النظام</a>
            <a href="/admin" class="btn">لوحة الإدارة</a>
        </div>
        
        <div class="footer">
            Ta9affi v1.0 - نظام إدارة التقدم التعليمي<br>
            تم النشر بنجاح على Dokploy
        </div>
    </div>
</body>
</html>
'''
    return template_content

def get_essential_files():
    """قائمة الملفات الأساسية المطلوبة"""
    return [
        'app.py',
        'models_new.py',
        'config.py',
        'requirements.txt',
        'gunicorn.conf.py',
    ]

def get_essential_dirs():
    """قائمة المجلدات الأساسية"""
    return [
        'templates',
        'static',
        'instance'
    ]

def create_complete_package():
    """إنشاء الحزمة الكاملة"""
    
    print("📦 إنشاء حزمة Ta9affi الكاملة...")
    
    # إنشاء مجلد مؤقت
    temp_dir = "ta9affi_complete_package"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    try:
        # إنشاء الملفات الجديدة
        print("📝 إنشاء ملفات Docker...")
        
        # Dockerfile
        with open(os.path.join(temp_dir, 'Dockerfile'), 'w', encoding='utf-8') as f:
            f.write(create_dockerfile())
        
        # .dockerignore
        with open(os.path.join(temp_dir, '.dockerignore'), 'w', encoding='utf-8') as f:
            f.write(create_dockerignore())
        
        # requirements_production.txt
        with open(os.path.join(temp_dir, 'requirements_production.txt'), 'w', encoding='utf-8') as f:
            f.write(create_requirements_production())
        
        # app.py مبسط (إذا لم يكن موجود)
        if not os.path.exists('app.py'):
            with open(os.path.join(temp_dir, 'app.py'), 'w', encoding='utf-8') as f:
                f.write(create_simple_app())
        
        # نسخ الملفات الموجودة
        print("📁 نسخ الملفات الموجودة...")
        
        essential_files = get_essential_files()
        for file_name in essential_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, temp_dir)
                print(f"✅ نسخ: {file_name}")
        
        # نسخ المجلدات
        essential_dirs = get_essential_dirs()
        for dir_name in essential_dirs:
            if os.path.exists(dir_name):
                shutil.copytree(dir_name, os.path.join(temp_dir, dir_name))
                print(f"✅ نسخ مجلد: {dir_name}")
        
        # إنشاء مجلد templates إذا لم يكن موجود
        templates_dir = os.path.join(temp_dir, 'templates')
        if not os.path.exists(templates_dir):
            os.makedirs(templates_dir)
            with open(os.path.join(templates_dir, 'index.html'), 'w', encoding='utf-8') as f:
                f.write(create_index_template())
            print("✅ إنشاء قالب index.html")
        
        # إنشاء مجلد static إذا لم يكن موجود
        static_dir = os.path.join(temp_dir, 'static')
        if not os.path.exists(static_dir):
            os.makedirs(static_dir)
            os.makedirs(os.path.join(static_dir, 'css'))
            os.makedirs(os.path.join(static_dir, 'js'))
            os.makedirs(os.path.join(static_dir, 'images'))
            print("✅ إنشاء مجلد static")
        
        # إنشاء مجلد instance
        instance_dir = os.path.join(temp_dir, 'instance')
        if not os.path.exists(instance_dir):
            os.makedirs(instance_dir)
            print("✅ إنشاء مجلد instance")
        
        # إنشاء الحزمة المضغوطة
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        zip_name = f"ta9affi_complete_docker_{timestamp}.zip"
        
        print(f"🗜️ إنشاء الحزمة المضغوطة: {zip_name}")
        
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, temp_dir)
                    zipf.write(file_path, arc_name)
        
        # حساب حجم الملف
        size = os.path.getsize(zip_name)
        size_mb = size / (1024 * 1024)
        
        print(f"\n🎉 تم إنشاء الحزمة بنجاح!")
        print(f"📦 اسم الملف: {zip_name}")
        print(f"📊 الحجم: {size_mb:.1f} MB")
        
        # عرض محتويات الحزمة
        print(f"\n📋 محتويات الحزمة:")
        with zipfile.ZipFile(zip_name, 'r') as zipf:
            for name in sorted(zipf.namelist()):
                print(f"   {name}")
        
        # تنظيف المجلد المؤقت
        shutil.rmtree(temp_dir)
        
        print(f"\n🚀 تعليمات النشر:")
        print(f"1. ارفع {zip_name} إلى GitHub")
        print(f"2. في Dokploy:")
        print(f"   - Build Type: Dockerfile")
        print(f"   - Repository: رابط GitHub الجديد")
        print(f"   - Environment Variables:")
        print(f"     FLASK_ENV=production")
        print(f"     SECRET_KEY=ta9affi-secret-2024")
        print(f"3. اضغط Deploy")
        
        return zip_name
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحزمة: {e}")
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return None

if __name__ == "__main__":
    print("🚀 إنشاء حزمة Ta9affi الكاملة مع Docker")
    print("=" * 60)
    
    package_name = create_complete_package()
    
    if package_name:
        print(f"\n✅ تم إنشاء الحزمة: {package_name}")
        print(f"🎯 جاهز للنشر على Dokploy!")
    else:
        print(f"\n❌ فشل في إنشاء الحزمة")
