{% extends "base.html" %}

{% block title %}قائمة المستخدمين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-users me-2 text-primary"></i>
                        قائمة المستخدمين
                    </h2>
                    <p class="text-muted mb-0">إدارة وعرض جميع المستخدمين</p>
                </div>
                <div>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <!-- بطاقة المستخدمين المتصلين -->
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1" id="online-users-count">{{ stats.online }}</h4>
                    <small class="text-muted">متصل الآن</small>
                </div>
            </div>
        </div>

        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ stats.total }}</h4>
                    <small class="text-muted">إجمالي المستخدمين</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ stats.active }}</h4>
                    <small class="text-muted">مفعل</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-user-times fa-2x text-danger mb-2"></i>
                    <h4 class="mb-1">{{ stats.inactive }}</h4>
                    <small class="text-muted">معطل</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-chalkboard-teacher fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ stats.teachers }}</h4>
                    <small class="text-muted">أساتذة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-user-tie fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ stats.inspectors }}</h4>
                    <small class="text-muted">مفتشون</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center border-info">
                <div class="card-body">
                    <i class="fas fa-users-cog fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ stats.user_managers }}</h4>
                    <small class="text-muted">مديرو مستخدمين</small>
                </div>
            </div>
        </div>
        <!-- إحصائية المستخدمين الإداريين -->
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card border-0 shadow-sm text-center border-danger">
                <div class="card-body">
                    <i class="fas fa-crown fa-2x text-danger mb-2"></i>
                    <h4 class="mb-1">{{ stats.admins + stats.user_managers }}</h4>
                    <small class="text-muted">مستخدمون إداريون</small>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والفلترة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('users_list') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="search" name="search" value="{{ search }}"
                                placeholder="البحث...">
                            <label for="search">
                                <i class="fas fa-search me-1"></i>
                                البحث (اسم المستخدم أو رقم الهاتف)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="form-floating">
                            <select class="form-select" id="role" name="role">
                                <option value="">جميع الأدوار</option>
                                <option value="admin" {{ 'selected' if role_filter=='admin' }}>إدارة</option>
                                <option value="user_manager" {{ 'selected' if role_filter=='user_manager' }}>مدير
                                    مستخدمين</option>
                                <option value="inspector" {{ 'selected' if role_filter=='inspector' }}>مفتش</option>
                                <option value="teacher" {{ 'selected' if role_filter=='teacher' }}>أستاذ</option>
                            </select>
                            <label for="role">
                                <i class="fas fa-user-tag me-1"></i>
                                الدور
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="form-floating">
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" {{ 'selected' if status_filter=='active' }}>مفعل</option>
                                <option value="inactive" {{ 'selected' if status_filter=='inactive' }}>معطل</option>
                            </select>
                            <label for="status">
                                <i class="fas fa-toggle-on me-1"></i>
                                الحالة
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="form-floating">
                            <select class="form-select" id="wilaya" name="wilaya">
                                <option value="">جميع الولايات</option>
                                {% for wilaya in wilayas %}
                                <option value="{{ wilaya.code }}" {{ 'selected' if wilaya_filter==wilaya.code }}>
                                    {{ wilaya.code }} - {{ wilaya.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <label for="wilaya">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                الولاية
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <button type="submit" class="btn btn-primary w-100 h-100">
                            <i class="fas fa-filter me-1"></i>
                            تطبيق الفلتر
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المستخدمين -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <div id="users-loading" class="text-center py-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2 text-muted">جاري تحميل المستخدمين...</p>
            </div>

            <div id="users-container">
                {% if users.items %}
                <div class="table-responsive">
                    <table class="table table-hover" id="users-table">
                        <thead class="table-light">
                            <tr>
                                <th>المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>الولاية</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="users-tbody">
                            {% set previous_role_group = '' %}
                            {% for user in users.items %}

                            <!-- إضافة فاصل بين الإداريين والعاديين فقط -->
                            {% set current_role_group = 'admin' if user.role in ['admin', 'user_manager'] else 'others'
                            %}
                            {% if current_role_group == 'others' and previous_role_group == 'admin' %}
                            <tr class="table-secondary">
                                <td colspan="7" class="text-center py-3">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <hr class="flex-grow-1 me-3">
                                        <span class="fw-bold text-muted">
                                            <i class="fas fa-users me-2"></i>المستخدمون العاديون
                                        </span>
                                        <hr class="flex-grow-1 ms-3">
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                            {% set previous_role_group = current_role_group %}

                            <!-- إضافة عنوان للمجموعة الأولى -->
                            {% if loop.first and user.role in ['admin', 'user_manager'] %}
                            <tr class="table-primary">
                                <td colspan="7" class="text-center py-3">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <hr class="flex-grow-1 me-3">
                                        <span class="fw-bold text-primary">
                                            <i class="fas fa-crown me-2"></i>المستخدمون الإداريون
                                        </span>
                                        <hr class="flex-grow-1 ms-3">
                                    </div>
                                </td>
                            </tr>
                            {% endif %}

                            <tr class="{% if user.role in ['admin', 'user_manager'] %}table-light{% endif %}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if user.role == 'admin' %}
                                        <i class="fas fa-user-shield text-danger me-2"></i>
                                        {% elif user.role == 'user_manager' %}
                                        <i class="fas fa-users-cog text-info me-2"></i>
                                        {% elif user.role == 'inspector' %}
                                        <i class="fas fa-user-tie text-warning me-2"></i>
                                        {% elif user.role == 'teacher' %}
                                        <i class="fas fa-chalkboard-teacher text-success me-2"></i>
                                        {% endif %}
                                        <strong
                                            class="{% if user.role in ['admin', 'user_manager'] %}text-primary{% endif %}">
                                            {% if current_user.role == 'admin' or
                                            (current_user.role == 'user_manager' and user.role != 'admin') %}
                                            {{ user.username }}
                                            {% else %}
                                            {{ user.masked_username }}
                                            <i class="fas fa-shield-alt text-warning ms-1" title="بيانات محمية"></i>
                                            {% endif %}
                                            {% if user.role == 'admin' %}
                                            <span class="badge bg-danger ms-2 small">مدير النظام</span>
                                            {% elif user.role == 'user_manager' %}
                                            <span class="badge bg-info ms-2 small">مدير المستخدمين</span>
                                            {% endif %}
                                        </strong>
                                    </div>
                                </td>
                                <td>
                                    {% if current_user.role == 'admin' or
                                    (current_user.role == 'user_manager' and user.role != 'admin') %}
                                    {{ user.email }}
                                    {% else %}
                                    {{ user.masked_email }}
                                    <i class="fas fa-shield-alt text-warning ms-1" title="بيانات محمية"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.phone_number %}
                                    {% if current_user.role == 'admin' or
                                    (current_user.role == 'user_manager' and user.role != 'admin') %}
                                    <span class="phone-number">{{ user.formatted_phone }}</span>
                                    {% else %}
                                    <span class="phone-number">{{ user.masked_phone }}</span>
                                    <i class="fas fa-shield-alt text-warning ms-1" title="بيانات محمية"></i>
                                    {% endif %}
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.wilaya_code %}
                                    {{ user.wilaya_name }}
                                    {% else %}
                                    <span class="text-muted">غير محددة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge 
                                        {% if user.role == 'admin' %}bg-danger
                                        {% elif user.role == 'user_manager' %}bg-info
                                        {% elif user.role == 'inspector' %}bg-warning
                                        {% elif user.role == 'teacher' %}bg-success
                                        {% else %}bg-secondary{% endif %}">
                                        {% if user.role == 'admin' %}إدارة
                                        {% elif user.role == 'user_manager' %}مدير مستخدمين
                                        {% elif user.role == 'inspector' %}مفتش
                                        {% elif user.role == 'teacher' %}أستاذ
                                        {% else %}{{ user.role }}{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">مفعل</span>
                                    {% else %}
                                    <span class="badge bg-danger">معطل</span>
                                    {% endif %}
                                    <br>
                                    {% if user.is_online %}
                                    <small class="text-success">
                                        <i class="fas fa-circle text-success me-1" style="font-size: 0.6rem;"></i>
                                        متصل الآن
                                    </small>
                                    {% else %}
                                    <small class="text-muted">
                                        <i class="fas fa-circle text-muted me-1" style="font-size: 0.6rem;"></i>
                                        غير متصل
                                    </small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <!-- زر عرض الملف الشخصي مع قيود الوصول -->
                                        {% if current_user.role == 'admin' or
                                        (current_user.role == 'user_manager' and user.role != 'admin') %}
                                        <a href="{{ url_for('view_user_profile', user_id=user.id) }}"
                                            class="btn btn-outline-primary btn-sm" title="عرض الملف الشخصي">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% else %}
                                        <button class="btn btn-outline-secondary btn-sm" disabled
                                            title="غير مصرح بالوصول لملفات الأدمن">
                                            <i class="fas fa-eye-slash"></i>
                                        </button>
                                        {% endif %}
                                        {% if (user.role != 'admin') and ((current_user.role == 'admin') or
                                        (current_user.role == 'user_manager' and user.role in ['teacher', 'inspector']))
                                        %}
                                        {% if user.is_active %}
                                        <form method="POST"
                                            action="{{ url_for('user_manager_deactivate_user', user_id=user.id) }}"
                                            style="display: inline;">
                                            <button type="submit" class="btn btn-outline-danger btn-sm"
                                                title="تعطيل الحساب"
                                                onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')">
                                                <i class="fas fa-user-times"></i>
                                            </button>
                                        </form>
                                        {% else %}
                                        <form method="POST"
                                            action="{{ url_for('user_manager_activate_user', user_id=user.id) }}"
                                            style="display: inline;">
                                            <button type="submit" class="btn btn-outline-success btn-sm"
                                                title="تفعيل الحساب">
                                                <i class="fas fa-user-check"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                        {% endif %}

                                        <!-- زر تغيير كلمة المرور -->
                                        {% if (user.role != 'admin') and ((current_user.role == 'admin') or
                                        (current_user.role == 'user_manager' and user.role in ['teacher', 'inspector']))
                                        %}
                                        <button type="button" class="btn btn-outline-info btn-sm"
                                            title="تغيير كلمة المرور"
                                            onclick="openChangePasswordModal('{{ user.id }}', '{{ user.username }}')">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        {% endif %}

                                        <!-- زر تصفير التقدم للأساتذة فقط -->
                                        {% if user.role == 'teacher' and ((current_user.role == 'admin') or
                                        (current_user.role == 'user_manager')) %}
                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                            title="تصفير عدّاد التقدم" data-bs-toggle="modal"
                                            data-bs-target="#resetTeacherProgressModal" data-teacher-id="{{ user.id }}"
                                            data-teacher-name="{{ user.username }}">
                                            <i class="fas fa-trash-restore"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                {% if users.pages > 1 %}
                <nav aria-label="تنقل الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if users.has_prev %}
                        <li class="page-item">
                            <a class="page-link"
                                href="{{ url_for('users_list', page=users.prev_num, search=search, role=role_filter, wilaya=wilaya_filter, status=status_filter) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in users.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != users.page %}
                        <li class="page-item">
                            <a class="page-link"
                                href="{{ url_for('users_list', page=page_num, search=search, role=role_filter, wilaya=wilaya_filter, status=status_filter) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if users.has_next %}
                        <li class="page-item">
                            <a class="page-link"
                                href="{{ url_for('users_list', page=users.next_num, search=search, role=role_filter, wilaya=wilaya_filter, status=status_filter) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users-slash fa-4x text-muted mb-3"></i>
                    <h4>لا توجد نتائج</h4>
                    <p class="text-muted">لم يتم العثور على مستخدمين يطابقون معايير البحث</p>
                    <a href="{{ url_for('users_list') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-1"></i>
                        إعادة تعيين الفلتر
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal تأكيد تصفير تقدم الأستاذ -->
    <div class="modal fade" id="resetTeacherProgressModal" tabindex="-1"
        aria-labelledby="resetTeacherProgressModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="resetTeacherProgressModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>تأكيد تصفير عدّاد التقدم
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير!</h5>
                        <p class="mb-0">
                            أنت على وشك حذف <strong>جميع</strong> سجلات التقدم للأستاذ <strong
                                id="teacherNameDisplay"></strong>.
                            هذا الإجراء سيؤدي إلى:
                        </p>
                    </div>

                    <ul class="list-group list-group-flush mb-3">
                        <li class="list-group-item">
                            <i class="fas fa-times text-danger me-2"></i>
                            حذف جميع سجلات التقدم للأستاذ
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-times text-danger me-2"></i>
                            تصفير نسبة التقدم الإجمالية للأستاذ
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-times text-danger me-2"></i>
                            تصفير نسب التقدم التفصيلية لجميع المواد
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-times text-danger me-2"></i>
                            فقدان جميع البيانات التاريخية للتقدم
                        </li>
                    </ul>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> لن يتم حذف بيانات الأستاذ أو المناهج الدراسية، فقط سجلات التقدم.
                    </div>

                    <form id="resetTeacherProgressForm" action="" method="POST">
                        <input type="hidden" id="teacherIdInput" name="teacher_id" value="">

                        <div class="mb-3">
                            <label for="managerPassword" class="form-label">
                                <strong>أدخل كلمة مرور حسابك للتأكيد:</strong>
                            </label>
                            <input type="password" class="form-control" id="managerPassword" name="manager_password"
                                placeholder="كلمة مرور حسابك" required>
                            <div class="form-text">
                                {% if current_user.role == 'admin' %}
                                أدخل كلمة مرور حساب الأدمن
                                {% else %}
                                أدخل كلمة مرور حساب مدير المستخدمين
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmTeacherReset"
                                name="confirm_reset" required>
                            <label class="form-check-label" for="confirmTeacherReset">
                                <strong>أؤكد أنني أفهم عواقب هذا الإجراء وأريد المتابعة</strong>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="submit" form="resetTeacherProgressForm" class="btn btn-warning">
                        <i class="fas fa-trash-restore me-1"></i>تأكيد التصفير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث modal عند فتحه
        document.getElementById('resetTeacherProgressModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const teacherId = button.getAttribute('data-teacher-id');
            const teacherName = button.getAttribute('data-teacher-name');

            // تحديث النص والنموذج
            document.getElementById('teacherNameDisplay').textContent = teacherName;
            document.getElementById('teacherIdInput').value = teacherId;
            document.getElementById('resetTeacherProgressForm').action = '/admin/reset_teacher_progress/' + teacherId;

            // إعادة تعيين النموذج
            document.getElementById('managerPassword').value = '';
            document.getElementById('confirmTeacherReset').checked = false;
        });

        // تأكيد إضافي قبل الإرسال
        document.getElementById('resetTeacherProgressForm').addEventListener('submit', function (e) {
            const password = document.getElementById('managerPassword').value;
            const confirmed = document.getElementById('confirmTeacherReset').checked;
            const teacherName = document.getElementById('teacherNameDisplay').textContent;

            if (!password || !confirmed) {
                e.preventDefault();
                alert('يرجى إدخال كلمة المرور وتأكيد الموافقة');
                return;
            }

            const finalConfirm = confirm(`هل أنت متأكد تماماً من رغبتك في تصفير جميع سجلات التقدم للأستاذ "${teacherName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!`);
            if (!finalConfirm) {
                e.preventDefault();
            }
        });

        // تحديث عدد المستخدمين المتصلين كل 30 ثانية
        function updateOnlineUsersCount() {
            fetch('/api/online-users-count')
                .then(response => response.json())
                .then(data => {
                    if (data.count !== undefined) {
                        document.getElementById('online-users-count').textContent = data.count;
                    }
                })
                .catch(error => {
                    console.log('خطأ في تحديث عدد المستخدمين المتصلين:', error);
                });
        }

        // تحديث العدد كل 30 ثانية
        setInterval(updateOnlineUsersCount, 30000);

        // ===== Infinite Scroll للمستخدمين =====
        let currentPage = 1;
        let isLoading = false;
        let hasMoreUsers = true;
        const currentUserRole = '{{ current_user.role }}';

        // تحميل المستخدمين الأوليين
        loadUsers(1, true);

        function loadUsers(page, isInitial = false) {
            if (isLoading || (!hasMoreUsers && !isInitial)) return;

            isLoading = true;

            // إظهار مؤشر التحميل
            if (!isInitial) {
                document.getElementById('users-loading').style.display = 'block';
            }

            // بناء معاملات البحث
            const searchParams = new URLSearchParams();
            searchParams.append('page', page);

            // إضافة معاملات الفلترة الحالية
            const searchInput = document.querySelector('input[name="search"]');
            const roleSelect = document.querySelector('select[name="role"]');
            const wilayaSelect = document.querySelector('select[name="wilaya"]');
            const statusSelect = document.querySelector('select[name="status"]');

            if (searchInput && searchInput.value) searchParams.append('search', searchInput.value);
            if (roleSelect && roleSelect.value) searchParams.append('role', roleSelect.value);
            if (wilayaSelect && wilayaSelect.value) searchParams.append('wilaya', wilayaSelect.value);
            if (statusSelect && statusSelect.value) searchParams.append('status', statusSelect.value);

            fetch(`/api/users/load_more?${searchParams.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.users && data.users.length > 0) {
                        if (isInitial) {
                            // مسح الجدول وإضافة المستخدمين الجدد
                            document.getElementById('users-tbody').innerHTML = '';
                        }

                        appendUsersToTable(data.users, isInitial);
                        hasMoreUsers = data.has_next;
                        currentPage = data.current_page;
                    } else {
                        hasMoreUsers = false;
                        if (isInitial) {
                            showNoUsersMessage();
                        }
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل المستخدمين:', error);
                    if (isInitial) {
                        showErrorMessage();
                    }
                })
                .finally(() => {
                    isLoading = false;
                    document.getElementById('users-loading').style.display = 'none';
                });
        }

        function appendUsersToTable(users, isInitial = false) {
            const tbody = document.getElementById('users-tbody');
            let previousRoleGroup = '';

            // إذا كان التحميل الأولي، نحتاج لتتبع المجموعة السابقة
            if (!isInitial) {
                const lastRow = tbody.querySelector('tr:last-child');
                if (lastRow && lastRow.dataset.roleGroup) {
                    previousRoleGroup = lastRow.dataset.roleGroup;
                }
            }

            users.forEach((user, index) => {
                const currentRoleGroup = ['admin', 'user_manager'].includes(user.role) ? 'admin' : 'others';

                // إضافة فاصل بين المجموعات
                if (currentRoleGroup === 'others' && previousRoleGroup === 'admin') {
                    const separatorRow = createSeparatorRow('المستخدمون العاديون', 'fas fa-users', 'table-secondary text-muted');
                    tbody.appendChild(separatorRow);
                }

                // إضافة عنوان للمجموعة الأولى في التحميل الأولي
                if (isInitial && index === 0 && currentRoleGroup === 'admin') {
                    const headerRow = createSeparatorRow('المستخدمون الإداريون', 'fas fa-crown', 'table-primary text-primary');
                    tbody.appendChild(headerRow);
                }

                const userRow = createUserRow(user);
                userRow.dataset.roleGroup = currentRoleGroup;
                tbody.appendChild(userRow);

                previousRoleGroup = currentRoleGroup;
            });
        }

        function createSeparatorRow(title, icon, className) {
            const row = document.createElement('tr');
            row.className = className;
            row.innerHTML = `
                <td colspan="7" class="text-center py-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <hr class="flex-grow-1 me-3">
                        <span class="fw-bold">
                            <i class="${icon} me-2"></i>${title}
                        </span>
                        <hr class="flex-grow-1 ms-3">
                    </div>
                </td>
            `;
            return row;
        }

        function createUserRow(user) {
            const row = document.createElement('tr');
            if (['admin', 'user_manager'].includes(user.role)) {
                row.className = 'table-light';
            }

            // تحديد أيقونة الدور
            let roleIcon = '';
            let roleColor = '';
            switch (user.role) {
                case 'admin':
                    roleIcon = 'fas fa-user-shield';
                    roleColor = 'text-danger';
                    break;
                case 'user_manager':
                    roleIcon = 'fas fa-users-cog';
                    roleColor = 'text-info';
                    break;
                case 'inspector':
                    roleIcon = 'fas fa-user-tie';
                    roleColor = 'text-warning';
                    break;
                case 'teacher':
                    roleIcon = 'fas fa-chalkboard-teacher';
                    roleColor = 'text-success';
                    break;
            }

            // تحديد شارة الدور
            let roleBadge = '';
            if (user.role === 'admin') {
                roleBadge = '<span class="badge bg-danger ms-2 small">مدير النظام</span>';
            } else if (user.role === 'user_manager') {
                roleBadge = '<span class="badge bg-info ms-2 small">مدير المستخدمين</span>';
            }

            // تحديد شارة الحالة
            let statusBadge = '';
            let statusIcon = '';
            let onlineStatus = '';

            if (user.is_active) {
                statusBadge = '<span class="badge bg-success">مفعل</span>';
                statusIcon = '<i class="fas fa-circle text-success me-1"></i>';
            } else {
                statusBadge = '<span class="badge bg-danger">معطل</span>';
                statusIcon = '<i class="fas fa-circle text-danger me-1"></i>';
            }

            // تحديد حالة الاتصال
            if (user.is_online) {
                onlineStatus = '<br><small class="text-success"><i class="fas fa-circle text-success me-1" style="font-size: 0.6rem;"></i>متصل الآن</small>';
            } else {
                onlineStatus = '<br><small class="text-muted"><i class="fas fa-circle text-muted me-1" style="font-size: 0.6rem;"></i>غير متصل</small>';
            }

            // تحديد شارة الاشتراك
            let subscriptionBadge = '';
            let subscriptionColor = '';
            switch (user.subscription_type) {
                case 'unlimited':
                    subscriptionBadge = '<span class="badge bg-primary ms-1">غير محدود</span>';
                    break;
                case 'free_trial':
                    subscriptionBadge = `<span class="badge bg-info ms-1">تجريبي (${user.trial_days_left} أيام)</span>`;
                    break;
                case 'paid':
                    subscriptionBadge = '<span class="badge bg-success ms-1">مدفوع</span>';
                    break;
                case 'expired':
                    subscriptionBadge = '<span class="badge bg-warning ms-1">منتهي</span>';
                    break;
            }

            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        <i class="${roleIcon} ${roleColor} me-2"></i>
                        <strong class="${['admin', 'user_manager'].includes(user.role) ? 'text-primary' : ''}">
                            ${user.username}
                            ${roleBadge}
                        </strong>
                    </div>
                </td>
                <td>${user.email || 'غير محدد'}</td>
                <td>
                    ${user.phone_number ? `<span class="phone-number">${user.phone_number}</span>` : '<span class="text-muted">غير محدد</span>'}
                </td>
                <td>${user.wilaya_name}</td>
                <td>
                    <span class="badge ${getRoleBadgeClass(user.role)}">
                        ${user.role_display}
                    </span>
                </td>
                <td>
                    ${statusIcon}${statusBadge}${subscriptionBadge}${onlineStatus}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        ${createActionButtons(user)}
                    </div>
                </td>
            `;

            return row;
        }

        function createActionButtons(user) {
            let buttons = '';

            // زر عرض الملف الشخصي مع قيود الوصول
            if (currentUserRole === 'admin' || (currentUserRole === 'user_manager' && user.role !== 'admin')) {
                buttons += `
                    <a href="/profile/view/${user.id}" class="btn btn-outline-primary btn-sm" title="عرض الملف الشخصي">
                        <i class="fas fa-eye"></i>
                    </a>
                `;
            } else {
                buttons += `
                    <button class="btn btn-outline-secondary btn-sm" disabled title="غير مصرح بالوصول لملفات الأدمن">
                        <i class="fas fa-eye-slash"></i>
                    </button>
                `;
            }

            // أزرار التفعيل/التعطيل (للمستخدمين غير الأدمن فقط)
            if (user.role !== 'admin' && (currentUserRole === 'admin' ||
                (currentUserRole === 'user_manager' && ['teacher', 'inspector'].includes(user.role)))) {

                if (user.is_active) {
                    buttons += `
                        <button type="button" class="btn btn-outline-danger btn-sm" title="تعطيل الحساب"
                                onclick="deactivateUser(${user.id}, '${user.username}')">
                            <i class="fas fa-user-times"></i>
                        </button>
                    `;
                } else {
                    buttons += `
                        <button type="button" class="btn btn-outline-success btn-sm" title="تفعيل الحساب"
                                onclick="activateUser(${user.id}, '${user.username}')">
                            <i class="fas fa-user-check"></i>
                        </button>
                    `;
                }
            }

            // زر تصفير التقدم للأساتذة فقط
            if (user.role === 'teacher' && (currentUserRole === 'admin' || currentUserRole === 'user_manager')) {
                buttons += `
                    <button type="button" class="btn btn-outline-warning btn-sm" title="تصفير عدّاد التقدم"
                            data-bs-toggle="modal" data-bs-target="#resetTeacherProgressModal"
                            data-teacher-id="${user.id}" data-teacher-name="${user.username}">
                        <i class="fas fa-trash-restore"></i>
                    </button>
                `;
            }

            return buttons;
        }

        function getRoleBadgeClass(role) {
            switch (role) {
                case 'admin': return 'bg-danger';
                case 'user_manager': return 'bg-info';
                case 'inspector': return 'bg-warning';
                case 'teacher': return 'bg-success';
                default: return 'bg-secondary';
            }
        }

        function showNoUsersMessage() {
            const container = document.getElementById('users-container');
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مستخدمون</h5>
                    <p class="text-muted">لم يتم العثور على أي مستخدمين يطابقون معايير البحث.</p>
                </div>
            `;
        }

        function showErrorMessage() {
            const container = document.getElementById('users-container');
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">خطأ في التحميل</h5>
                    <p class="text-muted">حدث خطأ أثناء تحميل المستخدمين. يرجى المحاولة مرة أخرى.</p>
                    <button class="btn btn-primary" onclick="location.reload()">إعادة المحاولة</button>
                </div>
            `;
        }

        // مراقبة التمرير للتحميل التلقائي
        window.addEventListener('scroll', function () {
            if (isLoading || !hasMoreUsers) return;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // إذا وصل المستخدم إلى 80% من أسفل الصفحة
            if (scrollTop + windowHeight >= documentHeight * 0.8) {
                loadUsers(currentPage + 1);
            }
        });

        // إعادة تحميل المستخدمين عند تطبيق الفلتر
        document.querySelector('form').addEventListener('submit', function (e) {
            e.preventDefault();
            currentPage = 1;
            hasMoreUsers = true;
            loadUsers(1, true);
        });

        // ===== دوال الإجراءات =====

        function activateUser(userId, username) {
            if (confirm(`هل أنت متأكد من تفعيل حساب "${username}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/user-manager/activate-user/${userId}`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deactivateUser(userId, username) {
            if (confirm(`هل أنت متأكد من تعطيل حساب "${username}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/user-manager/deactivate-user/${userId}`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // دالة فتح مودال تغيير كلمة المرور
        function openChangePasswordModal(userId, username) {
            console.log('Opening change password modal for:', userId, username);

            // تعبئة البيانات
            document.getElementById('changePasswordUserId').value = userId;
            document.getElementById('changePasswordUsername').textContent = username;
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmNewPassword').value = '';

            // إعادة تعيين حالة الزر
            const submitBtn = document.getElementById('changePasswordSubmitBtn');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.classList.remove('btn-success');
                submitBtn.classList.add('btn-secondary');
            }

            // إعادة تعيين مؤشر التطابق
            const matchIndicator = document.getElementById('passwordMatchIndicator');
            if (matchIndicator) {
                matchIndicator.innerHTML = '<i class="fas fa-info-circle text-muted me-1"></i><small class="text-muted">أدخل كلمة المرور وتأكيدها</small>';
            }

            // فتح المودال
            const changePasswordModal = document.getElementById('changePasswordModal');
            if (changePasswordModal) {
                const modal = new bootstrap.Modal(changePasswordModal);
                modal.show();
            } else {
                alert('خطأ: لم يتم العثور على نافذة تغيير كلمة المرور');
            }
        }

        // جعل الدالة متاحة عالمياً
        window.openChangePasswordModal = openChangePasswordModal;

        // التحقق من تطابق كلمات المرور في مودال تغيير كلمة المرور
        function checkPasswordMatch() {
            const newPassword = document.getElementById('newPassword');
            const confirmPassword = document.getElementById('confirmNewPassword');
            const submitBtn = document.getElementById('changePasswordSubmitBtn');
            const matchIndicator = document.getElementById('passwordMatchIndicator');

            if (newPassword && confirmPassword && submitBtn && matchIndicator) {
                const password = newPassword.value;
                const confirm = confirmPassword.value;

                if (password.length >= 8 && confirm.length >= 8) {
                    if (password === confirm) {
                        matchIndicator.innerHTML = '<i class="fas fa-check text-success me-1"></i><small class="text-success">كلمات المرور متطابقة</small>';
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('btn-secondary');
                        submitBtn.classList.add('btn-success');
                    } else {
                        matchIndicator.innerHTML = '<i class="fas fa-times text-danger me-1"></i><small class="text-danger">كلمات المرور غير متطابقة</small>';
                        submitBtn.disabled = true;
                        submitBtn.classList.remove('btn-success');
                        submitBtn.classList.add('btn-secondary');
                    }
                } else {
                    matchIndicator.innerHTML = '<i class="fas fa-info-circle text-muted me-1"></i><small class="text-muted">كلمة المرور يجب أن تكون 8 أحرف على الأقل</small>';
                    submitBtn.disabled = true;
                    submitBtn.classList.remove('btn-success');
                    submitBtn.classList.add('btn-secondary');
                }
            }
        }

        // إضافة مستمعي الأحداث لحقول كلمة المرور
        const newPasswordField = document.getElementById('newPassword');
        const confirmPasswordField = document.getElementById('confirmNewPassword');

        if (newPasswordField) {
            newPasswordField.addEventListener('input', checkPasswordMatch);
        }

        if (confirmPasswordField) {
            confirmPasswordField.addEventListener('input', checkPasswordMatch);
        }

    </script>

    <!-- مودال تغيير كلمة المرور -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="changePasswordModalLabel">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="{{ url_for('admin_change_password') }}">
                    <div class="modal-body">
                        <input type="hidden" id="changePasswordUserId" name="user_id">

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تغيير كلمة المرور للمستخدم:</strong>
                            <span id="changePasswordUsername" class="fw-bold"></span>
                        </div>

                        <div class="mb-3">
                            <label for="newPassword" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                كلمة المرور الجديدة
                            </label>
                            <input type="password" class="form-control" id="newPassword" name="new_password"
                                   placeholder="أدخل كلمة المرور الجديدة" required minlength="8">
                            <div class="form-text">
                                <i class="fas fa-shield-alt me-1"></i>
                                يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، ورقم
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirmNewPassword" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                تأكيد كلمة المرور
                            </label>
                            <input type="password" class="form-control" id="confirmNewPassword" name="confirm_password"
                                   placeholder="أعد إدخال كلمة المرور" required minlength="8">
                        </div>

                        <div id="passwordMatchIndicator" class="text-center mb-3">
                            <i class="fas fa-info-circle text-muted me-1"></i>
                            <small class="text-muted">أدخل كلمة المرور وتأكيدها</small>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> سيتم تسجيل خروج المستخدم تلقائياً من جميع الأجهزة بعد تغيير كلمة المرور.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-secondary" id="changePasswordSubmitBtn" disabled>
                            <i class="fas fa-save me-1"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {% endblock %}