#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Ta9affi المحدث مع نظام Rate Limiting
"""

import os
import logging
from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_migrate import Migrate
from flask_login import LoginManager, login_required, current_user
from config import get_config
from models_new import db, User
import redis

# استيراد نظام Rate Limiting
from rate_limiter import init_rate_limiter, rate_limit, check_user_rate_limits, can_add_progress, can_delete_progress, record_add_progress, record_delete_progress
from rate_limit_monitor import rate_limit_bp, init_rate_limit_monitor, get_user_rate_limit_status

def create_app(config_name=None):
    """إنشاء وتكوين تطبيق Flask مع Rate Limiting"""
    
    app = Flask(__name__)
    
    # تحديد بيئة التشغيل
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # تحميل الإعدادات
    config_class = get_config()
    app.config.from_object(config_class)
    
    # تهيئة الإعدادات الخاصة بالتطبيق
    config_class.init_app(app)
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    # تهيئة Flask-Migrate
    migrate = Migrate(app, db)
    
    # تهيئة Redis
    redis_client = None
    try:
        redis_client = redis.from_url(app.config['REDIS_URL'])
        redis_client.ping()
        app.redis = redis_client
        app.logger.info("✅ تم الاتصال بـ Redis بنجاح")
    except Exception as e:
        app.logger.warning(f"⚠️ لم يتم الاتصال بـ Redis: {str(e)}")
        app.redis = None
    
    # تهيئة نظام Rate Limiting
    init_rate_limiter(redis_client)
    init_rate_limit_monitor(redis_client)
    
    # تهيئة نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # تسجيل Blueprints
    app.register_blueprint(rate_limit_bp)
    
    # تهيئة نظام التسجيل
    setup_logging(app)
    
    # إضافة دوال مساعدة للقوالب
    @app.context_processor
    def inject_rate_limit_status():
        """إضافة معلومات Rate Limiting للقوالب"""
        if current_user.is_authenticated:
            return {
                'user_rate_limits': get_user_rate_limit_status(current_user.id)
            }
        return {}
    
    # مسارات Rate Limiting للمستخدمين
    @app.route('/api/my-limits')
    @login_required
    def my_rate_limits():
        """API للحصول على حدود المستخدم الحالي"""
        limits = check_user_rate_limits(current_user.id)
        return jsonify({
            'success': True,
            'limits': limits,
            'user_id': current_user.id
        })
    
    # مسارات محمية بـ Rate Limiting
    @app.route('/add-progress', methods=['POST'])
    @login_required
    @rate_limit('add_progress')
    def add_progress():
        """إضافة تقدم جديد مع حماية Rate Limiting"""
        try:
            # فحص إضافي للتأكد
            can_add, info = can_add_progress(current_user.id)
            if not can_add:
                return jsonify({
                    'success': False,
                    'error': 'rate_limit_exceeded',
                    'message': 'تم تجاوز الحد الأقصى لإضافة التقدمات (10 كل 12 ساعة)',
                    'details': info
                }), 429
            
            # هنا يتم تنفيذ منطق إضافة التقدم
            # سيتم استبدال هذا بالكود الفعلي من التطبيق الأصلي
            
            # تسجيل العملية (سيتم تلقائياً بواسطة decorator)
            # record_add_progress(current_user.id)
            
            return jsonify({
                'success': True,
                'message': 'تم إضافة التقدم بنجاح',
                'remaining_adds': info.get('remaining_requests', 0)
            })
            
        except Exception as e:
            app.logger.error(f"خطأ في إضافة التقدم: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'server_error',
                'message': 'حدث خطأ في الخادم'
            }), 500
    
    @app.route('/delete-progress', methods=['POST'])
    @login_required
    @rate_limit('delete_progress')
    def delete_progress():
        """حذف تقدم مع حماية Rate Limiting"""
        try:
            # فحص إضافي للتأكد
            can_delete, info = can_delete_progress(current_user.id)
            if not can_delete:
                return jsonify({
                    'success': False,
                    'error': 'rate_limit_exceeded',
                    'message': 'تم تجاوز الحد الأقصى لحذف التقدمات (3 كل 12 ساعة)',
                    'details': info
                }), 429
            
            # هنا يتم تنفيذ منطق حذف التقدم
            # سيتم استبدال هذا بالكود الفعلي من التطبيق الأصلي
            
            # تسجيل العملية (سيتم تلقائياً بواسطة decorator)
            # record_delete_progress(current_user.id)
            
            return jsonify({
                'success': True,
                'message': 'تم حذف التقدم بنجاح',
                'remaining_deletes': info.get('remaining_requests', 0)
            })
            
        except Exception as e:
            app.logger.error(f"خطأ في حذف التقدم: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'server_error',
                'message': 'حدث خطأ في الخادم'
            }), 500
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        """لوحة التحكم مع معلومات Rate Limiting"""
        # الحصول على حالة Rate Limits للمستخدم
        user_limits = check_user_rate_limits(current_user.id)
        
        return render_template('dashboard.html', 
                             user_limits=user_limits,
                             user=current_user)
    
    # تسجيل معلومات التطبيق
    app.logger.info(f"🚀 تم تشغيل Ta9affi مع Rate Limiting في بيئة: {config_name}")
    app.logger.info(f"🗄️ قاعدة البيانات: {app.config['SQLALCHEMY_DATABASE_URI'][:50]}...")
    app.logger.info(f"🛡️ نظام Rate Limiting: {'مفعل' if redis_client else 'معطل (لا يوجد Redis)'}")
    
    return app

def setup_logging(app):
    """إعداد نظام التسجيل"""
    
    if not app.debug and not app.testing:
        # إنشاء مجلد السجلات
        log_dir = os.path.dirname(app.config.get('LOG_FILE', 'logs/ta9affi.log'))
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # إعداد معالج الملف
        file_handler = logging.FileHandler(app.config.get('LOG_FILE', 'logs/ta9affi.log'))
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.addHandler(file_handler)
        
        # إعداد معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s'
        ))
        console_handler.setLevel(logging.INFO)
        app.logger.addHandler(console_handler)
        
        app.logger.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.info('🔧 تم تهيئة نظام التسجيل')

# إنشاء التطبيق
app = create_app()

# استيراد النماذج والمسارات (بعد إنشاء التطبيق)
with app.app_context():
    # استيراد جميع النماذج للتأكد من تسجيلها مع SQLAlchemy
    from models_new import (
        User, Role, RoleSettings, EducationalLevel, Subject, Domain, 
        KnowledgeMaterial, Competency, Schedule, ProgressEntry, 
        LevelDatabase, LevelDataEntry, AdminInspectorNotification, 
        InspectorTeacherNotification, GeneralNotification, 
        GeneralNotificationRead, SubscriptionPlan, Subscription, 
        Payment, SubscriptionNotification, SubscriptionExtensionLog, 
        UserSession, inspector_teacher
    )
    from news_model import NewsUpdate

# دالة لدمج المسارات من التطبيق الأصلي
def integrate_original_routes():
    """دمج المسارات من التطبيق الأصلي مع إضافة Rate Limiting"""
    
    # هنا سيتم دمج جميع المسارات من app.py الأصلي
    # مع إضافة decorators Rate Limiting للمسارات المناسبة
    
    # مثال على كيفية إضافة Rate Limiting للمسارات الموجودة:
    
    # للمسارات التي تضيف تقدم:
    # @rate_limit('add_progress')
    
    # للمسارات التي تحذف تقدم:
    # @rate_limit('delete_progress')
    
    # للمسارات التي تتطلب تسجيل دخول:
    # @rate_limit('login_attempts', use_ip=True)
    
    pass

if __name__ == '__main__':
    with app.app_context():
        # إنشاء الجداول إذا لم تكن موجودة
        try:
            db.create_all()
            app.logger.info("✅ تم إنشاء/تحديث جداول قاعدة البيانات")
        except Exception as e:
            app.logger.error(f"❌ خطأ في إنشاء جداول قاعدة البيانات: {str(e)}")
    
    # تشغيل التطبيق
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=app.config.get('DEBUG', False)
    )
