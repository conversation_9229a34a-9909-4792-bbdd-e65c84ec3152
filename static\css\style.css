/* Custom styles for Ta9affi application */

/* Add some padding to the body for better spacing */
body {
    padding-bottom: 60px;
}

/* Custom card styling */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

/* Dashboard card styling */
.card-body .h3 {
    font-weight: bold;
    margin-bottom: 0;
}

/* Table styling */
.table th {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Form styling */
.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

/* Progress bar styling */
.progress {
    height: 0.8rem;
    border-radius: 0.25rem;
}

/* Footer styling */
footer {
    margin-top: 3rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* RTL specific adjustments */
.dropdown-menu-end {
    right: auto;
    left: 0;
}

.me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.me-md-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Custom colors */
.bg-primary {
    background-color: #1976d2 !important;
}

/* Animated Icons Styles */
.animated-icon {
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;
}

.animated-icon:hover {
    transform: scale(1.1);
    color: #1976d2 !important;
}

/* Float animation */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Pulse animation for important icons */
.pulse-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Bounce animation */
.bounce-icon {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Rotate animation */
.rotate-icon {
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Shake animation */
.shake-icon {
    animation: shake 0.5s;
    animation-iteration-count: infinite;
}

@keyframes shake {
    0% { transform: translate(1px, 1px) rotate(0deg); }
    10% { transform: translate(-1px, -2px) rotate(-1deg); }
    20% { transform: translate(-3px, 0px) rotate(1deg); }
    30% { transform: translate(3px, 2px) rotate(0deg); }
    40% { transform: translate(1px, -1px) rotate(1deg); }
    50% { transform: translate(-1px, 2px) rotate(-1deg); }
    60% { transform: translate(-3px, 1px) rotate(0deg); }
    70% { transform: translate(3px, 1px) rotate(-1deg); }
    80% { transform: translate(-1px, -1px) rotate(1deg); }
    90% { transform: translate(1px, 2px) rotate(0deg); }
    100% { transform: translate(1px, -2px) rotate(-1deg); }
}

/* Home card hover effects */
.home-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.home-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.home-icon {
    color: #1976d2;
    transition: all 0.3s ease;
}

/* Main image animation */
.main-image {
    animation: float 6s ease-in-out infinite;
}

/* Button hover effects */
.btn-hover-effect {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-hover-effect:hover::before {
    left: 100%;
}

/* Navbar animated icons */
.navbar .animated-icon {
    transition: all 0.3s ease;
}

.navbar .animated-icon:hover {
    transform: scale(1.1) rotate(5deg);
    color: #fff !important;
}

/* Card title animations */
.card-title {
    transition: all 0.3s ease;
}

.home-card:hover .card-title {
    color: #1976d2;
    transform: translateY(-2px);
}

/* Glow effect for important elements */
.glow-effect {
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(25, 118, 210, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(25, 118, 210, 0.6);
    }
}

/* Slide in animation for cards */
.slide-in {
    animation: slideIn 0.8s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fade in animation */
.fade-in {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Scale animation for buttons */
.scale-on-hover {
    transition: transform 0.3s ease;
}

.scale-on-hover:hover {
    transform: scale(1.05);
}

/* Color transition for text */
.color-transition {
    transition: color 0.3s ease;
}

.color-transition:hover {
    color: #1976d2 !important;
}

.btn-primary {
    background-color: #1976d2;
    border-color: #1976d2;
}

.btn-primary:hover {
    background-color: #1565c0;
    border-color: #1565c0;
}

.text-primary {
    color: #1976d2 !important;
}

/* Password toggle button styling */
.password-toggle-btn {
    background: none !important;
    border: none !important;
    color: #6c757d !important;
    cursor: pointer;
    transition: color 0.3s ease;
    z-index: 10;
}

.password-toggle-btn:hover {
    color: #1976d2 !important;
}

.password-toggle-btn:focus {
    outline: none;
    box-shadow: none;
}

/* Ensure password toggle button is properly positioned */
.form-floating .password-toggle-btn {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    background: transparent;
    border: none;
}

/* تأكد من أن الحقل يترك مساحة للزر */
.form-floating .form-control {
    padding-left: 45px !important;
}

/* Phone number styling - display left to right */
.phone-number {
    direction: ltr;
    text-align: left;
    display: inline-block;
}

/* Apply to all phone number displays */
[dir="ltr"] {
    direction: ltr;
    text-align: left;
}
