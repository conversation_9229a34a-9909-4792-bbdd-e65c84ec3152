# Ta9affi - دليل النشر والتحديث

## التحديث الأخير - إصلاح روابط Chargily ✅

تم تحديث جميع روابط Chargily لتعمل مع النطاق الصحيح `ta9affi.com`:

### الروابط المحدثة:
- ✅ **Success URL**: `http://ta9affi.com/payment/success`
- ✅ **Failure URL**: `http://ta9affi.com/payment/failure`  
- ✅ **Webhook URL**: `http://ta9affi.com/chargily-webhook`

### الملفات المحدثة:
- `app.py` - تحديث روابط success/failure
- `subscription_manager.py` - تحديث webhook URL
- `config_production.py` - تحديث إعدادات الإنتاج
- `dokploy.config.js` - تحديث متغيرات البيئة
- `.env.example` - تحديث المثال
- `.env.dokploy` - تحديث إعدادات dokploy

### صفحات جديدة:
- `templates/payment_success.html` - صفحة نجاح الدفع
- `templates/payment_failure.html` - صفحة فشل الدفع

## النشر على Dokploy

### 1. رفع الملفات إلى GitHub

```bash
git add .
git commit -m "إصلاح روابط Chargily وإضافة صفحات الدفع"
git push origin main
```

### 2. إعدادات Dokploy

في لوحة تحكم Dokploy:

#### أ) إعدادات التطبيق:
- **Repository**: رابط GitHub repository
- **Branch**: `main`
- **Build Command**: `docker build -t ta9affi .`
- **Start Command**: `gunicorn --bind 0.0.0.0:8000 app:app`

#### ب) إعدادات Domain:
```
Domain: ta9affi.com
Port: 8000
Path: /
HTTPS: ✓ مفعل
Certificate: Let's Encrypt
```

#### ج) متغيرات البيئة:
```env
FLASK_ENV=production
SECRET_KEY=ta9affi-production-secret-key-2024-very-secure
CHARGILY_PUBLIC_KEY=live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk
CHARGILY_SECRET_KEY=live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU
CHARGILY_WEBHOOK_URL=http://ta9affi.com/chargily-webhook
```

### 3. إعداد Chargily Dashboard

في لوحة تحكم Chargily:

1. **اذهب إلى Settings → Webhooks**
2. **أضف Webhook URL**: `http://ta9affi.com/chargily-webhook`
3. **فعّل Events**: `checkout.paid`, `checkout.failed`
4. **احفظ الإعدادات**

## اختبار النظام

### 1. اختبار الصفحات:
```bash
curl -I http://ta9affi.com/
curl -I http://ta9affi.com/health
curl -I http://ta9affi.com/subscription/plans
```

### 2. اختبار الدفع:
1. اذهب إلى `/subscription/plans`
2. اختر باقة
3. أكمل عملية الدفع
4. تحقق من التوجيه للصفحة الصحيحة

### 3. اختبار Webhook:
```bash
curl -X POST http://ta9affi.com/chargily-webhook \
  -H "Content-Type: application/json" \
  -d '{"checkout_id":"test_123","status":"paid"}'
```

## حل المشاكل الشائعة

### مشكلة HTTPS:
إذا لم يعمل HTTPS:
1. تحقق من إعدادات Domain في dokploy
2. تأكد أن Port = 8000
3. أعد إنشاء الشهادة

### مشكلة الدفع:
إذا فشل الدفع:
1. تحقق من logs التطبيق
2. تحقق من إعدادات Chargily
3. تأكد من صحة API Keys

### مشكلة Webhook:
إذا لم يعمل webhook:
1. تحقق من URL في Chargily dashboard
2. تحقق من logs الخادم
3. اختبر endpoint يدوياً

## الملفات المهمة للنشر

```
ta9affi/
├── app.py                    # التطبيق الرئيسي ✅
├── subscription_manager.py   # إدارة الدفع ✅
├── Dockerfile               # إعدادات Docker ✅
├── docker-compose.yml       # Docker Compose ✅
├── dokploy.config.js        # إعدادات Dokploy ✅
├── requirements.txt         # المتطلبات ✅
├── .env.dokploy            # متغيرات البيئة ✅
├── templates/
│   ├── payment_success.html ✅
│   └── payment_failure.html ✅
└── static/                  # الملفات الثابتة ✅
```

## خطوات النشر السريع

1. **رفع للـ GitHub**:
```bash
git add .
git commit -m "تحديث روابط Chargily"
git push
```

2. **في Dokploy**:
   - اذهب للتطبيق
   - اضغط "Deploy"
   - انتظر اكتمال البناء

3. **اختبار**:
   - افتح `http://ta9affi.com`
   - جرب عملية دفع
   - تحقق من الـ webhook

## الدعم

للحصول على المساعدة:
- تحقق من logs في dokploy
- راجع documentation Chargily
- تواصل مع الدعم الفني

---

**ملاحظة**: تأكد من تحديث webhook URL في Chargily dashboard بعد كل deployment جديد.
