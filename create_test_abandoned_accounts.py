#!/usr/bin/env python3
"""
سكريبت لإنشاء حسابات اختبار مهجورة لاختبار ميزة إدارة الحسابات المهجورة
"""

import sys
import os
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models_new import db, User, Role

def create_test_abandoned_accounts():
    """إنشاء حسابات اختبار مهجورة"""
    
    with app.app_context():
        print("إنشاء حسابات اختبار مهجورة...")
        print("=" * 50)
        
        # تواريخ مختلفة للاختبار
        now = datetime.utcnow()
        
        test_accounts = [
            {
                'username': 'abandoned_teacher_1',
                'email': '<EMAIL>',
                'password': 'Test123!',
                'role': Role.TEACHER,
                'phone_number': '**********',
                'wilaya_code': '01',
                'created_at': now - timedelta(days=300),  # 10 أشهر
                'last_login': None  # لم يسجل دخول أبداً
            },
            {
                'username': 'abandoned_teacher_2',
                'email': '<EMAIL>',
                'password': 'Test123!',
                'role': Role.TEACHER,
                'phone_number': '**********',
                'wilaya_code': '02',
                'created_at': now - timedelta(days=250),  # 8 أشهر
                'last_login': now - timedelta(days=200)  # آخر دخول قبل 7 أشهر
            },
            {
                'username': 'abandoned_inspector_1',
                'email': '<EMAIL>',
                'password': 'Test123!',
                'role': Role.INSPECTOR,
                'phone_number': '0555333333',
                'wilaya_code': '03',
                'created_at': now - timedelta(days=400),  # 13 شهر
                'last_login': now - timedelta(days=220)  # آخر دخول قبل 7 أشهر
            },
            {
                'username': 'old_inactive_teacher',
                'email': '<EMAIL>',
                'password': 'Test123!',
                'role': Role.TEACHER,
                'phone_number': '0555444444',
                'wilaya_code': '04',
                'created_at': now - timedelta(days=500),  # 16 شهر
                'last_login': None  # لم يسجل دخول أبداً
            },
            {
                'username': 'recent_active_teacher',
                'email': '<EMAIL>',
                'password': 'Test123!',
                'role': Role.TEACHER,
                'phone_number': '**********',
                'wilaya_code': '05',
                'created_at': now - timedelta(days=100),  # 3 أشهر
                'last_login': now - timedelta(days=30)  # آخر دخول قبل شهر (نشط)
            }
        ]
        
        created_count = 0
        
        for account_data in test_accounts:
            # التحقق من عدم وجود المستخدم
            existing_user = User.query.filter_by(username=account_data['username']).first()
            if existing_user:
                print(f"  - المستخدم {account_data['username']} موجود بالفعل")
                continue
            
            # إنشاء المستخدم
            user = User(
                username=account_data['username'],
                email=account_data['email'],
                password=generate_password_hash(account_data['password']),
                role=account_data['role'],
                phone_number=account_data['phone_number'],
                wilaya_code=account_data['wilaya_code'],
                created_at=account_data['created_at'],
                last_login=account_data['last_login'],
                _is_active=True
            )
            
            db.session.add(user)
            created_count += 1
            
            status = "لم يسجل دخول أبداً" if not account_data['last_login'] else f"آخر دخول: {account_data['last_login'].strftime('%Y-%m-%d')}"
            print(f"  + إنشاء {account_data['username']} ({account_data['role']}) - {status}")
        
        try:
            db.session.commit()
            print(f"\n✓ تم إنشاء {created_count} حساب اختبار")
            
            # عرض إحصائيات الحسابات المهجورة الجديدة
            print("\nإحصائيات الحسابات المهجورة بعد الإضافة:")
            print("=" * 50)
            
            abandoned_accounts = User.get_abandoned_accounts(days=180)
            print(f"إجمالي الحسابات المهجورة (أكثر من 6 أشهر): {len(abandoned_accounts)}")
            
            if abandoned_accounts:
                print("\nتفاصيل الحسابات المهجورة:")
                print("-" * 30)
                for user in abandoned_accounts:
                    days_inactive = user.days_since_last_login
                    status = "لم يسجل دخول أبداً" if not user.last_login or user.last_login == user.created_at else f"آخر دخول: {user.last_login.strftime('%Y-%m-%d')}"
                    print(f"{user.username} ({user.role}) - {days_inactive} يوم - {status}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"✗ حدث خطأ أثناء إنشاء الحسابات: {str(e)}")
            return False

if __name__ == '__main__':
    success = create_test_abandoned_accounts()
    if success:
        print("\n🎉 تم إنشاء حسابات الاختبار بنجاح!")
        print("يمكنك الآن اختبار ميزة إدارة الحسابات المهجورة.")
    else:
        print("\n❌ فشل في إنشاء حسابات الاختبار!")
        sys.exit(1)
