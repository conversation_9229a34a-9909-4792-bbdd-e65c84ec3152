@echo off
echo 🚀 Commit إصلاح مشكلة Chargily URLs - Ta9affi
echo ===============================================

echo.
echo 📋 إضافة الملفات المحدثة...
git add .

echo.
echo 💬 إنشاء commit...
git commit -m "🔧 إصلاح مشكلة Chargily URLs للبيئات المختلفة

✅ الإصلاحات المطبقة:
- إصلاح نظام تحديد البيئة في app.py
- جعل URLs ديناميكية في subscription_manager.py  
- إضافة متغيرات البيئة للـ URLs في config.py
- إنشاء .env.development للبيئة المحلية
- تحديث dokploy.config.js بمتغيرات البيئة المطلوبة

🎯 النتيجة:
- ✅ http://127.0.0.1:5000/subscription/plans (محلي)
- ✅ http://ta9affi.com/subscription/plans (إنتاج)

📁 ملفات جديدة:
- .env.development: إعدادات البيئة المحلية
- test_chargily_config.py: اختبار الإعدادات
- run_local_with_env.py: تشغيل محلي محسن
- CHARGILY_ENVIRONMENT_FIX.md: دليل الإصلاح
- update_production_env.sh: سكريبت تحديث الإنتاج

🔧 ملفات محدثة:
- app.py: نظام تحديد البيئة الصحيح
- subscription_manager.py: URLs ديناميكية
- config.py: إعدادات Chargily للبيئات
- dokploy.config.js: متغيرات البيئة المحدثة"

echo.
echo ⬆️ رفع إلى GitHub...
git push origin main

echo.
echo ✅ تم رفع التحديثات بنجاح!
echo.
echo 📋 الخطوات التالية:
echo 1. اذهب إلى Dokploy dashboard
echo 2. اضغط Deploy للتطبيق
echo 3. اختبر عملية الدفع على http://ta9affi.com/subscription/plans
echo 4. تحقق من أن URLs الدفع تعمل بشكل صحيح
echo.
echo 🧪 للاختبار المحلي:
echo python test_chargily_config.py
echo python run_local_with_env.py
echo.
echo 🔗 الروابط المحدثة:
echo - محلي: http://127.0.0.1:5000/subscription/plans
echo - إنتاج: http://ta9affi.com/subscription/plans
echo.
pause
