{% extends 'base.html' %}

{% block extra_css %}
<style>
    .reset-password-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .reset-password-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 500px;
        width: 100%;
        padding: 40px;
        text-align: center;
    }

    .reset-password-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 30px;
        color: white;
        font-size: 2rem;
    }

    .btn-success-gradient {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 50px;
        padding: 12px 30px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    }

    .btn-success-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
        background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    }

    .form-floating > .form-control {
        border-radius: 15px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-floating > .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .password-toggle-btn {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 5px;
        border-radius: 3px;
        transition: color 0.3s ease;
        z-index: 10;
    }

    .password-toggle-btn:hover {
        color: #495057;
        background-color: rgba(0,0,0,0.05);
    }

    .password-strength {
        margin-top: 10px;
        text-align: right;
    }

    .strength-bar {
        height: 4px;
        border-radius: 2px;
        background-color: #e9ecef;
        margin-top: 5px;
        overflow: hidden;
    }

    .strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .strength-weak { background-color: #dc3545; width: 25%; }
    .strength-fair { background-color: #ffc107; width: 50%; }
    .strength-good { background-color: #17a2b8; width: 75%; }
    .strength-strong { background-color: #28a745; width: 100%; }

    .back-to-login {
        color: #28a745;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .back-to-login:hover {
        color: #20c997;
        text-decoration: underline;
    }

    .alert {
        border-radius: 15px;
        border: none;
        margin-bottom: 25px;
    }

    .requirements-list {
        text-align: right;
        margin-top: 10px;
    }

    .requirement {
        font-size: 0.85rem;
        margin-bottom: 5px;
        transition: color 0.3s ease;
    }

    .requirement.met {
        color: #28a745;
    }

    .requirement.unmet {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block title %}إعادة تعيين كلمة المرور - Ta9affi{% endblock %}

{% block content %}
<div class="reset-password-container">
    <div class="reset-password-card">
        <div class="reset-password-icon">
            <i class="fas fa-lock"></i>
        </div>
        
        <h2 class="mb-4 text-dark">إعادة تعيين كلمة المرور</h2>
        
        <p class="text-muted mb-4">
            أدخل كلمة المرور الجديدة لحسابك
        </p>

        <!-- عرض الرسائل -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' or category == 'danger' else 'info-circle' if category == 'info' else 'check-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" id="resetPasswordForm">
            <input type="hidden" name="token" value="{{ token }}">
            
            <div class="form-floating mb-3 position-relative">
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="كلمة المرور الجديدة" required>
                <label for="password">
                    <i class="fas fa-lock me-2"></i>
                    كلمة المرور الجديدة
                </label>
                <button type="button" class="password-toggle-btn" id="togglePassword">
                    <i class="fas fa-eye" id="togglePasswordIcon"></i>
                </button>
            </div>

            <div class="password-strength">
                <div class="strength-bar">
                    <div class="strength-fill" id="strengthFill"></div>
                </div>
                <small id="strengthText" class="text-muted">قوة كلمة المرور</small>
            </div>

            <div class="requirements-list">
                <div class="requirement unmet" id="req-length">
                    <i class="fas fa-times me-1"></i>
                    8 أحرف على الأقل
                </div>
                <div class="requirement unmet" id="req-uppercase">
                    <i class="fas fa-times me-1"></i>
                    حرف كبير واحد على الأقل
                </div>
                <div class="requirement unmet" id="req-lowercase">
                    <i class="fas fa-times me-1"></i>
                    حرف صغير واحد على الأقل
                </div>
                <div class="requirement unmet" id="req-number">
                    <i class="fas fa-times me-1"></i>
                    رقم واحد على الأقل
                </div>
            </div>

            <div class="form-floating mb-4 position-relative">
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                       placeholder="تأكيد كلمة المرور" required>
                <label for="confirm_password">
                    <i class="fas fa-lock me-2"></i>
                    تأكيد كلمة المرور
                </label>
                <button type="button" class="password-toggle-btn" id="toggleConfirmPassword">
                    <i class="fas fa-eye" id="toggleConfirmPasswordIcon"></i>
                </button>
            </div>

            <div id="passwordMatch" class="text-center mb-3" style="display: none;">
                <small class="text-success">
                    <i class="fas fa-check me-1"></i>
                    كلمات المرور متطابقة
                </small>
            </div>

            <div id="passwordMismatch" class="text-center mb-3" style="display: none;">
                <small class="text-danger">
                    <i class="fas fa-times me-1"></i>
                    كلمات المرور غير متطابقة
                </small>
            </div>

            <div class="d-grid mb-4">
                <button type="submit" class="btn btn-success-gradient btn-lg" id="submitBtn" disabled>
                    <span id="submitBtnText">
                        <i class="fas fa-save me-2"></i>
                        حفظ كلمة المرور الجديدة
                    </span>
                    <span id="submitBtnLoading" class="d-none">
                        <span class="spinner-border spinner-border-sm me-2"></span>
                        جاري الحفظ...
                    </span>
                </button>
            </div>
        </form>

        <div class="text-center">
            <a href="{{ url_for('login') }}" class="back-to-login">
                <i class="fas fa-arrow-right me-2"></i>
                العودة لتسجيل الدخول
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('resetPasswordForm');

    // تبديل إظهار/إخفاء كلمة المرور
    function setupPasswordToggle(fieldId, toggleId, iconId) {
        const field = document.getElementById(fieldId);
        const toggle = document.getElementById(toggleId);
        const icon = document.getElementById(iconId);

        toggle.addEventListener('click', function() {
            const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
            field.setAttribute('type', type);
            
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }

    setupPasswordToggle('password', 'togglePassword', 'togglePasswordIcon');
    setupPasswordToggle('confirm_password', 'toggleConfirmPassword', 'toggleConfirmPasswordIcon');

    // فحص قوة كلمة المرور
    function checkPasswordStrength(password) {
        const requirements = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password)
        };

        // تحديث المتطلبات
        Object.keys(requirements).forEach(req => {
            const element = document.getElementById(`req-${req}`);
            const icon = element.querySelector('i');
            
            if (requirements[req]) {
                element.classList.remove('unmet');
                element.classList.add('met');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-check');
            } else {
                element.classList.remove('met');
                element.classList.add('unmet');
                icon.classList.remove('fa-check');
                icon.classList.add('fa-times');
            }
        });

        // حساب القوة
        const metCount = Object.values(requirements).filter(Boolean).length;
        const strengthFill = document.getElementById('strengthFill');
        const strengthText = document.getElementById('strengthText');

        strengthFill.className = 'strength-fill';
        
        if (metCount === 0) {
            strengthText.textContent = 'قوة كلمة المرور';
            strengthFill.style.width = '0%';
        } else if (metCount === 1) {
            strengthFill.classList.add('strength-weak');
            strengthText.textContent = 'ضعيفة';
        } else if (metCount === 2) {
            strengthFill.classList.add('strength-fair');
            strengthText.textContent = 'متوسطة';
        } else if (metCount === 3) {
            strengthFill.classList.add('strength-good');
            strengthText.textContent = 'جيدة';
        } else {
            strengthFill.classList.add('strength-strong');
            strengthText.textContent = 'قوية';
        }

        return metCount === 4;
    }

    // فحص تطابق كلمات المرور
    function checkPasswordMatch() {
        const password = passwordField.value;
        const confirmPassword = confirmPasswordField.value;
        const matchDiv = document.getElementById('passwordMatch');
        const mismatchDiv = document.getElementById('passwordMismatch');

        if (confirmPassword === '') {
            matchDiv.style.display = 'none';
            mismatchDiv.style.display = 'none';
            return false;
        }

        if (password === confirmPassword) {
            matchDiv.style.display = 'block';
            mismatchDiv.style.display = 'none';
            return true;
        } else {
            matchDiv.style.display = 'none';
            mismatchDiv.style.display = 'block';
            return false;
        }
    }

    // تحديث حالة الزر
    function updateSubmitButton() {
        const isPasswordStrong = checkPasswordStrength(passwordField.value);
        const isPasswordMatch = checkPasswordMatch();
        
        submitBtn.disabled = !(isPasswordStrong && isPasswordMatch);
    }

    // مراقبة التغييرات
    passwordField.addEventListener('input', updateSubmitButton);
    confirmPasswordField.addEventListener('input', updateSubmitButton);

    // معالجة إرسال النموذج
    form.addEventListener('submit', function(e) {
        const submitBtnText = document.getElementById('submitBtnText');
        const submitBtnLoading = document.getElementById('submitBtnLoading');
        
        submitBtn.disabled = true;
        submitBtnText.classList.add('d-none');
        submitBtnLoading.classList.remove('d-none');
    });
});
</script>
{% endblock %}
